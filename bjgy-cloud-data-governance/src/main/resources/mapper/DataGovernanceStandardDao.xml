<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bjgy.dao.DataGovernanceStandardDao">


    <update id="updateCodeNumByStandardId">
        UPDATE data_governance_standard SET code_num=(SELECT COUNT(1) FROM data_governance_standard_code WHERE standard_id=#{standardId} AND deleted=0) WHERE id=#{standardId}
    </update>
</mapper>
