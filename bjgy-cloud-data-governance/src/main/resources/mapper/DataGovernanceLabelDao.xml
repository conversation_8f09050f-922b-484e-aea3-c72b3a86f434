<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjgy.dao.DataGovernanceLabelDao">
    <resultMap type="com.bjgy.vo.DataGovernanceLabelVO" id="Result">
        <id     property="id"   column="id"   />
        <result property="categoryId" column="category_id" />
        <result property="labelModelId"   column="label_model_id"   />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="conditionConfig" column="condition_config" jdbcType="OTHER" javaType="java.util.List" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="color"   column="color"   />
        <result property="orgId"   column="org_id"   />
        <result property="projectId" column="project_id" />
        <result property="status" column="status" />
        <result property="orderNo"   column="order_no"   />
        <result property="deleted" column="deleted" />
        <result property="version" column="version" />
        <result property="creator"   column="creator"   />
        <result property="createTime" column="create_time" />
        <result property="updater"   column="updater"   />
        <result property="updateTime" column="update_time" />
        <result property="categoryName"   column="categoryName"   />
        <result property="labelModelName" column="labelModelName" />
        <result property="labelModelType" column="labelModelType" />
    </resultMap>
    <select id="queryByPage" resultMap="Result">
        SELECT
        label.*,
        category.`name` AS categoryName,
        model.`name` AS labelModelName,
        model.`type` AS labelModelType
        FROM
        `data_governance_label` label
        LEFT JOIN data_governance_label_category category ON category.id = label.category_id
        LEFT JOIN data_governance_label_model model ON model.id = label.label_model_id
        <if test="_parameter.containsKey('ew') and ew!= null">
            ${ew.customSqlSegment}
        </if>
    </select>
</mapper>
