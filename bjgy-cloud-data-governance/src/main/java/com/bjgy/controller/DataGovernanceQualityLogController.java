package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceQualityLogConvert;
import com.bjgy.entity.DataGovernanceQualityLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataGovernanceQualityLogService;
import com.bjgy.query.DataGovernanceQualityLogQuery;
import com.bjgy.vo.DataGovernanceQualityLogVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 质量规则-预警日志
*/
@RestController
@RequestMapping("/quality-log")
@Tag(name="质量规则-预警日志")
@AllArgsConstructor
public class DataGovernanceQualityLogController {
    private final DataGovernanceQualityLogService dataGovernanceQualityLogService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataGovernanceQualityLogVO>> page(@Valid DataGovernanceQualityLogQuery query){
        PageResult<DataGovernanceQualityLogVO> page = dataGovernanceQualityLogService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataGovernanceQualityLogVO> get(@PathVariable("id") Long id){
        DataGovernanceQualityLogEntity entity = dataGovernanceQualityLogService.getById(id);

        return Result.ok(DataGovernanceQualityLogConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataGovernanceQualityLogVO vo){
        dataGovernanceQualityLogService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataGovernanceQualityLogVO vo){
        dataGovernanceQualityLogService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataGovernanceQualityLogService.delete(idList);

        return Result.ok();
    }
}
