package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushConfigConvert;
import com.bjgy.entity.DataPushConfigEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataPushConfigQuery;
import com.bjgy.service.DataPushConfigService;
import com.bjgy.vo.DataPushConfigVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-数据推送配置
 */
@RestController
@RequestMapping("/dataPushConfig")
@Tag(name = "数据治理-数据推送配置")
@AllArgsConstructor
public class DataPushConfigController {
	private final DataPushConfigService dataPushConfigService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataPushConfigVO>> page(@Valid DataPushConfigQuery query) {
		PageResult<DataPushConfigVO> page = dataPushConfigService.page(query);
		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataPushConfigVO> get(@PathVariable("id") Long id) {
		DataPushConfigEntity entity = dataPushConfigService.getById(id);
		return Result.ok(DataPushConfigConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataPushConfigVO vo) {
		dataPushConfigService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataPushConfigVO vo) {
		dataPushConfigService.update(vo);

		return Result.ok();
	}

	@DeleteMapping("/{id}")
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> ids) {
		dataPushConfigService.delete(ids.get(0));
		return Result.ok();
	}
}
