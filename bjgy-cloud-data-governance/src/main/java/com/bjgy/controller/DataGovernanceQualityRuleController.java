package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceQualityRuleConvert;
import com.bjgy.entity.DataGovernanceQualityRuleEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataGovernanceQualityRuleQuery;
import com.bjgy.service.DataGovernanceQualityRuleService;
import com.bjgy.vo.DataGovernanceQualityRuleVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-质量规则
 */
@RestController
@RequestMapping("/quality-rule")
@Tag(name = "数据治理-质量规则")
@AllArgsConstructor
public class DataGovernanceQualityRuleController {
	private final DataGovernanceQualityRuleService dataGovernanceQualityRuleService;


	@GetMapping("list")
	@Operation(summary = "查询列表")
	public Result<List<DataGovernanceQualityRuleVO>> list() {
		List<DataGovernanceQualityRuleEntity> list = dataGovernanceQualityRuleService.list();

		return Result.ok(DataGovernanceQualityRuleConvert.INSTANCE.convertList(list));
	}

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataGovernanceQualityRuleVO>> page(@Valid DataGovernanceQualityRuleQuery query) {
		PageResult<DataGovernanceQualityRuleVO> page = dataGovernanceQualityRuleService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataGovernanceQualityRuleVO> get(@PathVariable("id") Long id) {
		DataGovernanceQualityRuleEntity entity = dataGovernanceQualityRuleService.getById(id);

		return Result.ok(DataGovernanceQualityRuleConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataGovernanceQualityRuleVO vo) {
		dataGovernanceQualityRuleService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataGovernanceQualityRuleVO vo) {
		dataGovernanceQualityRuleService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataGovernanceQualityRuleService.delete(idList);

		return Result.ok();
	}
}
