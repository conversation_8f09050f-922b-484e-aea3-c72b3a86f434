package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceMetamodelPropertyConvert;
import com.bjgy.entity.DataGovernanceMetamodelPropertyEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataGovernanceMetamodelPropertyQuery;
import com.bjgy.service.DataGovernanceMetamodelPropertyService;
import com.bjgy.vo.DataGovernanceMetamodelPropertyVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-元模型属性
 */
@RestController
@RequestMapping("metamodel-property")
@Tag(name = "数据治理-元模型属性")
@AllArgsConstructor
public class DataGovernanceMetamodelPropertyController {
	private final DataGovernanceMetamodelPropertyService dataGovernanceMetamodelPropertyService;


	@GetMapping("/properties/{metaModelId}")
	@Operation(summary = "根据id获取属性列表")
	public Result<List<DataGovernanceMetamodelPropertyVO>> properties(@PathVariable Long metaModelId) {
		List<DataGovernanceMetamodelPropertyVO> properties = dataGovernanceMetamodelPropertyService.properties(metaModelId);

		return Result.ok(properties);
	}

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataGovernanceMetamodelPropertyVO>> page(@Valid DataGovernanceMetamodelPropertyQuery query) {
		PageResult<DataGovernanceMetamodelPropertyVO> page = dataGovernanceMetamodelPropertyService.page(query);
		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataGovernanceMetamodelPropertyVO> get(@PathVariable("id") Long id) {
		DataGovernanceMetamodelPropertyEntity entity = dataGovernanceMetamodelPropertyService.getById(id);

		return Result.ok(DataGovernanceMetamodelPropertyConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataGovernanceMetamodelPropertyVO vo) {
		dataGovernanceMetamodelPropertyService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataGovernanceMetamodelPropertyVO vo) {
		dataGovernanceMetamodelPropertyService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataGovernanceMetamodelPropertyService.delete(idList);

		return Result.ok();
	}
}
