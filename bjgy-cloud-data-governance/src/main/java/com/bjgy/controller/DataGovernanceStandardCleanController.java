package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.data.governance.dto.CleanConfig;
import com.bjgy.convert.DataGovernanceStandardCleanConvert;
import com.bjgy.entity.DataGovernanceStandardCleanEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataGovernanceStandardCleanService;
import com.bjgy.query.DataGovernanceStandardCleanQuery;
import com.bjgy.vo.DataGovernanceMetadataStandardRelVO;
import com.bjgy.vo.DataGovernanceStandardCleanVO;
import com.bjgy.vo.DataGovernanceStandardCodeVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-标准清洗
 */
@RestController
@RequestMapping("/standard-clean")
@Tag(name = "数据治理-标准清洗")
@AllArgsConstructor
public class DataGovernanceStandardCleanController {
	private final DataGovernanceStandardCleanService dataGovernanceStandardCleanService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataGovernanceStandardCleanVO>> page(@Valid DataGovernanceStandardCleanQuery query) {
		PageResult<DataGovernanceStandardCleanVO> page = dataGovernanceStandardCleanService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataGovernanceStandardCleanVO> get(@PathVariable("id") Long id) {
		DataGovernanceStandardCleanEntity entity = dataGovernanceStandardCleanService.getById(id);

		return Result.ok(DataGovernanceStandardCleanConvert.INSTANCE.convert(entity));
	}

	@GetMapping("metadata/{metadataId}")
	@Operation(summary = "根据元数据id获取清洗配置")
	public Result<DataGovernanceStandardCleanVO> getByMetadataId(@PathVariable("metadataId") Long metadataId) {
		DataGovernanceStandardCleanEntity entity = dataGovernanceStandardCleanService.getByMetadataId(metadataId);

		return Result.ok(DataGovernanceStandardCleanConvert.INSTANCE.convert(entity));
	}

	@GetMapping("meta-standard-rel/{metadataId}")
	@Operation(summary = "根据元数据id获取标准关联")
	public Result<DataGovernanceMetadataStandardRelVO> getMetadataStandardRel(@PathVariable("metadataId") Long metadataId) {
		DataGovernanceMetadataStandardRelVO metadataStandardRel = dataGovernanceStandardCleanService.getMetadataStandardRel(metadataId);

		return Result.ok(metadataStandardRel);
	}

	@GetMapping("metadata-column-type/{metadataId}")
	@Operation(summary = "根据元数据id获取字段的值列表")
	public Result<String> getMetadataColumnType(@PathVariable("metadataId") Long metadataId) {
		String classType = dataGovernanceStandardCleanService.getMetadataColumnType(metadataId);
		return Result.ok(classType);
	}

	@GetMapping("standard-code/{metadataId}")
	@Operation(summary = "根据元数据id获取标准数据列表")
	public Result<List<DataGovernanceStandardCodeVO>> listStandardCode(@PathVariable("metadataId") Long metadataId) {
		List<DataGovernanceStandardCodeVO> standardCode = dataGovernanceStandardCleanService.listStandardCode(metadataId);

		return Result.ok(standardCode);
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<DataGovernanceStandardCleanVO> save(@RequestBody DataGovernanceStandardCleanVO vo) {
		return Result.ok(dataGovernanceStandardCleanService.save(vo));
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<DataGovernanceStandardCleanVO> update(@RequestBody @Valid DataGovernanceStandardCleanVO vo) {
		return Result.ok(dataGovernanceStandardCleanService.update(vo));
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataGovernanceStandardCleanService.delete(idList);

		return Result.ok();
	}

	@PostMapping("release/{id}")
	@Operation(summary = "发布")
	public Result<DataGovernanceStandardCleanVO> release(@PathVariable Long id) {
		return Result.ok(dataGovernanceStandardCleanService.release(id));
	}

	@PostMapping("cancel/{id}")
	@Operation(summary = "取消发布")
	public Result<DataGovernanceStandardCleanVO> cancel(@PathVariable Long id) {
		return Result.ok(dataGovernanceStandardCleanService.cancel(id));
	}

	@PostMapping("/hand-run/{id}")
	@Operation(summary = "手动执行")
	public Result<String> handRun(@PathVariable Long id) {
		dataGovernanceStandardCleanService.handRun(id);
		return Result.ok();
	}
}
