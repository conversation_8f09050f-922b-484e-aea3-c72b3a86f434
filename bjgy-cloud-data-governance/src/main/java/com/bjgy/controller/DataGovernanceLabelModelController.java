package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelModelConvert;
import com.bjgy.dto.CheckSqlRequest;
import com.bjgy.dto.CheckSqlResult;
import com.bjgy.entity.DataGovernanceLabelModelEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataGovernanceLabelModelService;
import com.bjgy.query.DataGovernanceLabelModelQuery;
import com.bjgy.vo.DataGovernanceLabelModelVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-标签实体
*/
@RestController
@RequestMapping("/label-model")
@Tag(name="数据治理-标签实体")
@AllArgsConstructor
public class DataGovernanceLabelModelController {
    private final DataGovernanceLabelModelService dataGovernanceLabelModelService;

	@GetMapping("list")
	@Operation(summary = "获取列表")
	public Result<List<DataGovernanceLabelModelVO>> list(){
		List<DataGovernanceLabelModelEntity> entities = dataGovernanceLabelModelService.listAll();
		return Result.ok(DataGovernanceLabelModelConvert.INSTANCE.convertList(entities));
	}

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('data-governance:label-model:page')")
    public Result<PageResult<DataGovernanceLabelModelVO>> page(@Valid DataGovernanceLabelModelQuery query){
        PageResult<DataGovernanceLabelModelVO> page = dataGovernanceLabelModelService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    @PreAuthorize("hasAuthority('data-governance:label-model:info')")
    public Result<DataGovernanceLabelModelVO> get(@PathVariable("id") Long id){
        DataGovernanceLabelModelEntity entity = dataGovernanceLabelModelService.getById(id);

        return Result.ok(DataGovernanceLabelModelConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    @PreAuthorize("hasAuthority('data-governance:label-model:save')")
    public Result<String> save(@RequestBody DataGovernanceLabelModelVO vo){
        dataGovernanceLabelModelService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('data-governance:label-model:update')")
    public Result<String> update(@RequestBody @Valid DataGovernanceLabelModelVO vo){
        dataGovernanceLabelModelService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('data-governance:label-model:delete')")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataGovernanceLabelModelService.delete(idList);

        return Result.ok();
    }

	@PostMapping("check-sql")
	@Operation(summary = "检查sql")
	public Result<CheckSqlResult> checkSql(@RequestBody @Valid CheckSqlRequest checkSqlRequest){
		return Result.ok(dataGovernanceLabelModelService.checkSql(checkSqlRequest));
	}

	@GetMapping("list-column/{id}")
	@Operation(summary = "根据modelId获取字段列表")
	public Result<List<ColumnDescription>> listColumn(@PathVariable Long id){
		return Result.ok(dataGovernanceLabelModelService.listColumn(id));
	}
}
