package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceStandardCleanLogConvert;
import com.bjgy.entity.DataGovernanceStandardCleanLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataGovernanceStandardCleanLogService;
import com.bjgy.query.DataGovernanceStandardCleanLogQuery;
import com.bjgy.vo.DataGovernanceStandardCleanLogVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-标准清洗日志
*/
@RestController
@RequestMapping("/standard-clean-log")
@Tag(name="数据治理-标准清洗日志")
@AllArgsConstructor
public class DataGovernanceStandardCleanLogController {
    private final DataGovernanceStandardCleanLogService dataGovernanceStandardCleanLogService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataGovernanceStandardCleanLogVO>> page(@Valid DataGovernanceStandardCleanLogQuery query){
        PageResult<DataGovernanceStandardCleanLogVO> page = dataGovernanceStandardCleanLogService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataGovernanceStandardCleanLogVO> get(@PathVariable("id") Long id){
        DataGovernanceStandardCleanLogEntity entity = dataGovernanceStandardCleanLogService.getById(id);

        return Result.ok(DataGovernanceStandardCleanLogConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataGovernanceStandardCleanLogVO vo){
        dataGovernanceStandardCleanLogService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataGovernanceStandardCleanLogVO vo){
        dataGovernanceStandardCleanLogService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataGovernanceStandardCleanLogService.delete(idList);

        return Result.ok();
    }
}
