package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushTaskConvert;
import com.bjgy.entity.DataPushTaskEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataPushTaskQuery;
import com.bjgy.service.DataPushTaskService;
import com.bjgy.vo.DataPushTaskVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-数据推送任务
*/
@RestController
@RequestMapping("/dataPushTask")
@Tag(name="数据治理-数据推送任务")
@AllArgsConstructor
public class DataPushTaskController {
    private final DataPushTaskService dataPushTaskService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataPushTaskVO>> page(@Valid DataPushTaskQuery query){
        PageResult<DataPushTaskVO> page = dataPushTaskService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataPushTaskVO> get(@PathVariable("id") Long id){
        DataPushTaskEntity entity = dataPushTaskService.getById(id);

        return Result.ok(DataPushTaskConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataPushTaskVO vo){
        dataPushTaskService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataPushTaskVO vo){
        dataPushTaskService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataPushTaskService.delete(idList);

        return Result.ok();
    }

	@PostMapping("release/{id}")
	@Operation(summary = "发布任务")
	public Result<String> release(@PathVariable Long id) {
		dataPushTaskService.release(id);
		return Result.ok();
	}

	@PostMapping("cancel/{id}")
	@Operation(summary = "取消任务")
	public Result<String> cancel(@PathVariable Long id) {
		dataPushTaskService.cancel(id);
		return Result.ok();
	}

	@PostMapping("hand-run/{id}")
	@Operation(summary = "手动调度执行")
	public Result<String> handRun(@PathVariable Long id) {
		dataPushTaskService.handRun(id);
		return Result.ok();
	}

	@PostMapping("stop-task/{executeNo}")
	@Operation(summary = "根据手动执行的编号停止任务")
	public Result<String> stopHandTask(@PathVariable String executeNo) {
		dataPushTaskService.stopHandTask(executeNo);
		return Result.ok();
	}
}
