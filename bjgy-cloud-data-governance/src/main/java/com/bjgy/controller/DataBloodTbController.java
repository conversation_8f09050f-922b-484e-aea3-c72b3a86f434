package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.blood.convert.DataBloodColumnConvert;
import com.bjgy.blood.convert.DataBloodTbConvert;
import com.bjgy.blood.dto.DataBloodStructure;
import com.bjgy.blood.entity.DataBloodTbEntity;
import com.bjgy.blood.query.DataBloodTbQuery;
import com.bjgy.blood.service.DataBloodTbService;
import com.bjgy.blood.vo.DataBloodColumnVO;
import com.bjgy.blood.vo.DataBloodTbVO;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-数据血缘（表）
 */
@RestController
@RequestMapping("/dataBloodTb")
@Tag(name = "数据治理-数据血缘（表）")
@AllArgsConstructor
public class DataBloodTbController {
	private final DataBloodTbService dataBloodTbService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataBloodTbVO>> page(@Valid DataBloodTbQuery query) {
		PageResult<DataBloodTbVO> page = dataBloodTbService.page(query);

		return Result.ok(page);
	}

	@GetMapping("blood-cloumn/{id}")
	@Operation(summary = "字段血缘")
	public Result<List<DataBloodColumnVO>> getBloodCloumn(@PathVariable("id") Long id) {
		return Result.ok(DataBloodColumnConvert.INSTANCE.convertList(dataBloodTbService.getBloodCloumn(id)));
	}

	@GetMapping("blood-structure")
	@Operation(summary = "血缘关系图")
	public Result<DataBloodStructure> getBloodStructure(@RequestParam(defaultValue = "-1") Long dbId, @RequestParam String tableName) {
		return Result.ok(dataBloodTbService.getBloodStructure(dbId, tableName));
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataBloodTbVO> get(@PathVariable("id") Long id) {
		DataBloodTbEntity entity = dataBloodTbService.getById(id);

		return Result.ok(DataBloodTbConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataBloodTbVO vo) {
		dataBloodTbService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataBloodTbVO vo) {
		dataBloodTbService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataBloodTbService.delete(idList);

		return Result.ok();
	}
}
