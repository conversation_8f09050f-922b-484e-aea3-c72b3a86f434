package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushIncreaseLogConvert;
import com.bjgy.entity.DataPushIncreaseLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataPushIncreaseLogQuery;
import com.bjgy.service.DataPushIncreaseLogService;
import com.bjgy.vo.DataPushIncreaseLogVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-数据推送增量日志
*/
@RestController
@RequestMapping("/dataPushIncreaseLog")
@Tag(name="数据治理-数据推送增量日志")
@AllArgsConstructor
public class DataPushIncreaseLogController {
    private final DataPushIncreaseLogService dataPushIncreaseLogService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataPushIncreaseLogVO>> page(@Valid DataPushIncreaseLogQuery query){
        PageResult<DataPushIncreaseLogVO> page = dataPushIncreaseLogService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataPushIncreaseLogVO> get(@PathVariable("id") Long id){
        DataPushIncreaseLogEntity entity = dataPushIncreaseLogService.getById(id);

        return Result.ok(DataPushIncreaseLogConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataPushIncreaseLogVO vo){
        dataPushIncreaseLogService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataPushIncreaseLogVO vo){
        dataPushIncreaseLogService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataPushIncreaseLogService.delete(idList);

        return Result.ok();
    }
}
