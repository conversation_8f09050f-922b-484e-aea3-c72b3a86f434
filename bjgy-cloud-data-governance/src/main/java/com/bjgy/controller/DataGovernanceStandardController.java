package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceStandardConvert;
import com.bjgy.dto.StandardCheckDto;
import com.bjgy.entity.DataGovernanceStandardEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataGovernanceStandardQuery;
import com.bjgy.service.DataGovernanceStandardService;
import com.bjgy.vo.DataGovernanceStandardVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-数据标准
 */
@RestController
@RequestMapping("/data-standard")
@Tag(name = "数据治理-数据标准")
@AllArgsConstructor
public class DataGovernanceStandardController {
	private final DataGovernanceStandardService dataGovernanceStandardService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataGovernanceStandardVO>> page(@Valid DataGovernanceStandardQuery query) {
		PageResult<DataGovernanceStandardVO> page = dataGovernanceStandardService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataGovernanceStandardVO> get(@PathVariable("id") Long id) {
		DataGovernanceStandardEntity entity = dataGovernanceStandardService.getById(id);

		return Result.ok(DataGovernanceStandardConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataGovernanceStandardVO vo) {
		dataGovernanceStandardService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataGovernanceStandardVO vo) {
		dataGovernanceStandardService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataGovernanceStandardService.delete(idList);

		return Result.ok();
	}

	@Deprecated
	@GetMapping("/{id}/online")
	@Operation(summary = "上线")
	public Result<String> online(@PathVariable("id") Long id) {
		dataGovernanceStandardService.online(id);
		return Result.ok();
	}

	@Deprecated
	@PostMapping("/{id}/offline")
	@Operation(summary = "下线")
	public Result<String> offline(@PathVariable("id") Long id) {
		dataGovernanceStandardService.offline(id);
		return Result.ok();
	}



	@GetMapping("/check/{metadataId}/{standardId}")
	@Operation(summary = "标准检测")
	public Result<StandardCheckDto> checkStandard(@PathVariable Long metadataId, @PathVariable Long standardId) {
		return Result.ok(dataGovernanceStandardService.checkStandard(metadataId, standardId));
	}
}
