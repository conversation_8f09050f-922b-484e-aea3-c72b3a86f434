package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceQualityTopicConvert;
import com.bjgy.dto.CorrectTopic;
import com.bjgy.entity.DataGovernanceQualityTopicEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataGovernanceQualityTopicService;
import com.bjgy.query.DataGovernanceQualityTopicQuery;
import com.bjgy.vo.DataGovernanceQualityTopicVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-预警主题库
*/
@RestController
@RequestMapping("/quality-topic")
@Tag(name="数据治理-预警主题库")
@AllArgsConstructor
public class DataGovernanceQualityTopicController {
    private final DataGovernanceQualityTopicService dataGovernanceQualityTopicService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataGovernanceQualityTopicVO>> page(@Valid DataGovernanceQualityTopicQuery query){
        PageResult<DataGovernanceQualityTopicVO> page = dataGovernanceQualityTopicService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataGovernanceQualityTopicVO> get(@PathVariable("id") Long id){
        DataGovernanceQualityTopicEntity entity = dataGovernanceQualityTopicService.getById(id);

        return Result.ok(DataGovernanceQualityTopicConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataGovernanceQualityTopicVO vo){
        dataGovernanceQualityTopicService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataGovernanceQualityTopicVO vo){
        dataGovernanceQualityTopicService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataGovernanceQualityTopicService.delete(idList);

        return Result.ok();
    }

	@PutMapping("correct")
	@Operation(summary = "保存修正数据")
	public Result<String> saveCorrect(@RequestBody @Valid CorrectTopic correctTopic) {
		dataGovernanceQualityTopicService.saveCorrect(correctTopic);

		return Result.ok();
	}
}
