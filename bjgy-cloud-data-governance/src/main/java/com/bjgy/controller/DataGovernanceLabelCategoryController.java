package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelCategoryConvert;
import com.bjgy.entity.DataGovernanceLabelCategoryEntity;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.service.DataGovernanceLabelCategoryService;
import com.bjgy.vo.DataGovernanceLabelCategoryVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-标签类目
*/
@RestController
@RequestMapping("/label-category")
@Tag(name="数据治理-标签类目")
@AllArgsConstructor
public class DataGovernanceLabelCategoryController {
    private final DataGovernanceLabelCategoryService dataGovernanceLabelCategoryService;

	@GetMapping("list")
	@Operation(summary = "获取列表")
	public Result<List<DataGovernanceLabelCategoryVO>> list(){
		List<DataGovernanceLabelCategoryEntity> entities = dataGovernanceLabelCategoryService.listAll();
		return Result.ok(DataGovernanceLabelCategoryConvert.INSTANCE.convertList(entities));
	}

	@GetMapping("list-tree")
	@Operation(summary = "获取目录树")
	public Result<List<TreeNodeVo>> listTree() {
		List<TreeNodeVo> treeNodeVos = dataGovernanceLabelCategoryService.listTree();
		return Result.ok(treeNodeVos);
	}

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataGovernanceLabelCategoryVO> get(@PathVariable("id") Long id){
        DataGovernanceLabelCategoryEntity entity = dataGovernanceLabelCategoryService.getById(id);

        return Result.ok(DataGovernanceLabelCategoryConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataGovernanceLabelCategoryVO vo){
        dataGovernanceLabelCategoryService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataGovernanceLabelCategoryVO vo){
        dataGovernanceLabelCategoryService.update(vo);

        return Result.ok();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除")
    public Result<String> delete(@PathVariable Long id){
        dataGovernanceLabelCategoryService.delete(id);

        return Result.ok();
    }
}
