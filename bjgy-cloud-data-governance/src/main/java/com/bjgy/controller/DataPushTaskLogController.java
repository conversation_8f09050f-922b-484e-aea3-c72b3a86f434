package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushTaskLogConvert;
import com.bjgy.entity.DataPushTaskLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataPushTaskLogService;
import com.bjgy.query.DataPushTaskLogQuery;
import com.bjgy.vo.DataPushTaskLogVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 数据治理-数据推送任务日志
*/
@RestController
@RequestMapping("/dataPushTaskLog")
@Tag(name="数据治理-数据推送任务日志")
@AllArgsConstructor
public class DataPushTaskLogController {
    private final DataPushTaskLogService dataPushTaskLogService;

    @GetMapping("page")
    @Operation(summary = "分页")
    public Result<PageResult<DataPushTaskLogVO>> page(@Valid DataPushTaskLogQuery query){
        PageResult<DataPushTaskLogVO> page = dataPushTaskLogService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<DataPushTaskLogVO> get(@PathVariable("id") Long id){
        DataPushTaskLogEntity entity = dataPushTaskLogService.getById(id);

        return Result.ok(DataPushTaskLogConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @Operation(summary = "保存")
    public Result<String> save(@RequestBody DataPushTaskLogVO vo){
        dataPushTaskLogService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    public Result<String> update(@RequestBody @Valid DataPushTaskLogVO vo){
        dataPushTaskLogService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    public Result<String> delete(@RequestBody List<Long> idList){
        dataPushTaskLogService.delete(idList);

        return Result.ok();
    }
}
