package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelConvert;
import com.bjgy.dto.LabelPageDataQuery;
import com.bjgy.entity.DataGovernanceLabelEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataGovernanceLabelQuery;
import com.bjgy.service.DataGovernanceLabelService;
import com.bjgy.vo.DataGovernanceLabelVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.model.JdbcSelectResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据治理-标签
 */
@RestController
@RequestMapping("/label")
@Tag(name = "数据治理-标签")
@AllArgsConstructor
public class DataGovernanceLabelController {
	private final DataGovernanceLabelService dataGovernanceLabelService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataGovernanceLabelVO>> page(@Valid DataGovernanceLabelQuery query) {
		PageResult<DataGovernanceLabelVO> page = dataGovernanceLabelService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataGovernanceLabelVO> get(@PathVariable("id") Long id) {
		DataGovernanceLabelEntity entity = dataGovernanceLabelService.getById(id);

		return Result.ok(DataGovernanceLabelConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataGovernanceLabelVO vo) {
		dataGovernanceLabelService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataGovernanceLabelVO vo) {
		dataGovernanceLabelService.update(vo);
		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataGovernanceLabelService.delete(idList);

		return Result.ok();
	}

	@PostMapping("/on/{id}")
	@Operation(summary = "启用")
	public Result<String> on(@PathVariable Long id) {
		dataGovernanceLabelService.on(id);
		return Result.ok();
	}

	@PostMapping("/off/{id}")
	@Operation(summary = "禁用")
	public Result<String> off(@PathVariable Long id) {
		dataGovernanceLabelService.off(id);
		return Result.ok();
	}

	@GetMapping("list")
	@Operation(summary = "获取启用的标签列表")
	public Result<List<DataGovernanceLabelVO>> listEnable(Long typeId) {
		List<DataGovernanceLabelVO> enableList = dataGovernanceLabelService.listEnable(typeId);
		return Result.ok(enableList);
	}

	@GetMapping("list-column/{id}")
	@Operation(summary = "根据Id获取字段列表")
	public Result<List<ColumnDescription>> listColumn(@PathVariable Integer id) {
		return Result.ok(dataGovernanceLabelService.listColumn(id));
	}

	@PostMapping("/table-data")
	@Operation(summary = "根据sql获取数据")
	public Result<JdbcSelectResult> pageDataBySql(@RequestBody LabelPageDataQuery labelPageDataQuery) {
		JdbcSelectResult jdbcSelectResult = dataGovernanceLabelService.pageDataBySql(labelPageDataQuery);
		return Result.ok(jdbcSelectResult);
	}
}
