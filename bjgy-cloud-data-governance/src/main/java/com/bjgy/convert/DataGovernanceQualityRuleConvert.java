package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceQualityRuleEntity;
import com.bjgy.vo.DataGovernanceQualityRuleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-质量规则
*/
@Mapper
public interface DataGovernanceQualityRuleConvert {
    DataGovernanceQualityRuleConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityRuleConvert.class);

    DataGovernanceQualityRuleEntity convert(DataGovernanceQualityRuleVO vo);

    DataGovernanceQualityRuleVO convert(DataGovernanceQualityRuleEntity entity);

    List<DataGovernanceQualityRuleVO> convertList(List<DataGovernanceQualityRuleEntity> list);

}