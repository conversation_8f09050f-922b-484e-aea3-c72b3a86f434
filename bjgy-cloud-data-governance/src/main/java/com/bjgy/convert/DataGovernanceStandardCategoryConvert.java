package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceStandardCategoryEntity;
import com.bjgy.vo.DataGovernanceStandardCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标准目录
*/
@Mapper
public interface DataGovernanceStandardCategoryConvert {
    DataGovernanceStandardCategoryConvert INSTANCE = Mappers.getMapper(DataGovernanceStandardCategoryConvert.class);

    DataGovernanceStandardCategoryEntity convert(DataGovernanceStandardCategoryVO vo);

    DataGovernanceStandardCategoryVO convert(DataGovernanceStandardCategoryEntity entity);

    List<DataGovernanceStandardCategoryVO> convertList(List<DataGovernanceStandardCategoryEntity> list);

}