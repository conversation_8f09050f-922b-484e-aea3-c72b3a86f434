package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceMetadataStandardRelEntity;
import com.bjgy.vo.DataGovernanceMetadataStandardRelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-元数据标准关联表
*/
@Mapper
public interface DataGovernanceMetadataStandardRelConvert {
    DataGovernanceMetadataStandardRelConvert INSTANCE = Mappers.getMapper(DataGovernanceMetadataStandardRelConvert.class);

    DataGovernanceMetadataStandardRelEntity convert(DataGovernanceMetadataStandardRelVO vo);

    DataGovernanceMetadataStandardRelVO convert(DataGovernanceMetadataStandardRelEntity entity);

    List<DataGovernanceMetadataStandardRelVO> convertList(List<DataGovernanceMetadataStandardRelEntity> list);

}