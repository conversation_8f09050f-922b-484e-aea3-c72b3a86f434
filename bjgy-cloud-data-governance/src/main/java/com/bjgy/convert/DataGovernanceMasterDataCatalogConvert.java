package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceMasterDataCatalogEntity;
import com.bjgy.vo.DataGovernanceMasterDataCatalogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-主数据目录
*/
@Mapper
public interface DataGovernanceMasterDataCatalogConvert {
    DataGovernanceMasterDataCatalogConvert INSTANCE = Mappers.getMapper(DataGovernanceMasterDataCatalogConvert.class);

    DataGovernanceMasterDataCatalogEntity convert(DataGovernanceMasterDataCatalogVO vo);

    DataGovernanceMasterDataCatalogVO convert(DataGovernanceMasterDataCatalogEntity entity);

    List<DataGovernanceMasterDataCatalogVO> convertList(List<DataGovernanceMasterDataCatalogEntity> list);

}