package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceQualityLogDto;
import com.bjgy.entity.DataGovernanceQualityLogEntity;
import com.bjgy.vo.DataGovernanceQualityLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 质量规则-预警日志
*/
@Mapper
public interface DataGovernanceQualityLogConvert {
    DataGovernanceQualityLogConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityLogConvert.class);

    DataGovernanceQualityLogEntity convert(DataGovernanceQualityLogVO vo);

    DataGovernanceQualityLogVO convert(DataGovernanceQualityLogEntity entity);

	DataGovernanceQualityLogEntity convert(DataGovernanceQualityLogDto dto);

    List<DataGovernanceQualityLogVO> convertList(List<DataGovernanceQualityLogEntity> list);

}
