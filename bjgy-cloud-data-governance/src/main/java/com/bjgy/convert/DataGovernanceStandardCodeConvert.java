package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceStandardCodeEntity;
import com.bjgy.vo.DataGovernanceStandardCodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标准码表数据
*/
@Mapper
public interface DataGovernanceStandardCodeConvert {
    DataGovernanceStandardCodeConvert INSTANCE = Mappers.getMapper(DataGovernanceStandardCodeConvert.class);

    DataGovernanceStandardCodeEntity convert(DataGovernanceStandardCodeVO vo);

    DataGovernanceStandardCodeVO convert(DataGovernanceStandardCodeEntity entity);

    List<DataGovernanceStandardCodeVO> convertList(List<DataGovernanceStandardCodeEntity> list);

}