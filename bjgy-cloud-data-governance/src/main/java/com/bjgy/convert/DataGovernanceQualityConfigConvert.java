package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceQualityConfigDto;
import com.bjgy.entity.DataGovernanceQualityConfigEntity;
import com.bjgy.vo.DataGovernanceQualityConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据治理-质量规则配置
 */
@Mapper
public interface DataGovernanceQualityConfigConvert {
	DataGovernanceQualityConfigConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityConfigConvert.class);

	DataGovernanceQualityConfigEntity convert(DataGovernanceQualityConfigVO vo);

	DataGovernanceQualityConfigVO convert(DataGovernanceQualityConfigEntity entity);

	List<DataGovernanceQualityConfigVO> convertList(List<DataGovernanceQualityConfigEntity> list);

	DataGovernanceQualityConfigDto convertDto(DataGovernanceQualityConfigEntity entity);
}
