package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceLabelCategoryEntity;
import com.bjgy.vo.DataGovernanceLabelCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标签类目
*/
@Mapper
public interface DataGovernanceLabelCategoryConvert {
    DataGovernanceLabelCategoryConvert INSTANCE = Mappers.getMapper(DataGovernanceLabelCategoryConvert.class);

    DataGovernanceLabelCategoryEntity convert(DataGovernanceLabelCategoryVO vo);

    DataGovernanceLabelCategoryVO convert(DataGovernanceLabelCategoryEntity entity);

    List<DataGovernanceLabelCategoryVO> convertList(List<DataGovernanceLabelCategoryEntity> list);

}