package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataPushTaskDto;
import com.bjgy.entity.DataPushTaskEntity;
import com.bjgy.vo.DataPushTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-数据推送任务
*/
@Mapper
public interface DataPushTaskConvert {
    DataPushTaskConvert INSTANCE = Mappers.getMapper(DataPushTaskConvert.class);

    DataPushTaskEntity convert(DataPushTaskVO vo);

    DataPushTaskVO convert(DataPushTaskEntity entity);

    List<DataPushTaskVO> convertList(List<DataPushTaskEntity> list);

	DataPushTaskDto convertDto(DataPushTaskEntity entity);
}
