package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataPushTaskLogDto;
import com.bjgy.entity.DataPushTaskLogEntity;
import com.bjgy.vo.DataPushTaskLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-数据推送任务日志
*/
@Mapper
public interface DataPushTaskLogConvert {
    DataPushTaskLogConvert INSTANCE = Mappers.getMapper(DataPushTaskLogConvert.class);

    DataPushTaskLogEntity convert(DataPushTaskLogVO vo);

	DataPushTaskLogEntity convert(DataPushTaskLogDto dto);

    DataPushTaskLogVO convert(DataPushTaskLogEntity entity);

    List<DataPushTaskLogVO> convertList(List<DataPushTaskLogEntity> list);

	DataPushTaskLogDto convertDto(DataPushTaskLogEntity entity);
}
