package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceMetamodelPropertyEntity;
import com.bjgy.vo.DataGovernanceMetamodelPropertyVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-元模型属性
*/
@Mapper
public interface DataGovernanceMetamodelPropertyConvert {
    DataGovernanceMetamodelPropertyConvert INSTANCE = Mappers.getMapper(DataGovernanceMetamodelPropertyConvert.class);

    DataGovernanceMetamodelPropertyEntity convert(DataGovernanceMetamodelPropertyVO vo);

    DataGovernanceMetamodelPropertyVO convert(DataGovernanceMetamodelPropertyEntity entity);

    List<DataGovernanceMetamodelPropertyVO> convertList(List<DataGovernanceMetamodelPropertyEntity> list);

}