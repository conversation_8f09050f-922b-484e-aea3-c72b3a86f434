package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataPushConfigDto;
import com.bjgy.entity.DataPushConfigEntity;
import com.bjgy.vo.DataPushConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-数据推送配置
*/
@Mapper
public interface DataPushConfigConvert {
    DataPushConfigConvert INSTANCE = Mappers.getMapper(DataPushConfigConvert.class);

    DataPushConfigEntity convert(DataPushConfigVO vo);

    DataPushConfigVO convert(DataPushConfigEntity entity);

    List<DataPushConfigVO> convertList(List<DataPushConfigEntity> list);

	DataPushConfigDto convertDto(DataPushConfigEntity entity);
}
