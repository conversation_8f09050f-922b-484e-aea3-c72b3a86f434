package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMetadataCollectRecordDto;
import com.bjgy.entity.DataGovernanceMetadataCollectRecordEntity;
import com.bjgy.vo.DataGovernanceMetadataCollectRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-元数据采集任务记录
*/
@Mapper
public interface DataGovernanceMetadataCollectRecordConvert {
    DataGovernanceMetadataCollectRecordConvert INSTANCE = Mappers.getMapper(DataGovernanceMetadataCollectRecordConvert.class);

    DataGovernanceMetadataCollectRecordEntity convert(DataGovernanceMetadataCollectRecordVO vo);

	DataGovernanceMetadataCollectRecordEntity convert(DataGovernanceMetadataCollectRecordDto dto);

    DataGovernanceMetadataCollectRecordVO convert(DataGovernanceMetadataCollectRecordEntity entity);

    List<DataGovernanceMetadataCollectRecordVO> convertList(List<DataGovernanceMetadataCollectRecordEntity> list);

}
