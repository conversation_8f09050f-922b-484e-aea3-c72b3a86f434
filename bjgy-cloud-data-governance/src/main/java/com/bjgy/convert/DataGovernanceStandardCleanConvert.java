package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceStandardCleanDto;
import com.bjgy.entity.DataGovernanceStandardCleanEntity;
import com.bjgy.vo.DataGovernanceStandardCleanVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标准清洗
*/
@Mapper
public interface DataGovernanceStandardCleanConvert {
    DataGovernanceStandardCleanConvert INSTANCE = Mappers.getMapper(DataGovernanceStandardCleanConvert.class);

    DataGovernanceStandardCleanEntity convert(DataGovernanceStandardCleanVO vo);

    DataGovernanceStandardCleanVO convert(DataGovernanceStandardCleanEntity entity);

    List<DataGovernanceStandardCleanVO> convertList(List<DataGovernanceStandardCleanEntity> list);

	DataGovernanceStandardCleanDto convertDto(DataGovernanceStandardCleanEntity standardCleanEntity);
}
