package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMasterModelDto;
import com.bjgy.entity.DataGovernanceMasterModelEntity;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.vo.DataGovernanceMasterModelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-主数据模型
*/
@Mapper
public interface DataGovernanceMasterModelConvert {
    DataGovernanceMasterModelConvert INSTANCE = Mappers.getMapper(DataGovernanceMasterModelConvert.class);

    DataGovernanceMasterModelEntity convert(DataGovernanceMasterModelVO vo);

    DataGovernanceMasterModelVO convert(DataGovernanceMasterModelEntity entity);

    List<DataGovernanceMasterModelVO> convertList(List<DataGovernanceMasterModelEntity> list);

	DataGovernanceMasterModelDto convertDto(DataGovernanceMasterModelEntity modelEntity);
}
