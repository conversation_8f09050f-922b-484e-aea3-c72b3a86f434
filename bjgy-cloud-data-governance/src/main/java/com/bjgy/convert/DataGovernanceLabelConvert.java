package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceLabelEntity;
import com.bjgy.vo.DataGovernanceLabelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标签
*/
@Mapper
public interface DataGovernanceLabelConvert {
    DataGovernanceLabelConvert INSTANCE = Mappers.getMapper(DataGovernanceLabelConvert.class);

    DataGovernanceLabelEntity convert(DataGovernanceLabelVO vo);

    DataGovernanceLabelVO convert(DataGovernanceLabelEntity entity);

    List<DataGovernanceLabelVO> convertList(List<DataGovernanceLabelEntity> list);

}