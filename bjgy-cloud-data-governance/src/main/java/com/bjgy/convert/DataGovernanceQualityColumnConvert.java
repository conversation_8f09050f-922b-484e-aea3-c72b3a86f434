package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceQualityColumnDto;
import com.bjgy.entity.DataGovernanceQualityColumnEntity;
import com.bjgy.vo.DataGovernanceQualityColumnVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-质量规则字段配置
*/
@Mapper
public interface DataGovernanceQualityColumnConvert {
    DataGovernanceQualityColumnConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityColumnConvert.class);

    DataGovernanceQualityColumnEntity convert(DataGovernanceQualityColumnVO vo);

    DataGovernanceQualityColumnVO convert(DataGovernanceQualityColumnEntity entity);

    List<DataGovernanceQualityColumnVO> convertList(List<DataGovernanceQualityColumnEntity> list);

	List<DataGovernanceQualityColumnDto> convertDtoList(List<DataGovernanceQualityColumnEntity> list);

}
