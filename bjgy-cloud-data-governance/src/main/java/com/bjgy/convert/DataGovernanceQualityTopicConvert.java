package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceQualityTopicDto;
import com.bjgy.entity.DataGovernanceQualityTopicEntity;
import com.bjgy.vo.DataGovernanceQualityTopicVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-预警主题库
*/
@Mapper
public interface DataGovernanceQualityTopicConvert {
    DataGovernanceQualityTopicConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityTopicConvert.class);

    DataGovernanceQualityTopicEntity convert(DataGovernanceQualityTopicVO vo);

    DataGovernanceQualityTopicVO convert(DataGovernanceQualityTopicEntity entity);

    List<DataGovernanceQualityTopicVO> convertList(List<DataGovernanceQualityTopicEntity> list);

	List<DataGovernanceQualityTopicEntity> convertDtoList(List<DataGovernanceQualityTopicDto> list);

}
