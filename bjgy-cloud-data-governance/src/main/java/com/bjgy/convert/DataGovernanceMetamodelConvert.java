package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceMetamodelEntity;
import com.bjgy.vo.DataGovernanceMetamodelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-元模型
*
* @<NAME_EMAIL>
* @since 1.0.0 2023-03-28
*/
@Mapper
public interface DataGovernanceMetamodelConvert {
    DataGovernanceMetamodelConvert INSTANCE = Mappers.getMapper(DataGovernanceMetamodelConvert.class);

    DataGovernanceMetamodelEntity convert(DataGovernanceMetamodelVO vo);

    DataGovernanceMetamodelVO convert(DataGovernanceMetamodelEntity entity);

    List<DataGovernanceMetamodelVO> convertList(List<DataGovernanceMetamodelEntity> list);

}