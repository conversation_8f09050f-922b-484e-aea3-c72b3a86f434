package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMasterDistributeLogDto;
import com.bjgy.entity.DataGovernanceMasterDistributeLogEntity;
import com.bjgy.vo.DataGovernanceMasterDistributeLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-主数据派发日志
*/
@Mapper
public interface DataGovernanceMasterDistributeLogConvert {
    DataGovernanceMasterDistributeLogConvert INSTANCE = Mappers.getMapper(DataGovernanceMasterDistributeLogConvert.class);

    DataGovernanceMasterDistributeLogEntity convert(DataGovernanceMasterDistributeLogVO vo);

	DataGovernanceMasterDistributeLogEntity convert(DataGovernanceMasterDistributeLogDto dto);

    DataGovernanceMasterDistributeLogVO convert(DataGovernanceMasterDistributeLogEntity entity);

    List<DataGovernanceMasterDistributeLogVO> convertList(List<DataGovernanceMasterDistributeLogEntity> list);

}
