package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMasterDistributeDto;
import com.bjgy.entity.DataGovernanceMasterDistributeEntity;
import com.bjgy.vo.DataGovernanceMasterDistributeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据治理-主数据派发
 */
@Mapper
public interface DataGovernanceMasterDistributeConvert {
	DataGovernanceMasterDistributeConvert INSTANCE = Mappers.getMapper(DataGovernanceMasterDistributeConvert.class);

	DataGovernanceMasterDistributeEntity convert(DataGovernanceMasterDistributeVO vo);

	DataGovernanceMasterDistributeVO convert(DataGovernanceMasterDistributeEntity entity);

	List<DataGovernanceMasterDistributeVO> convertList(List<DataGovernanceMasterDistributeEntity> list);

	DataGovernanceMasterDistributeDto convertDto(DataGovernanceMasterDistributeEntity masterDistributeEntity);
}
