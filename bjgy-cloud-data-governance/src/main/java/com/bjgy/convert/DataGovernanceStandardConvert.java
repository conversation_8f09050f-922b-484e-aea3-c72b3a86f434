package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceStandardEntity;
import com.bjgy.vo.DataGovernanceStandardVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-数据标准
*/
@Mapper
public interface DataGovernanceStandardConvert {
    DataGovernanceStandardConvert INSTANCE = Mappers.getMapper(DataGovernanceStandardConvert.class);

    DataGovernanceStandardEntity convert(DataGovernanceStandardVO vo);

    DataGovernanceStandardVO convert(DataGovernanceStandardEntity entity);

    List<DataGovernanceStandardVO> convertList(List<DataGovernanceStandardEntity> list);

}