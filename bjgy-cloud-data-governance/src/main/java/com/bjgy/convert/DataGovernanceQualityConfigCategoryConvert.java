package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceQualityConfigCategoryEntity;
import com.bjgy.vo.DataGovernanceQualityConfigCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-规则配置目录
*/
@Mapper
public interface DataGovernanceQualityConfigCategoryConvert {
    DataGovernanceQualityConfigCategoryConvert INSTANCE = Mappers.getMapper(DataGovernanceQualityConfigCategoryConvert.class);

    DataGovernanceQualityConfigCategoryEntity convert(DataGovernanceQualityConfigCategoryVO vo);

    DataGovernanceQualityConfigCategoryVO convert(DataGovernanceQualityConfigCategoryEntity entity);

    List<DataGovernanceQualityConfigCategoryVO> convertList(List<DataGovernanceQualityConfigCategoryEntity> list);

}