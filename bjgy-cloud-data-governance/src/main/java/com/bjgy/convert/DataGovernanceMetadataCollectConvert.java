package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMetadataCollectDto;
import com.bjgy.entity.DataGovernanceMetadataCollectEntity;
import com.bjgy.vo.DataGovernanceMetadataCollectVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据治理-元数据采集
 */
@Mapper
public interface DataGovernanceMetadataCollectConvert {
	DataGovernanceMetadataCollectConvert INSTANCE = Mappers.getMapper(DataGovernanceMetadataCollectConvert.class);

	DataGovernanceMetadataCollectEntity convert(DataGovernanceMetadataCollectVO vo);

	DataGovernanceMetadataCollectEntity convert(DataGovernanceMetadataCollectDto dto);

	DataGovernanceMetadataCollectDto convertDto(DataGovernanceMetadataCollectEntity entity);

	DataGovernanceMetadataCollectVO convert(DataGovernanceMetadataCollectEntity entity);

	List<DataGovernanceMetadataCollectVO> convertList(List<DataGovernanceMetadataCollectEntity> list);

}
