package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceLabelModelEntity;
import com.bjgy.vo.DataGovernanceLabelModelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标签实体
*/
@Mapper
public interface DataGovernanceLabelModelConvert {
    DataGovernanceLabelModelConvert INSTANCE = Mappers.getMapper(DataGovernanceLabelModelConvert.class);

    DataGovernanceLabelModelEntity convert(DataGovernanceLabelModelVO vo);

    DataGovernanceLabelModelVO convert(DataGovernanceLabelModelEntity entity);

    List<DataGovernanceLabelModelVO> convertList(List<DataGovernanceLabelModelEntity> list);

}
