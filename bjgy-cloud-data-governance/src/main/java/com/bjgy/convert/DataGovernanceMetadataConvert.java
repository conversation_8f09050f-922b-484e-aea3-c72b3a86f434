package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMetadataDto;
import com.bjgy.entity.DataGovernanceMetadataEntity;
import com.bjgy.vo.DataGovernanceMetadataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数据治理-元数据
 */
@Mapper
public interface DataGovernanceMetadataConvert {
	DataGovernanceMetadataConvert INSTANCE = Mappers.getMapper(DataGovernanceMetadataConvert.class);

	DataGovernanceMetadataEntity convert(DataGovernanceMetadataVO vo);

	DataGovernanceMetadataEntity convert(DataGovernanceMetadataDto dto);

	DataGovernanceMetadataVO convert(DataGovernanceMetadataEntity entity);

	DataGovernanceMetadataDto convertDto(DataGovernanceMetadataEntity entity);

	List<DataGovernanceMetadataVO> convertList(List<DataGovernanceMetadataEntity> list);

	List<DataGovernanceMetadataDto> convertDtoList(List<DataGovernanceMetadataEntity> list);

}
