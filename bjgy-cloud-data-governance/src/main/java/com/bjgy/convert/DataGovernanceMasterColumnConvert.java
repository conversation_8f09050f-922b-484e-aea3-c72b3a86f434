package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMasterColumnDto;
import com.bjgy.entity.DataGovernanceMasterColumnEntity;
import com.bjgy.vo.DataGovernanceMasterColumnVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-主数据模型字段
*/
@Mapper
public interface DataGovernanceMasterColumnConvert {
    DataGovernanceMasterColumnConvert INSTANCE = Mappers.getMapper(DataGovernanceMasterColumnConvert.class);

    DataGovernanceMasterColumnEntity convert(DataGovernanceMasterColumnVO vo);

    DataGovernanceMasterColumnVO convert(DataGovernanceMasterColumnEntity entity);

    List<DataGovernanceMasterColumnVO> convertList(List<DataGovernanceMasterColumnEntity> list);

	List<DataGovernanceMasterColumnDto> convertDtoList(List<DataGovernanceMasterColumnEntity> list);
}
