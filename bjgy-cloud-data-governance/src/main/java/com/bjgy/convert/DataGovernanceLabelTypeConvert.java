package com.bjgy.convert;

import com.bjgy.entity.DataGovernanceLabelTypeEntity;
import com.bjgy.vo.DataGovernanceLabelTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标签类型
*/
@Mapper
public interface DataGovernanceLabelTypeConvert {
    DataGovernanceLabelTypeConvert INSTANCE = Mappers.getMapper(DataGovernanceLabelTypeConvert.class);

    DataGovernanceLabelTypeEntity convert(DataGovernanceLabelTypeVO vo);

    DataGovernanceLabelTypeVO convert(DataGovernanceLabelTypeEntity entity);

    List<DataGovernanceLabelTypeVO> convertList(List<DataGovernanceLabelTypeEntity> list);

}