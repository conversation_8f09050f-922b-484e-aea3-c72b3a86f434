package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceStandardCleanLogDto;
import com.bjgy.entity.DataGovernanceStandardCleanLogEntity;
import com.bjgy.vo.DataGovernanceStandardCleanLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-标准清洗日志
*/
@Mapper
public interface DataGovernanceStandardCleanLogConvert {
    DataGovernanceStandardCleanLogConvert INSTANCE = Mappers.getMapper(DataGovernanceStandardCleanLogConvert.class);

    DataGovernanceStandardCleanLogEntity convert(DataGovernanceStandardCleanLogVO vo);

	DataGovernanceStandardCleanLogEntity convert(DataGovernanceStandardCleanLogDto dto);

    DataGovernanceStandardCleanLogVO convert(DataGovernanceStandardCleanLogEntity entity);

    List<DataGovernanceStandardCleanLogVO> convertList(List<DataGovernanceStandardCleanLogEntity> list);

}
