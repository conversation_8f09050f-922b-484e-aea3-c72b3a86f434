package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataPushIncreaseLogDto;
import com.bjgy.api.module.data.governance.dto.DataPushTaskLogDto;
import com.bjgy.entity.DataPushIncreaseLogEntity;
import com.bjgy.vo.DataPushIncreaseLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-数据推送增量日志
*/
@Mapper
public interface DataPushIncreaseLogConvert {
    DataPushIncreaseLogConvert INSTANCE = Mappers.getMapper(DataPushIncreaseLogConvert.class);

    DataPushIncreaseLogEntity convert(DataPushIncreaseLogVO vo);

	DataPushIncreaseLogEntity convert(DataPushIncreaseLogDto dto);

    DataPushIncreaseLogVO convert(DataPushIncreaseLogEntity entity);

    List<DataPushIncreaseLogVO> convertList(List<DataPushIncreaseLogEntity> list);

	DataPushIncreaseLogDto convertDto(DataPushIncreaseLogEntity selectOne);
}
