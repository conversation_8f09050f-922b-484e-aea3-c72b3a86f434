package com.bjgy.convert;

import com.bjgy.api.module.data.governance.dto.DataGovernanceMetadataPropertyDto;
import com.bjgy.entity.DataGovernanceMetadataPropertyEntity;
import com.bjgy.vo.DataGovernanceMetadataPropertyVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据治理-元数据属性值
*/
@Mapper
public interface DataGovernanceMetadataPropertyConvert {
    DataGovernanceMetadataPropertyConvert INSTANCE = Mappers.getMapper(DataGovernanceMetadataPropertyConvert.class);

    DataGovernanceMetadataPropertyEntity convert(DataGovernanceMetadataPropertyVO vo);

	DataGovernanceMetadataPropertyEntity convert(DataGovernanceMetadataPropertyDto dto);

    DataGovernanceMetadataPropertyVO convert(DataGovernanceMetadataPropertyEntity entity);

	DataGovernanceMetadataPropertyDto convertDto(DataGovernanceMetadataPropertyEntity entity);

    List<DataGovernanceMetadataPropertyVO> convertList(List<DataGovernanceMetadataPropertyEntity> list);

}
