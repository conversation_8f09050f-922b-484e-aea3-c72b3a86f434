package com.bjgy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据治理-标准清洗日志
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_governance_standard_clean_log")
public class DataGovernanceStandardCleanLogEntity extends BaseEntity {

	/**
	* 标准清洗id
	*/
	private Long metadataId;
	private Long standardId;
	private Long standardCleanId;
	private String standardCleanName;

	/**
	* 1-成功 0-失败 2-运行中
	*/
	private Integer status;

	/**
	* 实时日志
	*/
	private String realTimeLog;

	/**
	* 错误日志
	*/
	private String errorLog;

	/**
	* 开始时间
	*/
	private Date startTime;

	/**
	* 结束时间
	*/
	private Date endTime;

	/**
	* 机构id
	*/
	private Long orgId;

	/**
	* 项目（租户）id
	*/
	private Long projectId;


	private Integer deleted;


}
