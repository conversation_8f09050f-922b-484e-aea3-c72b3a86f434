package com.bjgy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据治理-主数据目录
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("data_governance_master_data_catalog")
public class DataGovernanceMasterDataCatalogEntity extends BaseEntity {

	/**
	 * 父级id
	 */
	private Long parentId;

	/**
	 * 类型-0-目录 1-主数据
	 */
	private Integer type;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 序号
	 */
	private Integer orderNo;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 目录全路径
	 */
	private String path;

	/**
	 * 项目id
	 */
	private Long projectId;
	private Long orgId;


}
