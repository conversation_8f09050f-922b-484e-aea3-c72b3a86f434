package com.bjgy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据治理-质量规则字段配置
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_governance_quality_column")
public class DataGovernanceQualityColumnEntity extends BaseEntity {

	/**
	* 质量规则id
	*/
	private Long qualityConfigId;

	/**
	* 源表字段（英文）
	*/
	private String sourceColumn;

	private String sourceColumnType;
	/**
	* 源表字段（注释）
	*/
	private String sourceColumnRemark;

	/**
	* 规则类型id
	*/
	private Long ruleId;

	/**
	* 数据范围校验 开始值
	*/
	private String rangeStart;

	/**
	* 数据范围校验 结束值
	*/
	private String rangeEnd;

	/**
	* 及时性校验
	*/
	private Integer timelyNum;

	/**
	 * 及时性校验类型 1-分钟 2-小时 3-天
	 */
	private Integer timelyType;

	/**
	* 正则表达式校验 正则
	*/
	private String regexVal;

	/**
	* 长度校验-长度
	*/
	private Integer dataLength;







}
