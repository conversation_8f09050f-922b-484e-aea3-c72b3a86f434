package com.bjgy.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.mybatis.entity.BaseEntity;

/**
 * 数据治理-质量规则配置
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "data_governance_quality_config", autoResultMap = true)
public class DataGovernanceQualityConfigEntity extends BaseEntity {

	/**
	 * 名称
	 */
	private String name;

	private Long categoryId;

	/**
	 * 质量检测类型 1-技术检测 2-业务检测
	 */
	private Integer qualityType;

	/**
	 * 状态，1-启用，0-关闭
	 */
	private Integer status;

	/**
	 * 任务类型，1-一次性任务，2-周期任务
	 */
	private Integer taskType;

	private String dataProv;
	private Long sourceTableMetaId;
	private String sourceTableName;
	private String sourceTableRemark;
	private String sourceTablePkRemark;
	private String sourceTablePkType;
	private String sourceTablePk;
	private String dutyUser;
	private String dutyPhone;
	private String dutyEmail;
	private Integer smsModelId;

	/**
	 * cron表达式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String cron;

	/**
	 * 备注
	 */
	private String note;

	/**
	 * 项目id
	 */
	private Long projectId;
	private Long orgId;


}
