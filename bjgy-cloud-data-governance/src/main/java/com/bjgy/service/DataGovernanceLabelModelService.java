package com.bjgy.service;

import com.bjgy.dto.CheckSqlRequest;
import com.bjgy.dto.CheckSqlResult;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceLabelModelQuery;
import com.bjgy.vo.DataGovernanceLabelModelVO;
import com.bjgy.entity.DataGovernanceLabelModelEntity;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;

import java.util.List;

/**
 * 数据治理-标签实体
 */
public interface DataGovernanceLabelModelService extends BaseService<DataGovernanceLabelModelEntity> {

    PageResult<DataGovernanceLabelModelVO> page(DataGovernanceLabelModelQuery query);

    void save(DataGovernanceLabelModelVO vo);

    void update(DataGovernanceLabelModelVO vo);

    void delete(List<Long> idList);

	CheckSqlResult checkSql(CheckSqlRequest checkSqlRequest);

	List<DataGovernanceLabelModelEntity> listAll();

	List<ColumnDescription> listColumn(Long id);
}
