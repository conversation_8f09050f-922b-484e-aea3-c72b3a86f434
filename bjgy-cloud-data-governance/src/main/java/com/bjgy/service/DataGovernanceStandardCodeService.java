package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceStandardCodeVO;
import com.bjgy.query.DataGovernanceStandardCodeQuery;
import com.bjgy.entity.DataGovernanceStandardCodeEntity;

import java.util.List;

/**
 * 数据治理-标准码表数据
 */
public interface DataGovernanceStandardCodeService extends BaseService<DataGovernanceStandardCodeEntity> {

    PageResult<DataGovernanceStandardCodeVO> page(DataGovernanceStandardCodeQuery query);

    void save(DataGovernanceStandardCodeVO vo);

    void update(DataGovernanceStandardCodeVO vo);

    void delete(List<Long> idList);
}
