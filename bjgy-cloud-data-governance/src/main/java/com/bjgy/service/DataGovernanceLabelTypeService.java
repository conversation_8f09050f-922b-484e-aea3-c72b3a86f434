package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceLabelTypeVO;
import com.bjgy.query.DataGovernanceLabelTypeQuery;
import com.bjgy.entity.DataGovernanceLabelTypeEntity;

import java.util.List;

/**
 * 数据治理-标签类型
 */
public interface DataGovernanceLabelTypeService extends BaseService<DataGovernanceLabelTypeEntity> {

    PageResult<DataGovernanceLabelTypeVO> page(DataGovernanceLabelTypeQuery query);

    void save(DataGovernanceLabelTypeVO vo);

    void update(DataGovernanceLabelTypeVO vo);

    void delete(List<Long> idList);

	List<DataGovernanceLabelTypeEntity> listAll();
}
