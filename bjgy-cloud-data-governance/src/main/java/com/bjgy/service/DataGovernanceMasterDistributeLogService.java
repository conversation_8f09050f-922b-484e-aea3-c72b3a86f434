package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMasterDistributeLogVO;
import com.bjgy.query.DataGovernanceMasterDistributeLogQuery;
import com.bjgy.entity.DataGovernanceMasterDistributeLogEntity;

import java.util.List;

/**
 * 数据治理-主数据派发日志
 */
public interface DataGovernanceMasterDistributeLogService extends BaseService<DataGovernanceMasterDistributeLogEntity> {

    PageResult<DataGovernanceMasterDistributeLogVO> page(DataGovernanceMasterDistributeLogQuery query);

    void save(DataGovernanceMasterDistributeLogVO vo);

    void update(DataGovernanceMasterDistributeLogVO vo);

    void delete(List<Long> idList);
}
