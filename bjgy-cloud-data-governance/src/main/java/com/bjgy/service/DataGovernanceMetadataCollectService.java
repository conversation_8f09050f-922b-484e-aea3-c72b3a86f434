package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceMetadataCollectQuery;
import com.bjgy.vo.DataGovernanceMetadataCollectVO;
import com.bjgy.entity.DataGovernanceMetadataCollectEntity;

import java.util.List;

/**
 * 数据治理-元数据采集
 */
public interface DataGovernanceMetadataCollectService extends BaseService<DataGovernanceMetadataCollectEntity> {

    PageResult<DataGovernanceMetadataCollectVO> page(DataGovernanceMetadataCollectQuery query);

    void save(DataGovernanceMetadataCollectVO vo);

    void update(DataGovernanceMetadataCollectVO vo);

    void delete(List<Long> idList);

	void release(Long id);

	void cancel(Long id);

	void handRun(Long id);
}
