package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceQualityLogQuery;
import com.bjgy.vo.DataGovernanceQualityLogVO;
import com.bjgy.entity.DataGovernanceQualityLogEntity;

import java.util.List;

/**
 * 质量规则-预警日志
 */
public interface DataGovernanceQualityLogService extends BaseService<DataGovernanceQualityLogEntity> {

    PageResult<DataGovernanceQualityLogVO> page(DataGovernanceQualityLogQuery query);

    void save(DataGovernanceQualityLogVO vo);

    void update(DataGovernanceQualityLogVO vo);

    void delete(List<Long> idList);

	void dealNotFinished();
}
