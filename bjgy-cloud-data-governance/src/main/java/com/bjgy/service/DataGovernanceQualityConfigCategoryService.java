package com.bjgy.service;

import com.bjgy.entity.DataGovernanceQualityConfigCategoryEntity;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceQualityConfigCategoryVO;

import java.util.List;

/**
 * 数据治理-规则配置目录
 */
public interface DataGovernanceQualityConfigCategoryService extends BaseService<DataGovernanceQualityConfigCategoryEntity> {

	List<TreeNodeVo> listTree();

	void save(DataGovernanceQualityConfigCategoryVO vo);

	void update(DataGovernanceQualityConfigCategoryVO vo);

	void delete(Long id);


}
