package com.bjgy.service;

import com.bjgy.entity.DataGovernanceQualityRuleEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceQualityRuleQuery;
import com.bjgy.vo.DataGovernanceQualityRuleVO;

import java.util.List;

/**
 * 数据治理-质量规则
 */
public interface DataGovernanceQualityRuleService extends BaseService<DataGovernanceQualityRuleEntity> {

    PageResult<DataGovernanceQualityRuleVO> page(DataGovernanceQualityRuleQuery query);

    void save(DataGovernanceQualityRuleVO vo);

    void update(DataGovernanceQualityRuleVO vo);

    void delete(List<Long> idList);
}
