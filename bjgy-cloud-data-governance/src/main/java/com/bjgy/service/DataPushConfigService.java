package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataPushConfigVO;
import com.bjgy.query.DataPushConfigQuery;
import com.bjgy.entity.DataPushConfigEntity;

/**
 * 数据治理-数据推送配置
 */
public interface DataPushConfigService extends BaseService<DataPushConfigEntity> {

    PageResult<DataPushConfigVO> page(DataPushConfigQuery query);

    void save(DataPushConfigVO vo);

    void update(DataPushConfigVO vo);

    void delete(Long id);
}
