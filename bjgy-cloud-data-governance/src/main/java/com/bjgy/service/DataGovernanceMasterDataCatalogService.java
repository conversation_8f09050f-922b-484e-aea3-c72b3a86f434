package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMasterDataCatalogVO;
import com.bjgy.query.DataGovernanceMasterDataCatalogQuery;
import com.bjgy.entity.DataGovernanceMasterDataCatalogEntity;

import java.util.List;

/**
 * 数据治理-主数据目录
 */
public interface DataGovernanceMasterDataCatalogService extends BaseService<DataGovernanceMasterDataCatalogEntity> {

	List<TreeNodeVo> listMasterModelTree();

	List<TreeNodeVo> listTree();

	PageResult<DataGovernanceMasterDataCatalogVO> page(DataGovernanceMasterDataCatalogQuery query);

	void save(DataGovernanceMasterDataCatalogVO vo);

	void update(DataGovernanceMasterDataCatalogVO vo);

	void delete(Long id);

}
