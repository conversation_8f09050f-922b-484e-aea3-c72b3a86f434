package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceLabelCategoryVO;
import com.bjgy.entity.DataGovernanceLabelCategoryEntity;

import java.util.List;

/**
 * 数据治理-标签类目
 */
public interface DataGovernanceLabelCategoryService extends BaseService<DataGovernanceLabelCategoryEntity> {

	List<TreeNodeVo> listTree();

    void save(DataGovernanceLabelCategoryVO vo);

    void update(DataGovernanceLabelCategoryVO vo);

    void delete(Long id);

	List<DataGovernanceLabelCategoryEntity> listAll();
}
