package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMetadataStandardRelEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetadataStandardRelVO;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 数据治理-元数据标准关联表
 */
public interface DataGovernanceMetadataStandardRelService extends BaseService<DataGovernanceMetadataStandardRelEntity> {

	void save(DataGovernanceMetadataStandardRelVO vo);

	void update(DataGovernanceMetadataStandardRelVO vo);

	void delete(Long metadataId, Long standardId);

	DataGovernanceMetadataStandardRelVO getMetadataRel(Long metadataId);
}
