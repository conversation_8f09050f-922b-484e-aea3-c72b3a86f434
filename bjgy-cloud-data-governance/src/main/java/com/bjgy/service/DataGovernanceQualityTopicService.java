package com.bjgy.service;

import com.bjgy.dto.CorrectTopic;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceQualityTopicVO;
import com.bjgy.query.DataGovernanceQualityTopicQuery;
import com.bjgy.entity.DataGovernanceQualityTopicEntity;

import java.util.List;

/**
 * 数据治理-预警主题库
 */
public interface DataGovernanceQualityTopicService extends BaseService<DataGovernanceQualityTopicEntity> {

    PageResult<DataGovernanceQualityTopicVO> page(DataGovernanceQualityTopicQuery query);

    void save(DataGovernanceQualityTopicVO vo);

    void update(DataGovernanceQualityTopicVO vo);

    void delete(List<Long> idList);

	void saveCorrect(CorrectTopic correctTopic);
}
