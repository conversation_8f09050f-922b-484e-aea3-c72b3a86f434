package com.bjgy.service;

import com.bjgy.dto.StandardCheckDto;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceStandardVO;
import com.bjgy.query.DataGovernanceStandardQuery;
import com.bjgy.entity.DataGovernanceStandardEntity;

import java.util.List;

/**
 * 数据治理-数据标准
 */
public interface DataGovernanceStandardService extends BaseService<DataGovernanceStandardEntity> {

    PageResult<DataGovernanceStandardVO> page(DataGovernanceStandardQuery query);

    void save(DataGovernanceStandardVO vo);

    void update(DataGovernanceStandardVO vo);

    void delete(List<Long> idList);

	void online(Long id);

	void offline(Long id);

	StandardCheckDto checkStandard(Long metadataId, Long standardId);
}
