package com.bjgy.service;

import com.bjgy.entity.DataGovernanceStandardCategoryEntity;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceStandardCategoryVO;

import java.util.List;

/**
 * 数据治理-标准目录
 */
public interface DataGovernanceStandardCategoryService extends BaseService<DataGovernanceStandardCategoryEntity> {

	List<TreeNodeVo> listTree();

    void save(DataGovernanceStandardCategoryVO vo);

    void update(DataGovernanceStandardCategoryVO vo);

    void delete(Long id);

}
