package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceMetamodelPropertyQuery;
import com.bjgy.vo.DataGovernanceMetamodelPropertyVO;
import com.bjgy.entity.DataGovernanceMetamodelPropertyEntity;

import java.util.List;

/**
 * 数据治理-元模型属性
 */
public interface DataGovernanceMetamodelPropertyService extends BaseService<DataGovernanceMetamodelPropertyEntity> {

    PageResult<DataGovernanceMetamodelPropertyVO> page(DataGovernanceMetamodelPropertyQuery query);

    void save(DataGovernanceMetamodelPropertyVO vo);

    void update(DataGovernanceMetamodelPropertyVO vo);

    void delete(List<Long> idList);

	List<DataGovernanceMetamodelPropertyVO> properties(Long metaModelId);
}
