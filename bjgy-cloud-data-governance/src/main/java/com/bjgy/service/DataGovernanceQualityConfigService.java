package com.bjgy.service;

import com.bjgy.entity.DataGovernanceQualityColumnEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceQualityConfigVO;
import com.bjgy.query.DataGovernanceQualityConfigQuery;
import com.bjgy.entity.DataGovernanceQualityConfigEntity;

import java.util.List;

/**
 * 数据治理-质量规则配置

 */
public interface DataGovernanceQualityConfigService extends BaseService<DataGovernanceQualityConfigEntity> {

    PageResult<DataGovernanceQualityConfigVO> page(DataGovernanceQualityConfigQuery query);

	DataGovernanceQualityConfigVO get(Long id);

    void save(DataGovernanceQualityConfigVO vo);

    void update(DataGovernanceQualityConfigVO vo);

    void delete(List<Long> idList);

	void online(Long id);

	void offline(Long id);

	void handRun(Long id);

	List<DataGovernanceQualityColumnEntity> queryConfigColumn(Long configId);
}
