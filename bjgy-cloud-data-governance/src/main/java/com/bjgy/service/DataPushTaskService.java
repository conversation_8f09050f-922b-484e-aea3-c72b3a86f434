package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataPushTaskQuery;
import com.bjgy.vo.DataPushTaskVO;
import com.bjgy.entity.DataPushTaskEntity;

import java.util.List;

/**
 * 数据治理-数据推送任务
 */
public interface DataPushTaskService extends BaseService<DataPushTaskEntity> {

    PageResult<DataPushTaskVO> page(DataPushTaskQuery query);

    void save(DataPushTaskVO vo);

    void update(DataPushTaskVO vo);

    void delete(List<Long> idList);

	void release(Long id);

	void cancel(Long id);

	void handRun(Long id);

	void stopHandTask(String executeNo);
}
