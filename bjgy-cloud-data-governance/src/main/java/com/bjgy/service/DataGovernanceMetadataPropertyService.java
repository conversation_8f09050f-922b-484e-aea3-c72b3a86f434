package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMetadataPropertyEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetadataPropertyVO;

import java.util.List;

/**
 * 数据治理-元数据属性值
 */
public interface DataGovernanceMetadataPropertyService extends BaseService<DataGovernanceMetadataPropertyEntity> {

	void save(DataGovernanceMetadataPropertyVO vo);

	void update(DataGovernanceMetadataPropertyVO vo);

	void delete(List<Long> idList);
}
