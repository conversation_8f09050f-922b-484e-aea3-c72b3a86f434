package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetadataCollectRecordVO;
import com.bjgy.query.DataGovernanceMetadataCollectRecordQuery;
import com.bjgy.entity.DataGovernanceMetadataCollectRecordEntity;

import java.util.List;

/**
 * 数据治理-元数据采集任务记录
 */
public interface DataGovernanceMetadataCollectRecordService extends BaseService<DataGovernanceMetadataCollectRecordEntity> {

    PageResult<DataGovernanceMetadataCollectRecordVO> page(DataGovernanceMetadataCollectRecordQuery query);

    void save(DataGovernanceMetadataCollectRecordVO vo);

    void update(DataGovernanceMetadataCollectRecordVO vo);

    void delete(List<Long> idList);

	void dealNotFinished();
}
