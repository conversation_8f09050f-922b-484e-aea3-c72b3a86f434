package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMasterModelEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMasterModelVO;

/**
 * 数据治理-主数据模型
 */
public interface DataGovernanceMasterModelService extends BaseService<DataGovernanceMasterModelEntity> {

	DataGovernanceMasterModelEntity save(DataGovernanceMasterModelVO vo);

	DataGovernanceMasterModelEntity update(DataGovernanceMasterModelVO vo);

	void delete(Long id);

	DataGovernanceMasterModelEntity getByCatalogId(Long catalogId);

	DataGovernanceMasterModelEntity release(Long id);

	DataGovernanceMasterModelEntity cancelRelease(Long id);
}
