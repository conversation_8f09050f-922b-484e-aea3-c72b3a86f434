package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMetadataEntity;
import com.bjgy.framework.common.cache.bean.Neo4jInfo;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetadataVO;

import java.util.List;

/**
 * 数据治理-元数据
 */
public interface DataGovernanceMetadataService extends BaseService<DataGovernanceMetadataEntity> {

    void save(DataGovernanceMetadataVO vo);

    void update(DataGovernanceMetadataVO vo);

    void delete(Long id);

	List<TreeNodeVo> listByPatentId(Long parentId);

	List<TreeNodeVo> listColumnByPatentId(Long parentId);

	List<TreeNodeVo> listDb();

	DataGovernanceMetadataVO get(Long id);

	List<TreeNodeVo> listByKeyword(String keyword);

	List<TreeNodeVo> listFloder();

	void deleteAll(Long id);

	void upNeo4jInfo(Neo4jInfo neo4jInfo);

	Neo4jInfo getNeo4jInfo();


}
