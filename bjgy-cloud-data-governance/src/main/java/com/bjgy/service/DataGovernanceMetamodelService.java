package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMetamodelEntity;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetamodelVO;

import java.util.List;

/**
 * 数据治理-元模型
 */
public interface DataGovernanceMetamodelService extends BaseService<DataGovernanceMetamodelEntity> {

	List<TreeNodeVo> listTree();

    void save(DataGovernanceMetamodelVO vo);

    void update(DataGovernanceMetamodelVO vo);

    void delete(Long id);

}
