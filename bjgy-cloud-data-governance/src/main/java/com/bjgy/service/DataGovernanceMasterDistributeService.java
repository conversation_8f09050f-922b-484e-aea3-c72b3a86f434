package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceMasterDistributeQuery;
import com.bjgy.vo.DataGovernanceMasterDistributeVO;
import com.bjgy.entity.DataGovernanceMasterDistributeEntity;

import java.util.List;

/**
 * 数据治理-主数据派发
 */
public interface DataGovernanceMasterDistributeService extends BaseService<DataGovernanceMasterDistributeEntity> {

    PageResult<DataGovernanceMasterDistributeVO> page(DataGovernanceMasterDistributeQuery query);

    void save(DataGovernanceMasterDistributeVO vo);

    void update(DataGovernanceMasterDistributeVO vo);

    void delete(List<Long> idList);

	void release(Long id);

	void cancel(Long id);

	void handRun(Long id);
}
