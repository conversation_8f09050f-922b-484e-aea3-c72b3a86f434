package com.bjgy.service;

import com.bjgy.dto.LabelPageDataQuery;
import com.bjgy.entity.DataGovernanceLabelEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataGovernanceLabelQuery;
import com.bjgy.vo.DataGovernanceLabelVO;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.model.JdbcSelectResult;

import java.util.List;

/**
 * 数据治理-标签
 */
public interface DataGovernanceLabelService extends BaseService<DataGovernanceLabelEntity> {

	PageResult<DataGovernanceLabelVO> page(DataGovernanceLabelQuery query);

	void save(DataGovernanceLabelVO vo);

	void update(DataGovernanceLabelVO vo);

	void delete(List<Long> idList);

	void on(Long id);

	void off(Long id);

	List<DataGovernanceLabelVO> listEnable(Long typeId);

	List<ColumnDescription> listColumn(Integer id);

	JdbcSelectResult pageDataBySql(LabelPageDataQuery labelPageDataQuery);
}
