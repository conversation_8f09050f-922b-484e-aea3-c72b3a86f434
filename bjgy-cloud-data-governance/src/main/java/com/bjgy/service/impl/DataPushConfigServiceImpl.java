package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushConfigConvert;
import com.bjgy.dao.DataPushConfigDao;
import com.bjgy.dao.DataPushTaskDao;
import com.bjgy.entity.DataPushConfigEntity;
import com.bjgy.entity.DataPushTaskEntity;
import com.bjgy.framework.common.constant.Constant;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.framework.security.user.SecurityUser;
import com.bjgy.framework.security.user.UserDetail;
import com.bjgy.query.DataPushConfigQuery;
import com.bjgy.service.DataPushConfigService;
import com.bjgy.vo.DataPushConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据治理-数据推送配置
 */
@Service
@AllArgsConstructor
public class DataPushConfigServiceImpl extends BaseServiceImpl<DataPushConfigDao, DataPushConfigEntity> implements DataPushConfigService {


	private final DataPushTaskDao dataPushTaskDao;

	@Override
	public PageResult<DataPushConfigVO> page(DataPushConfigQuery query) {
		IPage<DataPushConfigEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataPushConfigConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataPushConfigEntity> getWrapper(DataPushConfigQuery query) {
		UserDetail user = SecurityUser.getUser();
		List<Long> dataScopeList = user.getDataScopeList() != null ? user.getDataScopeList() : new ArrayList<>();
		LambdaQueryWrapper<DataPushConfigEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getBuiltIn() != null, DataPushConfigEntity::getBuiltIn, query.getBuiltIn())
				.like(StringUtil.isNotBlank(query.getName()), DataPushConfigEntity::getName, query.getName())
				.eq(DataPushConfigEntity::getProjectId, getProjectId()).or().eq(DataPushConfigEntity::getProjectId, -1);
		if (!user.getSuperAdmin().equals(Constant.SUPER_ADMIN)) {
			dataScopeList.add(-1L);
			wrapper.in(DataPushConfigEntity::getOrgId, dataScopeList);
		}
		return wrapper;
	}

	@Override
	public void save(DataPushConfigVO vo) {
		DataPushConfigEntity entity = DataPushConfigConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataPushConfigVO vo) {
		DataPushConfigEntity entity = DataPushConfigConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id) {
		//判断是否有任务引用
		LambdaQueryWrapper<DataPushTaskEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataPushTaskEntity::getDataPushConfigId, id).last("limit 1");
		DataPushTaskEntity taskEntity = dataPushTaskDao.selectOne(wrapper);
		if (taskEntity != null) {
			throw new ServerException("存在数据推送任务与之关联，不可删除");
		}
		removeById(id);
	}

}
