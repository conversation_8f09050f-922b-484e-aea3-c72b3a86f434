package com.bjgy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceQualityLogConvert;
import com.bjgy.entity.DataGovernanceQualityLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceQualityLogQuery;
import com.bjgy.vo.DataGovernanceQualityLogVO;
import com.bjgy.dao.DataGovernanceQualityLogDao;
import com.bjgy.service.DataGovernanceQualityLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.Date;
import java.util.List;

/**
 * 质量规则-预警日志
 */
@Service
@AllArgsConstructor
public class DataGovernanceQualityLogServiceImpl extends BaseServiceImpl<DataGovernanceQualityLogDao, DataGovernanceQualityLogEntity> implements DataGovernanceQualityLogService {

	@Override
	public PageResult<DataGovernanceQualityLogVO> page(DataGovernanceQualityLogQuery query) {
		IPage<DataGovernanceQualityLogEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceQualityLogConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceQualityLogEntity> getWrapper(DataGovernanceQualityLogQuery query) {
		LambdaQueryWrapper<DataGovernanceQualityLogEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(ObjectUtil.isNotNull(query.getQualityConfigId()),DataGovernanceQualityLogEntity::getQualityConfigId,query.getQualityConfigId())
				.like(StringUtil.isNotBlank(query.getQualityConfigName()), DataGovernanceQualityLogEntity::getQualityConfigName, query.getQualityConfigName())
				.eq(query.getQualityType() != null, DataGovernanceQualityLogEntity::getQualityType, query.getQualityType())
				.like(StringUtil.isNotBlank(query.getDataProv()), DataGovernanceQualityLogEntity::getDataProv, query.getDataProv())
				.gt(query.getCheckStartTime() != null, DataGovernanceQualityLogEntity::getCheckStartTime, query.getCheckStartTime())
				.orderByDesc(DataGovernanceQualityLogEntity::getId);
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceQualityLogVO vo) {
		DataGovernanceQualityLogEntity entity = DataGovernanceQualityLogConvert.INSTANCE.convert(vo);
		baseMapper.insert(entity);
		vo.setId(entity.getId());
	}

	@Override
	public void update(DataGovernanceQualityLogVO vo) {
		DataGovernanceQualityLogEntity entity = DataGovernanceQualityLogConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public void dealNotFinished() {
		LambdaQueryWrapper<DataGovernanceQualityLogEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceQualityLogEntity::getStatus, 0);
		List<DataGovernanceQualityLogEntity> logEntities = baseMapper.selectList(wrapper);
		for (DataGovernanceQualityLogEntity logEntity : logEntities) {
			logEntity.setCheckEndTime(new Date());
			logEntity.setStatus(2);
			baseMapper.updateById(logEntity);
		}
	}

}
