package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.quartz.QuartzDataPushTaskApi;
import com.bjgy.convert.DataPushTaskConvert;
import com.bjgy.dao.DataPushIncreaseLogDao;
import com.bjgy.dao.DataPushTaskDao;
import com.bjgy.dao.DataPushTaskLogDao;
import com.bjgy.entity.DataPushIncreaseLogEntity;
import com.bjgy.entity.DataPushTaskEntity;
import com.bjgy.entity.DataPushTaskLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.framework.security.user.SecurityUser;
import com.bjgy.query.DataPushTaskQuery;
import com.bjgy.service.DataPushTaskService;
import com.bjgy.vo.DataPushTaskVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.Date;
import java.util.List;

/**
 * 数据治理-数据推送任务
 */
@Service
@AllArgsConstructor
public class DataPushTaskServiceImpl extends BaseServiceImpl<DataPushTaskDao, DataPushTaskEntity> implements DataPushTaskService {

	private final QuartzDataPushTaskApi dataPushTaskApi;
	private final DataPushTaskLogDao dataPushTaskLogDao;
	private final DataPushIncreaseLogDao dataPushIncreaseLogDao;

	@Override
	public PageResult<DataPushTaskVO> page(DataPushTaskQuery query) {
		IPage<DataPushTaskEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataPushTaskConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataPushTaskEntity> getWrapper(DataPushTaskQuery query) {
		LambdaQueryWrapper<DataPushTaskEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataPushTaskEntity::getName, query.getName())
				.eq(query.getPushType() != null, DataPushTaskEntity::getPushType, query.getPushType())
				.eq(query.getIncrementFlag() != null, DataPushTaskEntity::getIncrementFlag, query.getIncrementFlag())
				.eq(query.getTaskType() != null, DataPushTaskEntity::getTaskType, query.getTaskType())
				.eq(query.getStatus() != null, DataPushTaskEntity::getStatus, query.getStatus());
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataPushTaskVO vo) {
		DataPushTaskEntity entity = DataPushTaskConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataPushTaskVO vo) {
		DataPushTaskEntity entity = DataPushTaskConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
		LambdaQueryWrapper<DataPushTaskLogEntity> logWrapper = Wrappers.lambdaQuery();
		logWrapper.in(DataPushTaskLogEntity::getDataPushTaskId, idList);
		dataPushTaskLogDao.delete(logWrapper);
		LambdaQueryWrapper<DataPushIncreaseLogEntity> increaseLogWrapper = Wrappers.lambdaQuery();
		increaseLogWrapper.in(DataPushIncreaseLogEntity::getDataPushTaskId, idList);
		dataPushIncreaseLogDao.delete(increaseLogWrapper);
	}

	@Override
	public void release(Long id) {
		DataPushTaskEntity dataPushTaskEntity = baseMapper.selectById(id);
		dataPushTaskApi.releaseAccess(id);
		dataPushTaskEntity.setStatus(1);
		dataPushTaskEntity.setReleaseTime(new Date());
		dataPushTaskEntity.setReleaseUserId(SecurityUser.getUserId());
		baseMapper.updateById(dataPushTaskEntity);
	}

	@Override
	public void cancel(Long id) {
		DataPushTaskEntity dataPushTaskEntity = baseMapper.selectById(id);
		dataPushTaskApi.cancleAccess(id);
		dataPushTaskEntity.setStatus(0);
		dataPushTaskEntity.setReleaseTime(null);
		dataPushTaskEntity.setReleaseUserId(null);
		baseMapper.updateById(dataPushTaskEntity);
	}

	@Override
	public void handRun(Long id) {
		dataPushTaskApi.handRun(id);
	}

	@Override
	public void stopHandTask(String executeNo) {
		dataPushTaskApi.stopHandTask(executeNo);
	}

}
