package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceStandardCodeConvert;
import com.bjgy.dao.DataGovernanceStandardCodeDao;
import com.bjgy.dao.DataGovernanceStandardDao;
import com.bjgy.entity.DataGovernanceStandardCodeEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceStandardCodeQuery;
import com.bjgy.service.DataGovernanceStandardCodeService;
import com.bjgy.vo.DataGovernanceStandardCodeVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-标准码表数据
 */
@Service
@AllArgsConstructor
public class DataGovernanceStandardCodeServiceImpl extends BaseServiceImpl<DataGovernanceStandardCodeDao, DataGovernanceStandardCodeEntity> implements DataGovernanceStandardCodeService {

	private final DataGovernanceStandardDao standardDao;
	@Override
	public PageResult<DataGovernanceStandardCodeVO> page(DataGovernanceStandardCodeQuery query) {
		IPage<DataGovernanceStandardCodeEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceStandardCodeConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceStandardCodeEntity> getWrapper(DataGovernanceStandardCodeQuery query) {
		LambdaQueryWrapper<DataGovernanceStandardCodeEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getStandardId() != null, DataGovernanceStandardCodeEntity::getStandardId, query.getStandardId())
				.eq(StringUtil.isNotBlank(query.getDataId()), DataGovernanceStandardCodeEntity::getDataId, query.getDataId())
				.eq(StringUtil.isNotBlank(query.getDataName()), DataGovernanceStandardCodeEntity::getDataName, query.getDataName());
		return wrapper;
	}

	@Override
	public void save(DataGovernanceStandardCodeVO vo) {
		DataGovernanceStandardCodeEntity entity = DataGovernanceStandardCodeConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
		standardDao.updateCodeNumByStandardId(vo.getStandardId());
	}

	@Override
	public void update(DataGovernanceStandardCodeVO vo) {
		DataGovernanceStandardCodeEntity entity = DataGovernanceStandardCodeConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		Long id = idList.get(0);
		DataGovernanceStandardCodeEntity standardCodeEntity = baseMapper.selectById(id);
		removeByIds(idList);
		standardDao.updateCodeNumByStandardId(standardCodeEntity.getStandardId());
	}

}
