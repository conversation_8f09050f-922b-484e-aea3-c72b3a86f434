package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.quartz.QuartzDataGovernanceQualityApi;
import com.bjgy.convert.DataGovernanceQualityColumnConvert;
import com.bjgy.convert.DataGovernanceQualityConfigConvert;
import com.bjgy.dao.DataGovernanceQualityColumnDao;
import com.bjgy.dao.DataGovernanceQualityConfigCategoryDao;
import com.bjgy.dao.DataGovernanceQualityConfigDao;
import com.bjgy.dao.DataGovernanceQualityTopicDao;
import com.bjgy.entity.DataGovernanceQualityColumnEntity;
import com.bjgy.entity.DataGovernanceQualityConfigCategoryEntity;
import com.bjgy.entity.DataGovernanceQualityConfigEntity;
import com.bjgy.entity.DataGovernanceQualityTopicEntity;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceQualityConfigQuery;
import com.bjgy.service.DataGovernanceQualityConfigService;
import com.bjgy.vo.DataGovernanceQualityColumnVO;
import com.bjgy.vo.DataGovernanceQualityConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-质量规则配置
 */
@Service
@AllArgsConstructor
public class DataGovernanceQualityConfigServiceImpl extends BaseServiceImpl<DataGovernanceQualityConfigDao, DataGovernanceQualityConfigEntity> implements DataGovernanceQualityConfigService {

	private final QuartzDataGovernanceQualityApi quartzDataGovernanceQualityApi;
	private final DataGovernanceQualityConfigCategoryDao categoryDao;
	private final DataGovernanceQualityColumnDao qualityColumnDao;
	private final DataGovernanceQualityTopicDao qualityTopicDao;

	@Override
	public PageResult<DataGovernanceQualityConfigVO> page(DataGovernanceQualityConfigQuery query) {
		IPage<DataGovernanceQualityConfigEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceQualityConfigConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	@Override
	public DataGovernanceQualityConfigVO get(Long id) {
		DataGovernanceQualityConfigVO configVO = DataGovernanceQualityConfigConvert.INSTANCE.convert(baseMapper.selectById(id));
		LambdaQueryWrapper<DataGovernanceQualityColumnEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceQualityColumnEntity::getQualityConfigId, id);
		configVO.setColumnConfig(DataGovernanceQualityColumnConvert.INSTANCE.convertList(qualityColumnDao.selectList(wrapper)));
		return configVO;
	}

	private LambdaQueryWrapper<DataGovernanceQualityConfigEntity> getWrapper(DataGovernanceQualityConfigQuery query) {
		LambdaQueryWrapper<DataGovernanceQualityConfigEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getCategoryId() != null, DataGovernanceQualityConfigEntity::getCategoryId, query.getCategoryId())
				.like(StringUtil.isNotBlank(query.getName()), DataGovernanceQualityConfigEntity::getName, query.getName())
				.like(StringUtil.isNotBlank(query.getDataProv()), DataGovernanceQualityConfigEntity::getDataProv, query.getDataProv())
				.like(StringUtil.isNotBlank(query.getDutyUser()), DataGovernanceQualityConfigEntity::getDutyUser, query.getDutyUser())
				.like(StringUtil.isNotBlank(query.getDutyPhone()), DataGovernanceQualityConfigEntity::getDutyPhone, query.getDutyPhone())
				.like(StringUtil.isNotBlank(query.getDutyEmail()), DataGovernanceQualityConfigEntity::getDutyEmail, query.getDutyEmail())
				.eq(query.getStatus() != null, DataGovernanceQualityConfigEntity::getStatus, query.getStatus())
				.eq(query.getTaskType() != null, DataGovernanceQualityConfigEntity::getTaskType, query.getTaskType())
				.eq(query.getQualityType() != null, DataGovernanceQualityConfigEntity::getQualityType, query.getQualityType())
				.orderByDesc(DataGovernanceQualityConfigEntity::getId);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceQualityConfigVO vo) {
		List<DataGovernanceQualityColumnVO> columnConfig = vo.getColumnConfig();
		DataGovernanceQualityConfigCategoryEntity categoryEntity = categoryDao.selectById(vo.getCategoryId());
		DataGovernanceQualityConfigEntity entity = DataGovernanceQualityConfigConvert.INSTANCE.convert(vo);
		entity.setOrgId(categoryEntity.getOrgId());
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
		if (CollectionUtils.isEmpty(columnConfig)) {
			throw new ServerException("字段配置不可为空！");
		}
		for (DataGovernanceQualityColumnVO dataGovernanceQualityColumnVO : columnConfig) {
			dataGovernanceQualityColumnVO.setQualityConfigId(entity.getId());
			qualityColumnDao.insert(DataGovernanceQualityColumnConvert.INSTANCE.convert(dataGovernanceQualityColumnVO));
		}
	}

	@Override
	public void update(DataGovernanceQualityConfigVO vo) {
		List<DataGovernanceQualityColumnVO> columnConfig = vo.getColumnConfig();
		if (CollectionUtils.isEmpty(columnConfig)) {
			throw new ServerException("字段配置不可为空！");
		}
		LambdaUpdateWrapper<DataGovernanceQualityColumnEntity> delWrapper = Wrappers.lambdaUpdate();
		delWrapper.eq(DataGovernanceQualityColumnEntity::getQualityConfigId, vo.getId());
		qualityColumnDao.delete(delWrapper);
		for (DataGovernanceQualityColumnVO dataGovernanceQualityColumnVO : columnConfig) {
			dataGovernanceQualityColumnVO.setQualityConfigId(vo.getId());
			dataGovernanceQualityColumnVO.setId(null);
			qualityColumnDao.insert(DataGovernanceQualityColumnConvert.INSTANCE.convert(dataGovernanceQualityColumnVO));
		}
		DataGovernanceQualityConfigCategoryEntity categoryEntity = categoryDao.selectById(vo.getCategoryId());
		DataGovernanceQualityConfigEntity entity = DataGovernanceQualityConfigConvert.INSTANCE.convert(vo);
		entity.setOrgId(categoryEntity.getOrgId());
		entity.setProjectId(getProjectId());
		updateById(entity);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		for (Long id : idList) {
			quartzDataGovernanceQualityApi.cancel(id);
			//删除字段配置，删除日志
			LambdaUpdateWrapper<DataGovernanceQualityColumnEntity> delColumnWrapper = Wrappers.lambdaUpdate();
			delColumnWrapper.eq(DataGovernanceQualityColumnEntity::getQualityConfigId, id);
			qualityColumnDao.delete(delColumnWrapper);
			LambdaUpdateWrapper<DataGovernanceQualityTopicEntity> delTopicWrapper = Wrappers.lambdaUpdate();
			delTopicWrapper.eq(DataGovernanceQualityTopicEntity::getQualityConfigId, id);
			qualityTopicDao.delete(delTopicWrapper);
		}
		removeByIds(idList);
	}

	@Override
	public void online(Long id) {
		DataGovernanceQualityConfigEntity configEntity = baseMapper.selectById(id);
		configEntity.setStatus(1);
		quartzDataGovernanceQualityApi.release(id);
		baseMapper.updateById(configEntity);
	}

	@Override
	public void offline(Long id) {
		DataGovernanceQualityConfigEntity configEntity = baseMapper.selectById(id);
		configEntity.setStatus(0);
		quartzDataGovernanceQualityApi.cancel(id);
		baseMapper.updateById(configEntity);
	}

	@Override
	public void handRun(Long id) {
		quartzDataGovernanceQualityApi.handRun(id);
	}

	@Override
	public List<DataGovernanceQualityColumnEntity> queryConfigColumn(Long configId) {
		LambdaQueryWrapper<DataGovernanceQualityColumnEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceQualityColumnEntity::getQualityConfigId,configId);
		return qualityColumnDao.selectList(wrapper);
	}

}
