package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelTypeConvert;
import com.bjgy.dao.DataGovernanceLabelTypeDao;
import com.bjgy.entity.DataGovernanceLabelTypeEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceLabelTypeQuery;
import com.bjgy.vo.DataGovernanceLabelTypeVO;
import com.bjgy.service.DataGovernanceLabelTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-标签类型
 */
@Service
@AllArgsConstructor
public class DataGovernanceLabelTypeServiceImpl extends BaseServiceImpl<DataGovernanceLabelTypeDao, DataGovernanceLabelTypeEntity> implements DataGovernanceLabelTypeService {

	@Override
	public PageResult<DataGovernanceLabelTypeVO> page(DataGovernanceLabelTypeQuery query) {
		IPage<DataGovernanceLabelTypeEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceLabelTypeConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceLabelTypeEntity> getWrapper(DataGovernanceLabelTypeQuery query) {
		LambdaQueryWrapper<DataGovernanceLabelTypeEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataGovernanceLabelTypeEntity::getName, query.getName());
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceLabelTypeVO vo) {
		DataGovernanceLabelTypeEntity entity = DataGovernanceLabelTypeConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceLabelTypeVO vo) {
		DataGovernanceLabelTypeEntity entity = DataGovernanceLabelTypeConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		//TODO，判断有无关联
		removeByIds(idList);
	}

	@Override
	public List<DataGovernanceLabelTypeEntity> listAll() {
		LambdaQueryWrapper<DataGovernanceLabelTypeEntity> wrapper = new LambdaQueryWrapper<>();
		dataScopeWithOrgId(wrapper);
		return list(wrapper);
	}

}
