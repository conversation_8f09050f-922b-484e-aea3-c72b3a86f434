package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceStandardCleanLogConvert;
import com.bjgy.entity.DataGovernanceStandardCleanLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceStandardCleanLogQuery;
import com.bjgy.vo.DataGovernanceStandardCleanLogVO;
import com.bjgy.dao.DataGovernanceStandardCleanLogDao;
import com.bjgy.service.DataGovernanceStandardCleanLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-标准清洗日志
 */
@Service
@AllArgsConstructor
public class DataGovernanceStandardCleanLogServiceImpl extends BaseServiceImpl<DataGovernanceStandardCleanLogDao, DataGovernanceStandardCleanLogEntity> implements DataGovernanceStandardCleanLogService {

	@Override
	public PageResult<DataGovernanceStandardCleanLogVO> page(DataGovernanceStandardCleanLogQuery query) {
		IPage<DataGovernanceStandardCleanLogEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceStandardCleanLogConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceStandardCleanLogEntity> getWrapper(DataGovernanceStandardCleanLogQuery query) {
		LambdaQueryWrapper<DataGovernanceStandardCleanLogEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getMetadataId() != null, DataGovernanceStandardCleanLogEntity::getMetadataId, query.getMetadataId())
				.like(StringUtil.isNotBlank(query.getStandardCleanName()), DataGovernanceStandardCleanLogEntity::getStandardCleanName, query.getStandardCleanName())
				.eq(query.getStatus() != null, DataGovernanceStandardCleanLogEntity::getStatus, query.getStatus())
				.ge(query.getStartTime() != null, DataGovernanceStandardCleanLogEntity::getStartTime, query.getStartTime())
				.le(query.getEndTime() != null, DataGovernanceStandardCleanLogEntity::getEndTime, query.getEndTime())
				.orderByDesc(DataGovernanceStandardCleanLogEntity::getId);
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceStandardCleanLogVO vo) {
		DataGovernanceStandardCleanLogEntity entity = DataGovernanceStandardCleanLogConvert.INSTANCE.convert(vo);

		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceStandardCleanLogVO vo) {
		DataGovernanceStandardCleanLogEntity entity = DataGovernanceStandardCleanLogConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

}
