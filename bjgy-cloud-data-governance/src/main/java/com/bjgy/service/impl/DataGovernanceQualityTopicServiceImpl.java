package com.bjgy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.data.integrate.DataDatabaseApi;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.convert.DataGovernanceQualityTopicConvert;
import com.bjgy.dao.DataGovernanceMetadataDao;
import com.bjgy.dao.DataGovernanceQualityConfigDao;
import com.bjgy.dao.DataGovernanceQualityTopicDao;
import com.bjgy.dto.CorrectTopic;
import com.bjgy.entity.DataGovernanceMetadataEntity;
import com.bjgy.entity.DataGovernanceQualityConfigEntity;
import com.bjgy.entity.DataGovernanceQualityTopicEntity;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.DateUtils;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.framework.security.user.SecurityUser;
import com.bjgy.query.DataGovernanceQualityTopicQuery;
import com.bjgy.service.DataGovernanceQualityTopicService;
import com.bjgy.vo.DataGovernanceQualityTopicVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据治理-预警主题库
 */
@Service
@AllArgsConstructor
public class DataGovernanceQualityTopicServiceImpl extends BaseServiceImpl<DataGovernanceQualityTopicDao, DataGovernanceQualityTopicEntity> implements DataGovernanceQualityTopicService {


	private final DataGovernanceQualityConfigDao qualityConfigDao;
	private final DataGovernanceMetadataDao metadataDao;
	private final DataDatabaseApi databaseApi;

	@Override
	public PageResult<DataGovernanceQualityTopicVO> page(DataGovernanceQualityTopicQuery query) {
		IPage<DataGovernanceQualityTopicEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceQualityTopicConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceQualityTopicEntity> getWrapper(DataGovernanceQualityTopicQuery query) {
		LambdaQueryWrapper<DataGovernanceQualityTopicEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(ObjectUtil.isNotNull(query.getQualityLogId()),DataGovernanceQualityTopicEntity::getQualityLogId,query.getQualityLogId())
				.like(StringUtil.isNotBlank(query.getQualityConfigName()), DataGovernanceQualityTopicEntity::getQualityConfigName, query.getQualityConfigName())
				.eq(query.getQualityType() != null, DataGovernanceQualityTopicEntity::getQualityType, query.getQualityType())
				.like(StringUtil.isNotBlank(query.getDataProv()), DataGovernanceQualityTopicEntity::getDataProv, query.getDataProv())
				.orderByDesc(DataGovernanceQualityTopicEntity::getId);
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceQualityTopicVO vo) {
		DataGovernanceQualityTopicEntity entity = DataGovernanceQualityTopicConvert.INSTANCE.convert(vo);

		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceQualityTopicVO vo) {
		DataGovernanceQualityTopicEntity entity = DataGovernanceQualityTopicConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void saveCorrect(CorrectTopic correctTopic) {
		DataGovernanceQualityTopicEntity topic = baseMapper.selectById(correctTopic.getId());
		topic.setCorrectValue(correctTopic.getCorrectValue());
		topic.setCorrectUser(SecurityUser.getUser().getUsername());
		topic.setCorrectStatus(1);
		topic.setCorrectTime(new Date());
		baseMapper.updateById(topic);
		//更新库里的数据
		DataGovernanceQualityConfigEntity qualityConfigEntity = qualityConfigDao.selectById(topic.getQualityConfigId());
		Long sourceTableMetaId = qualityConfigEntity.getSourceTableMetaId();
		DataGovernanceMetadataEntity metadataEntity = metadataDao.selectById(sourceTableMetaId);
		Long datasourceId = metadataEntity.getDatasourceId();
		IMetaDataByJdbcService metaDataService;
		DataDatabaseDto dataDatabaseDto = new DataDatabaseDto();
		if (datasourceId == -1) {
			Long projectId = metadataEntity.getProjectId();
			DataProjectCacheBean project = getProject(projectId);
			dataDatabaseDto.setJdbcUrl(project.getDbUrl());
			dataDatabaseDto.setDatabaseType(project.getDbType());
			dataDatabaseDto.setDatabaseSchema(project.getDbSchema());
			dataDatabaseDto.setUserName(project.getDbUsername());
			dataDatabaseDto.setPassword(project.getDbPassword());
		} else {
			dataDatabaseDto = databaseApi.getById(datasourceId).getData();
		}
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(dataDatabaseDto.getDatabaseType());
		metaDataService = new MetaDataByJdbcServiceImpl(productTypeEnum);
		List<Object> params = new ArrayList<>();
		String errorColumnType = topic.getErrorColumnType();
		String sourceTablePkType = topic.getSourceTablePkType();
		params.add(getParam(correctTopic, errorColumnType));
		params.add(getPk(topic, sourceTablePkType));
		metaDataService.executeSql(dataDatabaseDto.getJdbcUrl(), dataDatabaseDto.getUserName(), dataDatabaseDto.getPassword(),
				String.format("UPDATE %s.%s SET %s=? WHERE %s=?", dataDatabaseDto.getDatabaseSchema(), qualityConfigEntity.getSourceTableName(), topic.getErrorColumn(), qualityConfigEntity.getSourceTablePk()), params);
	}

	private Object getParam(CorrectTopic correctTopic, String columnType) {
		if ("java.lang.Integer".equals(columnType)) {
			return Integer.parseInt(correctTopic.getCorrectValue());
		} else if ("java.lang.Double".equals(columnType)) {
			return Double.parseDouble(correctTopic.getCorrectValue());
		} else if ("java.lang.Float".equals(columnType)) {
			return Float.parseFloat(correctTopic.getCorrectValue());
		} else if ("java.sql.Date".equals(columnType)) {
			return DateUtils.parse(correctTopic.getCorrectValue(), DateUtils.DATE_PATTERN);
		} else if ("java.sql.Time".equals(columnType)) {
			return DateUtils.parse(correctTopic.getCorrectValue(), DateUtils.TIME_PATTERN);
		} else if ("java.sql.Timestamp".equals(columnType)) {
			return DateUtils.parse(correctTopic.getCorrectValue(), DateUtils.DATE_TIME_PATTERN);
		} else {
			return correctTopic.getCorrectValue();
		}
	}

	private Object getPk(DataGovernanceQualityTopicEntity topic, String columnType) {
		if ("java.lang.Integer".equals(columnType)) {
			return Integer.parseInt(topic.getDataPk());
		} else if ("java.lang.Double".equals(columnType)) {
			return Double.parseDouble(topic.getDataPk());
		} else if ("java.lang.Float".equals(columnType)) {
			return Float.parseFloat(topic.getDataPk());
		} else if ("java.sql.Date".equals(columnType)) {
			return DateUtils.parse(topic.getDataPk(), DateUtils.DATE_PATTERN);
		} else if ("java.sql.Time".equals(columnType)) {
			return DateUtils.parse(topic.getDataPk(), DateUtils.TIME_PATTERN);
		} else if ("java.sql.Timestamp".equals(columnType)) {
			return DateUtils.parse(topic.getDataPk(), DateUtils.DATE_TIME_PATTERN);
		} else {
			return topic.getDataPk();
		}
	}

}
