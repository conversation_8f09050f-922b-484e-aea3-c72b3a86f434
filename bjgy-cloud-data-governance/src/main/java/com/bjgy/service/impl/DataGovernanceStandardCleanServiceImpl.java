package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.DataDatabaseApi;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.api.module.quartz.QuartzDataGovernanceStandardApi;
import com.bjgy.convert.DataGovernanceMetadataStandardRelConvert;
import com.bjgy.convert.DataGovernanceStandardCleanConvert;
import com.bjgy.convert.DataGovernanceStandardCodeConvert;
import com.bjgy.dao.DataGovernanceMetadataDao;
import com.bjgy.dao.DataGovernanceMetadataStandardRelDao;
import com.bjgy.dao.DataGovernanceStandardCleanDao;
import com.bjgy.dao.DataGovernanceStandardCodeDao;
import com.bjgy.entity.DataGovernanceMetadataEntity;
import com.bjgy.entity.DataGovernanceMetadataStandardRelEntity;
import com.bjgy.entity.DataGovernanceStandardCleanEntity;
import com.bjgy.entity.DataGovernanceStandardCodeEntity;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceStandardCleanQuery;
import com.bjgy.service.DataGovernanceStandardCleanService;
import com.bjgy.vo.DataGovernanceMetadataStandardRelVO;
import com.bjgy.vo.DataGovernanceStandardCleanVO;
import com.bjgy.vo.DataGovernanceStandardCodeVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据治理-标准清洗
 */
@Service
@AllArgsConstructor
public class DataGovernanceStandardCleanServiceImpl extends BaseServiceImpl<DataGovernanceStandardCleanDao, DataGovernanceStandardCleanEntity> implements DataGovernanceStandardCleanService {

	private final DataGovernanceMetadataDao metadataDao;
	private final DataGovernanceMetadataStandardRelDao metadataStandardRelDao;
	private final DataGovernanceStandardCodeDao standardCodeDao;
	private final DataDatabaseApi databaseApi;
	private final QuartzDataGovernanceStandardApi quartzDataGovernanceStandardApi;

	@Override
	public PageResult<DataGovernanceStandardCleanVO> page(DataGovernanceStandardCleanQuery query) {
		IPage<DataGovernanceStandardCleanEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceStandardCleanConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceStandardCleanEntity> getWrapper(DataGovernanceStandardCleanQuery query) {
		LambdaQueryWrapper<DataGovernanceStandardCleanEntity> wrapper = Wrappers.lambdaQuery();

		return wrapper;
	}

	@Override
	public DataGovernanceStandardCleanVO save(DataGovernanceStandardCleanVO vo) {
		DataGovernanceStandardCleanEntity entity = DataGovernanceStandardCleanConvert.INSTANCE.convert(vo);
		DataGovernanceMetadataEntity metadataEntity = metadataDao.selectById(vo.getMetadataId());
		entity.setProjectId(metadataEntity.getProjectId());
		entity.setOrgId(metadataEntity.getOrgId());
		baseMapper.insert(entity);
		return DataGovernanceStandardCleanConvert.INSTANCE.convert(entity);
	}

	@Override
	public DataGovernanceStandardCleanVO update(DataGovernanceStandardCleanVO vo) {
		DataGovernanceStandardCleanEntity entity = DataGovernanceStandardCleanConvert.INSTANCE.convert(vo);
		DataGovernanceMetadataEntity metadataEntity = metadataDao.selectById(vo.getMetadataId());
		entity.setProjectId(metadataEntity.getProjectId());
		entity.setOrgId(metadataEntity.getOrgId());
		updateById(entity);
		return DataGovernanceStandardCleanConvert.INSTANCE.convert(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public DataGovernanceStandardCleanEntity getByMetadataId(Long metadataId) {
		LambdaQueryWrapper<DataGovernanceStandardCleanEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceStandardCleanEntity::getMetadataId, metadataId).last("limit 1");
		return baseMapper.selectOne(wrapper);
	}

	@SneakyThrows
	@Override
	public String getMetadataColumnType(Long metadataId) {
		DataGovernanceMetadataEntity columnMetadata = metadataDao.selectById(metadataId);
		//获取表元数据
		DataGovernanceMetadataEntity tableMetadata = metadataDao.selectById(columnMetadata.getParentId());
		Long datasourceId = columnMetadata.getDatasourceId();
		List<ColumnDescription> columnDescriptions;
		DataDatabaseDto databaseDto = new DataDatabaseDto();
		if (datasourceId == -1) {
			DataProjectCacheBean project = getProject();
			databaseDto.setJdbcUrl(project.getDbUrl());
			databaseDto.setUserName(project.getDbUsername());
			databaseDto.setPassword(project.getDbPassword());
			databaseDto.setDatabaseType(project.getDbType());
			databaseDto.setDatabaseSchema(project.getDbSchema());
		} else {
			databaseDto = databaseApi.getById(datasourceId).getData();
		}
		if (databaseDto == null) {
			throw new ServerException("该表元数据对应的物理表不存在，请及时更新元数据信息！");
		}
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(databaseDto.getDatabaseType());
		IMetaDataByJdbcService service = new MetaDataByJdbcServiceImpl(productTypeEnum);
		columnDescriptions = service.queryTableColumnMeta(databaseDto.getJdbcUrl(), databaseDto.getUserName(), databaseDto.getPassword(), databaseDto.getDatabaseSchema(), tableMetadata.getCode());
		ColumnDescription columnDescription = columnDescriptions.stream().filter(item -> item.getFieldName().equals(columnMetadata.getCode())).findFirst().orElse(null);
		if (columnDescription == null) {
			throw new ServerException("该表元数据对应的物理字段不存在，请及时更新元数据信息！");
		}
		return columnDescription.getFiledTypeClassName();
		/*//获取字段值
		String sql = String.format("SELECT %s FROM %s.%s GROUP BY %s", columnMetadata.getCode(), databaseDto.getDatabaseSchema(), tableMetadata.getCode(), columnMetadata.getCode());
		SchemaTableData schemaTableData = service.queryTableDataBySql(databaseDto.getJdbcUrl(), databaseDto.getUserName(), databaseDto.getPassword(), sql, 100);
		List<List<Object>> rows = schemaTableData.getRows();
		List<CleanConfig> cleanConfigs = new ArrayList<>();
		for (List<Object> row : rows) {
			CleanConfig cleanConfig = new CleanConfig();
			cleanConfig.setClassType(columnDescription.getFiledTypeClassName());
			cleanConfig.setOldValue(row.get(0) == null ? "NULL" : row.get(0));
			if (standardCleanEntity != null) {
				List<CleanConfig> dbConfigs = SingletonObject.OBJECT_MAPPER.readValue(SingletonObject.OBJECT_MAPPER.writeValueAsString(standardCleanEntity.getCleanConfig()), new TypeReference<List<CleanConfig>>() {
				});
				for (CleanConfig dbConfig : dbConfigs) {
					if (dbConfig.getOldValue().equals(cleanConfig.getOldValue())) {
						cleanConfig.setNewValue(dbConfig.getNewValue());
					}
				}
			}
			cleanConfigs.add(cleanConfig);
		}*/
		//return cleanConfigs;
	}

	@Override
	public List<DataGovernanceStandardCodeVO> listStandardCode(Long metadataId) {
		LambdaQueryWrapper<DataGovernanceMetadataStandardRelEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceMetadataStandardRelEntity::getMetadataId, metadataId).last("limit 1");
		DataGovernanceMetadataStandardRelEntity standardRelEntity = metadataStandardRelDao.selectOne(wrapper);
		if (standardRelEntity != null) {
			LambdaQueryWrapper<DataGovernanceStandardCodeEntity> codeWrapper = Wrappers.lambdaQuery();
			codeWrapper.eq(DataGovernanceStandardCodeEntity::getStandardId, standardRelEntity.getStandardId());
			return DataGovernanceStandardCodeConvert.INSTANCE.convertList(standardCodeDao.selectList(codeWrapper));
		}
		return new ArrayList<>();
	}

	@Override
	public DataGovernanceMetadataStandardRelVO getMetadataStandardRel(Long metadataId) {
		LambdaQueryWrapper<DataGovernanceMetadataStandardRelEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceMetadataStandardRelEntity::getMetadataId, metadataId).last("limit 1");
		DataGovernanceMetadataStandardRelEntity standardRelEntity = metadataStandardRelDao.selectOne(wrapper);
		return DataGovernanceMetadataStandardRelConvert.INSTANCE.convert(standardRelEntity);
	}

	@Override
	public DataGovernanceStandardCleanVO release(Long id) {
		quartzDataGovernanceStandardApi.release(id);
		DataGovernanceStandardCleanEntity standardCleanEntity = baseMapper.selectById(id);
		standardCleanEntity.setReleaseTime(new Date());
		standardCleanEntity.setStatus(1);
		baseMapper.updateById(standardCleanEntity);
		return DataGovernanceStandardCleanConvert.INSTANCE.convert(standardCleanEntity);
	}

	@Override
	public DataGovernanceStandardCleanVO cancel(Long id) {
		quartzDataGovernanceStandardApi.cancel(id);
		DataGovernanceStandardCleanEntity standardCleanEntity = baseMapper.selectById(id);
		standardCleanEntity.setReleaseTime(null);
		standardCleanEntity.setStatus(0);
		baseMapper.updateById(standardCleanEntity);
		return DataGovernanceStandardCleanConvert.INSTANCE.convert(standardCleanEntity);
	}

	@Override
	public void handRun(Long id) {
		quartzDataGovernanceStandardApi.handRun(id);
	}
}
