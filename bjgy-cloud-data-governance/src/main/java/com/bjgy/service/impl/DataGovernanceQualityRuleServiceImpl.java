package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceQualityRuleConvert;
import com.bjgy.dao.DataGovernanceQualityRuleDao;
import com.bjgy.entity.DataGovernanceQualityRuleEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceQualityRuleQuery;
import com.bjgy.service.DataGovernanceQualityRuleService;
import com.bjgy.vo.DataGovernanceQualityRuleVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-质量规则
 */
@Service
@AllArgsConstructor
public class DataGovernanceQualityRuleServiceImpl extends BaseServiceImpl<DataGovernanceQualityRuleDao, DataGovernanceQualityRuleEntity> implements DataGovernanceQualityRuleService {

	@Override
	public PageResult<DataGovernanceQualityRuleVO> page(DataGovernanceQualityRuleQuery query) {
		IPage<DataGovernanceQualityRuleEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceQualityRuleConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceQualityRuleEntity> getWrapper(DataGovernanceQualityRuleQuery query) {
		LambdaQueryWrapper<DataGovernanceQualityRuleEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataGovernanceQualityRuleEntity::getName, query.getName())
				.like(StringUtil.isNotBlank(query.getEngName()), DataGovernanceQualityRuleEntity::getEngName, query.getEngName());
		return wrapper;
	}

	@Override
	public void save(DataGovernanceQualityRuleVO vo) {
		DataGovernanceQualityRuleEntity entity = DataGovernanceQualityRuleConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceQualityRuleVO vo) {
		DataGovernanceQualityRuleEntity entity = DataGovernanceQualityRuleConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

}
