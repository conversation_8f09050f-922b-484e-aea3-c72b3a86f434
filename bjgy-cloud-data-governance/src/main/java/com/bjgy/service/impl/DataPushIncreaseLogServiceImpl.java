package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushIncreaseLogConvert;
import com.bjgy.entity.DataPushIncreaseLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataPushIncreaseLogQuery;
import com.bjgy.vo.DataPushIncreaseLogVO;
import com.bjgy.dao.DataPushIncreaseLogDao;
import com.bjgy.service.DataPushIncreaseLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据治理-数据推送增量日志
 */
@Service
@AllArgsConstructor
public class DataPushIncreaseLogServiceImpl extends BaseServiceImpl<DataPushIncreaseLogDao, DataPushIncreaseLogEntity> implements DataPushIncreaseLogService {

    @Override
    public PageResult<DataPushIncreaseLogVO> page(DataPushIncreaseLogQuery query) {
        IPage<DataPushIncreaseLogEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(DataPushIncreaseLogConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<DataPushIncreaseLogEntity> getWrapper(DataPushIncreaseLogQuery query){
        LambdaQueryWrapper<DataPushIncreaseLogEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(DataPushIncreaseLogVO vo) {
        DataPushIncreaseLogEntity entity = DataPushIncreaseLogConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(DataPushIncreaseLogVO vo) {
        DataPushIncreaseLogEntity entity = DataPushIncreaseLogConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}
