package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.data.governance.constant.BuiltInMetamodel;
import com.bjgy.api.module.data.governance.constant.BuiltInMetamodelProperty;
import com.bjgy.api.module.data.governance.constant.DbType;
import com.bjgy.api.module.data.integrate.DataDatabaseApi;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.constant.StandardDataType;
import com.bjgy.convert.DataGovernanceStandardConvert;
import com.bjgy.dao.DataGovernanceMetadataDao;
import com.bjgy.dao.DataGovernanceMetadataPropertyDao;
import com.bjgy.dao.DataGovernanceMetadataStandardRelDao;
import com.bjgy.dao.DataGovernanceStandardCodeDao;
import com.bjgy.dao.DataGovernanceStandardDao;
import com.bjgy.dto.CompareResult;
import com.bjgy.dto.StandardCheckDto;
import com.bjgy.entity.DataGovernanceMetadataEntity;
import com.bjgy.entity.DataGovernanceMetadataStandardRelEntity;
import com.bjgy.entity.DataGovernanceStandardCodeEntity;
import com.bjgy.entity.DataGovernanceStandardEntity;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceStandardQuery;
import com.bjgy.service.DataGovernanceStandardService;
import com.bjgy.vo.DataGovernanceMetamodelPropertyVO;
import com.bjgy.vo.DataGovernanceStandardVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.core.model.SchemaTableData;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据治理-数据标准
 */
@Service
@AllArgsConstructor
public class DataGovernanceStandardServiceImpl extends BaseServiceImpl<DataGovernanceStandardDao, DataGovernanceStandardEntity> implements DataGovernanceStandardService {


	private final DataGovernanceMetadataStandardRelDao metadataStandardRelDao;
	private final DataGovernanceMetadataDao metadataDao;
	private final DataGovernanceMetadataPropertyDao metadataPropertyDao;
	private final DataGovernanceStandardCodeDao standardCodeDao;
	private final DataDatabaseApi dataDatabaseApi;

	@Override
	public PageResult<DataGovernanceStandardVO> page(DataGovernanceStandardQuery query) {
		IPage<DataGovernanceStandardEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
		List<DataGovernanceStandardEntity> records = page.getRecords();
		List<DataGovernanceStandardVO> dataGovernanceStandardVOS = DataGovernanceStandardConvert.INSTANCE.convertList(records);
		if (query.isIfMeta() && query.getMetadataId() != null) {
			for (DataGovernanceStandardVO standardVO : dataGovernanceStandardVOS) {
				LambdaQueryWrapper<DataGovernanceMetadataStandardRelEntity> wrapper = Wrappers.lambdaQuery();
				wrapper.eq(DataGovernanceMetadataStandardRelEntity::getMetadataId, query.getMetadataId())
						.eq(DataGovernanceMetadataStandardRelEntity::getStandardId, standardVO.getId()).last("limit 1");
				standardVO.setIfStandardRel(metadataStandardRelDao.selectOne(wrapper) != null);
			}
		}
		return new PageResult<>(dataGovernanceStandardVOS, page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceStandardEntity> getWrapper(DataGovernanceStandardQuery query) {
		LambdaQueryWrapper<DataGovernanceStandardEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getCategoryId() != null, DataGovernanceStandardEntity::getCategoryId, query.getCategoryId())
				.like(StringUtil.isNotBlank(query.getEngName()), DataGovernanceStandardEntity::getEngName, query.getEngName())
				.like(StringUtil.isNotBlank(query.getCnName()), DataGovernanceStandardEntity::getCnName, query.getCnName())
				.eq(query.getType() != null, DataGovernanceStandardEntity::getType, query.getType())
				.orderByDesc(DataGovernanceStandardEntity::getCreateTime).orderByDesc(DataGovernanceStandardEntity::getId);
		dataScopeWithoutOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceStandardVO vo) {
		DataGovernanceStandardEntity entity = DataGovernanceStandardConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceStandardVO vo) {
		DataGovernanceStandardEntity entity = DataGovernanceStandardConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		for (Long id : idList) {
			DataGovernanceStandardEntity standardEntity = baseMapper.selectById(id);
			LambdaQueryWrapper<DataGovernanceMetadataStandardRelEntity> wrapper = Wrappers.lambdaQuery();
			wrapper.eq(DataGovernanceMetadataStandardRelEntity::getStandardId, id).last("limit 1");
			DataGovernanceMetadataStandardRelEntity standardRelEntity = metadataStandardRelDao.selectOne(wrapper);
			if (standardRelEntity != null) {
				throw new ServerException("存在元数据与该数据标准关联，不可删除！");
			}
			//删除标准数据
			LambdaUpdateWrapper<DataGovernanceStandardCodeEntity> delWrapper = Wrappers.lambdaUpdate();
			delWrapper.eq(DataGovernanceStandardCodeEntity::getStandardId, id);
			standardCodeDao.delete(delWrapper);
		}
		removeByIds(idList);

	}

	@Override
	public void online(Long id) {
		DataGovernanceStandardEntity entity = new DataGovernanceStandardEntity();
		entity.setId(id);
		entity.setStatus(1);
		updateById(entity);
	}

	@Override
	public void offline(Long id) {
		DataGovernanceStandardEntity entity = new DataGovernanceStandardEntity();
		entity.setId(id);
		entity.setStatus(0);
		updateById(entity);
	}

	@Override
	public StandardCheckDto checkStandard(Long metadataId, Long standardId) {
		//获取元数据属性
		List<DataGovernanceMetamodelPropertyVO> propertyVOS = metadataPropertyDao.listPropertyById(metadataId, BuiltInMetamodel.COLUMN.getId());
		DataGovernanceStandardEntity standardEntity = baseMapper.selectById(standardId);

		StandardCheckDto standardCheckDto = new StandardCheckDto();
		List<CompareResult> compareResults = new ArrayList<>();
		standardCheckDto.setCompareResults(compareResults);
		StandardDataType standardDataType = StandardDataType.getByCode(standardEntity.getDataType());
		//检测字段属性是否符合标准
		for (DataGovernanceMetamodelPropertyVO propertyVO : propertyVOS) {
			if (BuiltInMetamodelProperty.COLUMN_DATA_TYPE.getId().equals(propertyVO.getMetamodelPropertyId())) {
				CompareResult compareResult = new CompareResult();
				compareResult.setProperty("数据类型");
				compareResult.setMetadataVal(propertyVO.getValue());
				compareResult.setStandardVal(standardDataType.getLongValue());
				compareResult.setStandard(standardDataType.getDbDataTypes().contains(compareResult.getMetadataVal()));
				compareResults.add(compareResult);
			} else if (BuiltInMetamodelProperty.COLUMN_DATA_LENGTH.getId().equals(propertyVO.getMetamodelPropertyId())) {
				CompareResult compareResult = new CompareResult();
				compareResult.setProperty("长度");
				compareResult.setMetadataVal(propertyVO.getValue());
				compareResult.setStandardVal(standardEntity.getDataLength() != null ? standardEntity.getDataLength().toString() : null);
				compareResult.setStandard(compareResult.getMetadataVal() == null || compareResult.getMetadataVal().equals(compareResult.getStandardVal()));
				compareResults.add(compareResult);
			} else if (BuiltInMetamodelProperty.COLUMN_DATA_SCALE.getId().equals(propertyVO.getMetamodelPropertyId())) {
				CompareResult compareResult = new CompareResult();
				compareResult.setProperty("精度（小数位数）");
				compareResult.setMetadataVal(propertyVO.getValue());
				compareResult.setStandardVal(standardEntity.getDataPrecision() != null ? standardEntity.getDataPrecision().toString() : null);
				compareResult.setStandard(compareResult.getMetadataVal() == null || compareResult.getMetadataVal().equals(compareResult.getStandardVal()));
				compareResults.add(compareResult);
			} else if (BuiltInMetamodelProperty.COLUMN_NULLABLE.getId().equals(propertyVO.getMetamodelPropertyId())) {
				CompareResult compareResult = new CompareResult();
				compareResult.setProperty("允许为空");
				compareResult.setMetadataVal(propertyVO.getValue());
				compareResult.setStandardVal(standardEntity.getNullable() == 1 ? "是" : "否");
				compareResult.setStandard(compareResult.getMetadataVal().equals(compareResult.getStandardVal()));
				compareResults.add(compareResult);
			}
		}
		standardCheckDto.setRelStandardCode(true);
		//获取码表数据
		LambdaQueryWrapper<DataGovernanceStandardCodeEntity> codeWrapper = Wrappers.lambdaQuery();
		codeWrapper.eq(DataGovernanceStandardCodeEntity::getStandardId, standardEntity.getId());
		List<DataGovernanceStandardCodeEntity> standardCodes = standardCodeDao.selectList(codeWrapper);
		if (standardCodes.isEmpty()) {
			standardCheckDto.setHasStandardCode(false);
			return standardCheckDto;
		}
		standardCheckDto.setHasStandardCode(true);
		//检测符合标准的数量和不符合标准的数量
		DataGovernanceMetadataEntity columnMetadata = metadataDao.selectById(metadataId);
		//获取父级表的元数据
		DataGovernanceMetadataEntity tableMetadata = metadataDao.selectById(columnMetadata.getParentId());

		//数据库
		DataDatabaseDto database = new DataDatabaseDto();
		if (DbType.DATABASE.getValue().equals(columnMetadata.getDbType())) {
			database = dataDatabaseApi.getById(columnMetadata.getDatasourceId()).getData();
			//中台库
		} else if (DbType.MIDDLE_DB.getValue().equals(columnMetadata.getDbType())) {
			DataProjectCacheBean project = getProject();
			database.setJdbcUrl(project.getDbUrl());
			database.setDatabaseType(project.getDbType());
			database.setUserName(project.getDbUsername());
			database.setPassword(project.getDbPassword());
			database.setDatabaseSchema(project.getDbSchema());
		}

		StringBuilder fillNumSql = new StringBuilder(String.format("SELECT COUNT(1) AS c FROM %s.%s WHERE %s IN(", database.getDatabaseSchema(), tableMetadata.getCode(), columnMetadata.getCode()));
		StringBuilder notFillNumSql = new StringBuilder(String.format("SELECT COUNT(1) AS c FROM %s.%s WHERE %s NOT IN(", database.getDatabaseSchema(), tableMetadata.getCode(), columnMetadata.getCode()));
		for (DataGovernanceStandardCodeEntity standardCode : standardCodes) {
			if (StandardDataType.NUMBER.equals(standardDataType)) {
				fillNumSql.append(String.format("%s,", Long.parseLong(standardCode.getDataId())));
				notFillNumSql.append(String.format("%s,", Long.parseLong(standardCode.getDataId())));
			} else if (StandardDataType.NUMBER_SACLE.equals(standardDataType)) {
				fillNumSql.append(String.format("%s,", Double.parseDouble(standardCode.getDataId())));
				notFillNumSql.append(String.format("%s,", Double.parseDouble(standardCode.getDataId())));
			} else {
				fillNumSql.append(String.format("'%s',", standardCode.getDataId()));
				notFillNumSql.append(String.format("'%s',", standardCode.getDataId()));
			}

		}
		fillNumSql.deleteCharAt(fillNumSql.length() - 1);
		notFillNumSql.deleteCharAt(notFillNumSql.length() - 1);
		fillNumSql.append(")");
		notFillNumSql.append(")");
		standardCheckDto.setFillNumSql(fillNumSql.toString());
		standardCheckDto.setNotFillNumSql(notFillNumSql.toString());

		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(database.getDatabaseType());
		IMetaDataByJdbcService metaDataService = new MetaDataByJdbcServiceImpl(productTypeEnum);
		SchemaTableData schemaTableData = metaDataService.queryTableDataBySql(database.getJdbcUrl(), database.getUserName(), database.getPassword(), fillNumSql.toString(), 100);
		standardCheckDto.setFillNum(schemaTableData.getRows().get(0).get(0));
		schemaTableData = metaDataService.queryTableDataBySql(database.getJdbcUrl(), database.getUserName(), database.getPassword(), notFillNumSql.toString(), 100);
		standardCheckDto.setNotFullNum(schemaTableData.getRows().get(0).get(0));

		String sql = String.format("SELECT %s FROM %s.%s GROUP BY %s", columnMetadata.getCode(), database.getDatabaseSchema(), tableMetadata.getCode(), columnMetadata.getCode());
		schemaTableData = metaDataService.queryTableDataBySql(database.getJdbcUrl(), database.getUserName(), database.getPassword(), sql, 100);
		List<List<Object>> rows = schemaTableData.getRows();
		List<Object> columnValues = new ArrayList<>();
		for (List<Object> row : rows) {
			columnValues.add(row.get(0));
		}
		standardCheckDto.setColumnValues(JSONUtil.toJsonString(columnValues));

		return standardCheckDto;
	}

}
