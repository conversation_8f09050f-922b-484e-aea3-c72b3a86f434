package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataPushTaskLogConvert;
import com.bjgy.entity.DataPushTaskLogEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataPushTaskLogQuery;
import com.bjgy.vo.DataPushTaskLogVO;
import com.bjgy.dao.DataPushTaskLogDao;
import com.bjgy.service.DataPushTaskLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据治理-数据推送任务日志
 */
@Service
@AllArgsConstructor
public class DataPushTaskLogServiceImpl extends BaseServiceImpl<DataPushTaskLogDao, DataPushTaskLogEntity> implements DataPushTaskLogService {

	@Override
	public PageResult<DataPushTaskLogVO> page(DataPushTaskLogQuery query) {
		IPage<DataPushTaskLogEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataPushTaskLogConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataPushTaskLogEntity> getWrapper(DataPushTaskLogQuery query) {
		LambdaQueryWrapper<DataPushTaskLogEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getDataPushTaskId() != null, DataPushTaskLogEntity::getDataPushTaskId, query.getDataPushTaskId())
				.eq(query.getRunStatus() != null, DataPushTaskLogEntity::getRunStatus, query.getRunStatus())
				.orderByDesc(DataPushTaskLogEntity::getId);
		return wrapper;
	}

	@Override
	public void save(DataPushTaskLogVO vo) {
		DataPushTaskLogEntity entity = DataPushTaskLogConvert.INSTANCE.convert(vo);

		baseMapper.insert(entity);
	}

	@Override
	public void update(DataPushTaskLogVO vo) {
		DataPushTaskLogEntity entity = DataPushTaskLogConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

}
