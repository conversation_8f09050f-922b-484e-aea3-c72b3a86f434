package com.bjgy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import com.bjgy.convert.DataGovernanceLabelConvert;
import com.bjgy.dao.DataGovernanceLabelCategoryDao;
import com.bjgy.dao.DataGovernanceLabelDao;
import com.bjgy.dto.ConditionConfig;
import com.bjgy.dto.LabelPageDataQuery;
import com.bjgy.entity.DataGovernanceLabelCategoryEntity;
import com.bjgy.entity.DataGovernanceLabelEntity;
import com.bjgy.entity.DataGovernanceLabelModelEntity;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.DateUtils;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceLabelQuery;
import com.bjgy.service.DataGovernanceLabelModelService;
import com.bjgy.service.DataGovernanceLabelService;
import com.bjgy.vo.DataGovernanceLabelVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.model.JdbcSelectResult;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据治理-标签
 */
@Service
@AllArgsConstructor
public class DataGovernanceLabelServiceImpl extends BaseServiceImpl<DataGovernanceLabelDao, DataGovernanceLabelEntity> implements DataGovernanceLabelService {


	private final DataGovernanceLabelCategoryDao categoryDao;
	private final DataGovernanceLabelModelService labelModelService;

	@Override
	public PageResult<DataGovernanceLabelVO> page(DataGovernanceLabelQuery query) {
		QueryWrapper<DataGovernanceLabelEntity> wrapper = new QueryWrapper<>();
		if (ObjectUtil.isNotNull(query.getCategoryId())) {
			List<Long> categoryIdList = new ArrayList<>();
			findContainedCategory(query.getCategoryId(), categoryIdList);
			wrapper.in("label.category_id", categoryIdList);
		}
		wrapper.eq(ObjectUtil.isNotNull(query.getLabelModelId()), "label.label_model_id", query.getLabelModelId())
				.eq(ObjectUtil.isNotNull(query.getStatus()), "label.status", query.getStatus())
				.eq(ObjectUtil.isNotNull(query.getLabelModelType()), "model.type", query.getLabelModelType())
				.like(StringUtil.isNotBlank(query.getName()), "label.name", query.getName())
				.apply(getDataScope("label", "label", "org_id", "project_id", true, true).getSqlFilter())
				.orderByDesc("label.update_time");
		IPage<DataGovernanceLabelVO> page = baseMapper.queryByPage(getPage(query), wrapper);
		return new PageResult<>(page.getRecords(), page.getTotal());
	}

	private void findContainedCategory(Long categoryId, List<Long> categoryIdList) {
		categoryIdList.add(categoryId);
		LambdaQueryWrapper<DataGovernanceLabelCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceLabelCategoryEntity::getParentId, categoryId);
		List<DataGovernanceLabelCategoryEntity> categoryEntityList = this.categoryDao.selectList(wrapper);
		categoryEntityList.forEach(entity -> {
			categoryIdList.add(entity.getId());
			this.findContainedCategory(entity.getId(), categoryIdList);
		});
	}

	private LambdaQueryWrapper<DataGovernanceLabelEntity> getWrapper(DataGovernanceLabelQuery query) {
		LambdaQueryWrapper<DataGovernanceLabelEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(query.getCategoryId() != null, DataGovernanceLabelEntity::getCategoryId, query.getCategoryId())
				.eq(query.getTypeId() != null, DataGovernanceLabelEntity::getTypeId, query.getTypeId())
				.eq(query.getLabelModelId() != null, DataGovernanceLabelEntity::getLabelModelId, query.getLabelModelId())
				.like(StringUtil.isNotBlank(query.getName()), DataGovernanceLabelEntity::getName, query.getName());
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceLabelVO vo) {
		DataGovernanceLabelEntity entity = DataGovernanceLabelConvert.INSTANCE.convert(vo);
		DataGovernanceLabelCategoryEntity category = categoryDao.selectById(vo.getCategoryId());
		entity.setOrgId(category.getOrgId());
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceLabelVO vo) {
		DataGovernanceLabelEntity entity = DataGovernanceLabelConvert.INSTANCE.convert(vo);
		DataGovernanceLabelCategoryEntity category = categoryDao.selectById(vo.getCategoryId());
		entity.setOrgId(category.getOrgId());
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public void on(Long id) {
		DataGovernanceLabelEntity labelEntity = baseMapper.selectById(id);
		labelEntity.setStatus(1);
		baseMapper.updateById(labelEntity);
	}

	@Override
	public void off(Long id) {
		DataGovernanceLabelEntity labelEntity = baseMapper.selectById(id);
		labelEntity.setStatus(0);
		baseMapper.updateById(labelEntity);
	}

	@Override
	public List<DataGovernanceLabelVO> listEnable(Long typeId) {
		LambdaQueryWrapper<DataGovernanceLabelEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataGovernanceLabelEntity::getStatus, 1).eq(typeId != null, DataGovernanceLabelEntity::getTypeId, typeId);
		dataScopeWithOrgId(wrapper);
		return DataGovernanceLabelConvert.INSTANCE.convertList(baseMapper.selectList(wrapper));
	}

	@Override
	public List<ColumnDescription> listColumn(Integer id) {
		DataGovernanceLabelEntity labelEntity = baseMapper.selectById(id);
		return labelModelService.listColumn(labelEntity.getLabelModelId());
	}

	@SneakyThrows
	@Override
	public JdbcSelectResult pageDataBySql(LabelPageDataQuery labelPageDataQuery) {
		//拼装sql
		DataGovernanceLabelEntity labelEntity = baseMapper.selectById(labelPageDataQuery.getLabelId());
		List<ConditionConfig> labelConditionConfig = SingletonObject.OBJECT_MAPPER.readValue(SingletonObject.OBJECT_MAPPER.writeValueAsString(labelEntity.getConditionConfig()), new TypeReference<List<ConditionConfig>>() {
		});
		DataGovernanceLabelModelEntity model = labelModelService.getById(labelEntity.getLabelModelId());
		StringBuilder sql = new StringBuilder();
		List<Object> params = new ArrayList<>();
		if (model.getType() == 1) {
			sql.append(String.format("SELECT * FROM %s WHERE 1=1", model.getTableName()));
		} else {
			sql.append(String.format("SELECT * FROM (%s) t1 WHERE 1=1", model.getSqlText()));
		}
		if (!CollectionUtils.isEmpty(labelConditionConfig)) {
			sql.append(" AND (");
			int i = 0;
			for (ConditionConfig conditionConfig : labelConditionConfig) {
				if (i != 0) {
					sql.append(conditionConfig.getRelType());
				}
				sql.append(conditionConfig.getFieldName()).append(conditionConfig.getCondition());
				if (!" IS NULL ".equals(conditionConfig.getCondition()) && !" IS NOT NULL ".equals(conditionConfig.getCondition())) {
					sql.append("?");
					String filedTypeClassName = conditionConfig.getFiledTypeClassName();
					buildParams(params, filedTypeClassName, conditionConfig.getCondition().contains("LIKE") ? "%" + conditionConfig.getConditionVal() + "%" : conditionConfig.getConditionVal());
				}
				i++;
			}
			sql.append(")");
		}
		if (!CollectionUtils.isEmpty(labelPageDataQuery.getConditionConfigs())) {
			for (ConditionConfig conditionConfig : labelPageDataQuery.getConditionConfigs()) {
				if (StringUtil.isNotBlank(conditionConfig.getConditionVal())) {
					String filedTypeClassName = conditionConfig.getFiledTypeClassName();
					sql.append(" AND ").append(conditionConfig.getFieldName()).append("java.lang.String".equals(filedTypeClassName) ? " LIKE ?" : "=?");
					buildParams(params, filedTypeClassName, "java.lang.String".equals(filedTypeClassName) ? "%" + conditionConfig.getConditionVal() + "%" : conditionConfig.getConditionVal());
				}
			}
		}
		DataProjectCacheBean project = getProject();
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(project.getDbType());
		IMetaDataByJdbcService metaDataService = new MetaDataByJdbcServiceImpl(productTypeEnum);
		return metaDataService.pageDataBySql(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), sql.toString(), params, labelPageDataQuery.getPage(), labelPageDataQuery.getLimit());
	}

	private void buildParams(List<Object> params, String filedTypeClassName, String conditionVal) {
		try {
			switch (filedTypeClassName) {
				case "java.lang.Long":
					params.add(Long.parseLong(conditionVal));
					break;
				case "java.lang.Integer":
					params.add(Integer.parseInt(conditionVal));
					break;
				case "java.lang.Float":
				case "java.lang.Double":
				case "java.math.BigDecimal":
					params.add(Double.parseDouble(conditionVal));
					break;
				case "java.sql.Date":
					params.add(DateUtils.parse(conditionVal, DateUtils.DATE_PATTERN));
					break;
				case "java.sql.Time":
					params.add(DateUtils.parse(conditionVal, DateUtils.TIME_PATTERN));
					break;
				case "java.sql.TIMESTAMP":
					params.add(DateUtils.parse(conditionVal, DateUtils.DATE_TIME_PATTERN));
					break;
				default:
					params.add(conditionVal);
					break;
			}
		} catch (Exception e) {
			throw new ServerException("查询失败，请检查参数类型是否跟字段类型一致！");
		}
	}

}
