package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceStandardCategoryConvert;
import com.bjgy.dao.DataGovernanceStandardCategoryDao;
import com.bjgy.dao.DataGovernanceStandardDao;
import com.bjgy.entity.DataGovernanceStandardCategoryEntity;
import com.bjgy.entity.DataGovernanceStandardEntity;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.BeanUtil;
import com.bjgy.framework.common.utils.BuildTreeUtils;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.service.DataGovernanceStandardCategoryService;
import com.bjgy.vo.DataGovernanceStandardCategoryVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-标准目录
 */
@Service
@AllArgsConstructor
public class DataGovernanceStandardCategoryServiceImpl extends BaseServiceImpl<DataGovernanceStandardCategoryDao, DataGovernanceStandardCategoryEntity> implements DataGovernanceStandardCategoryService {

	private final DataGovernanceStandardDao standardDao;
	@Override
	public List<TreeNodeVo> listTree() {
		LambdaQueryWrapper<DataGovernanceStandardCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceStandardCategoryEntity::getProjectId, getProjectId()).orderByAsc(DataGovernanceStandardCategoryEntity::getOrderNo);
		List<DataGovernanceStandardCategoryEntity> dataGovernanceStandardCategoryEntities = baseMapper.selectList(wrapper);
		List<TreeNodeVo> treeNodeVos = BeanUtil.copyListProperties(dataGovernanceStandardCategoryEntities, TreeNodeVo::new, (oldItem, newItem) -> {
			newItem.setLabel(oldItem.getName());
			newItem.setValue(oldItem.getId());
			if (newItem.getPath().contains("/")) {
				newItem.setParentPath(newItem.getPath().substring(0, newItem.getPath().lastIndexOf("/")));
			}
		});
		return BuildTreeUtils.buildTree(treeNodeVos);
	}

	@Override
	public void save(DataGovernanceStandardCategoryVO vo) {
		DataGovernanceStandardCategoryEntity entity = DataGovernanceStandardCategoryConvert.INSTANCE.convert(vo);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceStandardCategoryVO vo) {
		DataGovernanceStandardCategoryEntity entity = DataGovernanceStandardCategoryConvert.INSTANCE.convert(vo);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	private String recursionPath(DataGovernanceStandardCategoryEntity categoryEntity, String path) {
		if (StringUtil.isBlank(path)) {
			path = categoryEntity.getName();
		}
		if (categoryEntity.getParentId() != 0) {
			DataGovernanceStandardCategoryEntity parent = getById(categoryEntity.getParentId());
			path = parent.getName() + "/" + path;
			return recursionPath(parent, path);
		}
		return path;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id) {
		LambdaQueryWrapper<DataGovernanceStandardCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceStandardCategoryEntity::getParentId, id).last("limit 1");
		if (baseMapper.selectOne(wrapper) != null) {
			throw new ServerException("存在子节点，不可删除！");
		}
		LambdaQueryWrapper<DataGovernanceStandardEntity> standardWrapper = new LambdaQueryWrapper<>();
		standardWrapper.eq(DataGovernanceStandardEntity::getCategoryId, id).last("limit 1");
		if (standardDao.selectOne(standardWrapper) != null) {
			throw new ServerException("目录下存在数据标准配置，不可删除！");
		}
		removeById(id);
	}

}
