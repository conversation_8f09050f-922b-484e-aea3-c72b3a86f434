package com.bjgy.service.impl;

import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceMetadataPropertyConvert;
import com.bjgy.dao.DataGovernanceMetadataPropertyDao;
import com.bjgy.entity.DataGovernanceMetadataPropertyEntity;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.service.DataGovernanceMetadataPropertyService;
import com.bjgy.vo.DataGovernanceMetadataPropertyVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据治理-元数据属性值
 */
@Service
@AllArgsConstructor
public class DataGovernanceMetadataPropertyServiceImpl extends BaseServiceImpl<DataGovernanceMetadataPropertyDao, DataGovernanceMetadataPropertyEntity> implements DataGovernanceMetadataPropertyService {


    @Override
    public void save(DataGovernanceMetadataPropertyVO vo) {
        DataGovernanceMetadataPropertyEntity entity = DataGovernanceMetadataPropertyConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(DataGovernanceMetadataPropertyVO vo) {
        DataGovernanceMetadataPropertyEntity entity = DataGovernanceMetadataPropertyConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}
