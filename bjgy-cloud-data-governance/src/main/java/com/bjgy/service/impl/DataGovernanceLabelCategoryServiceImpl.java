package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelCategoryConvert;
import com.bjgy.dao.DataGovernanceLabelDao;
import com.bjgy.entity.DataGovernanceLabelCategoryEntity;
import com.bjgy.entity.DataGovernanceLabelEntity;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.BeanUtil;
import com.bjgy.framework.common.utils.BuildTreeUtils;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.vo.DataGovernanceLabelCategoryVO;
import com.bjgy.dao.DataGovernanceLabelCategoryDao;
import com.bjgy.service.DataGovernanceLabelCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据治理-标签类目
 */
@Service
@AllArgsConstructor
public class DataGovernanceLabelCategoryServiceImpl extends BaseServiceImpl<DataGovernanceLabelCategoryDao, DataGovernanceLabelCategoryEntity> implements DataGovernanceLabelCategoryService {

	private final DataGovernanceLabelDao dataGovernanceLabelDao;

	@Override
	public List<TreeNodeVo> listTree() {
		LambdaQueryWrapper<DataGovernanceLabelCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceLabelCategoryEntity::getProjectId, getProjectId()).orderByAsc(DataGovernanceLabelCategoryEntity::getOrderNo);
		List<DataGovernanceLabelCategoryEntity> dataGovernanceLabelCategoryEntities = baseMapper.selectList(wrapper);
		List<TreeNodeVo> treeNodeVos = BeanUtil.copyListProperties(dataGovernanceLabelCategoryEntities, TreeNodeVo::new, (oldItem, newItem) -> {
			newItem.setLabel(oldItem.getName());
			newItem.setValue(oldItem.getId());
			newItem.setDisabled(oldItem.getType() != 1);
			if (newItem.getPath().contains("/")) {
				newItem.setParentPath(newItem.getPath().substring(0, newItem.getPath().lastIndexOf("/")));
			}
		});
		return BuildTreeUtils.buildTree(treeNodeVos);
	}

	@Override
    public void save(DataGovernanceLabelCategoryVO vo) {
        DataGovernanceLabelCategoryEntity entity = DataGovernanceLabelCategoryConvert.INSTANCE.convert(vo);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
        baseMapper.insert(entity);
    }

    @Override
    public void update(DataGovernanceLabelCategoryVO vo) {
        DataGovernanceLabelCategoryEntity entity = DataGovernanceLabelCategoryConvert.INSTANCE.convert(vo);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
        updateById(entity);
    }

	private String recursionPath(DataGovernanceLabelCategoryEntity categoryEntity, String path) {
		if (StringUtil.isBlank(path)) {
			path = categoryEntity.getName();
		}
		if (categoryEntity.getParentId() != 0) {
			DataGovernanceLabelCategoryEntity parent = getById(categoryEntity.getParentId());
			path = parent.getName() + "/" + path;
			return recursionPath(parent, path);
		}
		return path;
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
		LambdaQueryWrapper<DataGovernanceLabelCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataGovernanceLabelCategoryEntity::getParentId, id).last("limit 1");
		if (baseMapper.selectOne(wrapper) != null) {
			throw new ServerException("存在子节点，不可删除！");
		}
		LambdaQueryWrapper<DataGovernanceLabelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(DataGovernanceLabelEntity::getCategoryId, id).last("limit 1");
		if (dataGovernanceLabelDao.selectOne(lambdaQueryWrapper) != null) {
			throw new ServerException("目录下存在标签数据，不可删除！");
		}
        removeById(id);
    }

	@Override
	public List<DataGovernanceLabelCategoryEntity> listAll() {
		LambdaQueryWrapper<DataGovernanceLabelCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		dataScopeWithOrgId(wrapper);
		return list(wrapper);
	}

}
