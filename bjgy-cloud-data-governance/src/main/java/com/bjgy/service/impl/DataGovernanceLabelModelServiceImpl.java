package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceLabelModelConvert;
import com.bjgy.dao.DataGovernanceLabelModelDao;
import com.bjgy.dto.CheckSqlRequest;
import com.bjgy.dto.CheckSqlResult;
import com.bjgy.entity.DataGovernanceLabelModelEntity;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataGovernanceLabelModelQuery;
import com.bjgy.service.DataGovernanceLabelModelService;
import com.bjgy.vo.DataGovernanceLabelModelVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.List;

/**
 * 数据治理-标签实体
 */
@Service
@AllArgsConstructor
public class DataGovernanceLabelModelServiceImpl extends BaseServiceImpl<DataGovernanceLabelModelDao, DataGovernanceLabelModelEntity> implements DataGovernanceLabelModelService {

	@Override
	public PageResult<DataGovernanceLabelModelVO> page(DataGovernanceLabelModelQuery query) {
		IPage<DataGovernanceLabelModelEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataGovernanceLabelModelConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataGovernanceLabelModelEntity> getWrapper(DataGovernanceLabelModelQuery query) {
		LambdaQueryWrapper<DataGovernanceLabelModelEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataGovernanceLabelModelEntity::getName, query.getName());
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	public void save(DataGovernanceLabelModelVO vo) {
		DataGovernanceLabelModelEntity entity = DataGovernanceLabelModelConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceLabelModelVO vo) {
		DataGovernanceLabelModelEntity entity = DataGovernanceLabelModelConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public CheckSqlResult checkSql(CheckSqlRequest checkSqlRequest) {
		DataProjectCacheBean project = getProject();
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(project.getDbType());
		IMetaDataByJdbcService metaDataService = new MetaDataByJdbcServiceImpl(productTypeEnum);
		CheckSqlResult checkSqlResult = new CheckSqlResult();
		checkSqlResult.setSuccess(true);
		try {
			metaDataService.querySqlColumnMeta(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), checkSqlRequest.getSql());
			return checkSqlResult;
		} catch (Exception e) {
			checkSqlResult.setSuccess(false);
			checkSqlResult.setErrorMsg(e.getMessage());
			return checkSqlResult;
		}
	}

	@Override
	public List<DataGovernanceLabelModelEntity> listAll() {
		LambdaQueryWrapper<DataGovernanceLabelModelEntity> wrapper = new LambdaQueryWrapper<>();
		dataScopeWithOrgId(wrapper);
		return list(wrapper);
	}

	@Override
	public List<ColumnDescription> listColumn(Long id) {
		DataGovernanceLabelModelEntity modelEntity = baseMapper.selectById(id);
		Integer type = modelEntity.getType();
		DataProjectCacheBean project = getProject();
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(project.getDbType());
		IMetaDataByJdbcService service = new MetaDataByJdbcServiceImpl(productTypeEnum);
		if (type == 1) {
			return service.queryTableColumnMeta(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), project.getDbSchema(), modelEntity.getTableName());
		} else {
			return service.querySqlColumnMeta(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), modelEntity.getSqlText());
		}
	}

}
