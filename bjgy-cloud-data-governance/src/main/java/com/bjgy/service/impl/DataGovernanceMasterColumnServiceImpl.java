package com.bjgy.service.impl;

import lombok.AllArgsConstructor;
import com.bjgy.convert.DataGovernanceMasterColumnConvert;
import com.bjgy.dao.DataGovernanceMasterColumnDao;
import com.bjgy.entity.DataGovernanceMasterColumnEntity;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.utils.BeanUtil;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.service.DataGovernanceMasterColumnService;
import com.bjgy.vo.DataGovernanceMasterColumnVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据治理-主数据模型字段
 */
@Service
@AllArgsConstructor
public class DataGovernanceMasterColumnServiceImpl extends BaseServiceImpl<DataGovernanceMasterColumnDao, DataGovernanceMasterColumnEntity> implements DataGovernanceMasterColumnService {

	@Override
	public void save(DataGovernanceMasterColumnVO vo) {
		DataGovernanceMasterColumnEntity entity = DataGovernanceMasterColumnConvert.INSTANCE.convert(vo);

		baseMapper.insert(entity);
	}

	@Override
	public void update(DataGovernanceMasterColumnVO vo) {
		DataGovernanceMasterColumnEntity entity = DataGovernanceMasterColumnConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public List<DataGovernanceMasterColumnVO> middleDbClumnInfo(String tableName) {
		DataProjectCacheBean project = getProject();
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(project.getDbType());
		IMetaDataByJdbcService service = new MetaDataByJdbcServiceImpl(productTypeEnum);
		List<ColumnDescription> columnDescriptions = service.queryTableColumnMeta(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), project.getDbSchema(), tableName);
		List<String> pks = service.queryTablePrimaryKeys(project.getDbUrl(), project.getDbUsername(), project.getDbPassword(), project.getDbSchema(), tableName);
		List<DataGovernanceMasterColumnVO> columnVOS = new ArrayList<>(10);
		for (ColumnDescription columnDescription : columnDescriptions) {
			if (pks.contains(columnDescription.getFieldName())) {
				columnDescription.setPk(true);
			}
			DataGovernanceMasterColumnVO columnVO = new DataGovernanceMasterColumnVO(columnDescription);
			columnVOS.add(columnVO);
		}
		return columnVOS;
	}

}
