package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceStandardCleanLogVO;
import com.bjgy.query.DataGovernanceStandardCleanLogQuery;
import com.bjgy.entity.DataGovernanceStandardCleanLogEntity;

import java.util.List;

/**
 * 数据治理-标准清洗日志
 */
public interface DataGovernanceStandardCleanLogService extends BaseService<DataGovernanceStandardCleanLogEntity> {

    PageResult<DataGovernanceStandardCleanLogVO> page(DataGovernanceStandardCleanLogQuery query);

    void save(DataGovernanceStandardCleanLogVO vo);

    void update(DataGovernanceStandardCleanLogVO vo);

    void delete(List<Long> idList);
}
