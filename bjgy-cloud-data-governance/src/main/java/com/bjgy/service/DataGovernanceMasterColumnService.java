package com.bjgy.service;

import com.bjgy.entity.DataGovernanceMasterColumnEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMasterColumnVO;

import java.util.List;

/**
 * 数据治理-主数据模型字段
 */
public interface DataGovernanceMasterColumnService extends BaseService<DataGovernanceMasterColumnEntity> {

	void save(DataGovernanceMasterColumnVO vo);

	void update(DataGovernanceMasterColumnVO vo);

	void delete(List<Long> idList);

	List<DataGovernanceMasterColumnVO> middleDbClumnInfo(String tableName);
}
