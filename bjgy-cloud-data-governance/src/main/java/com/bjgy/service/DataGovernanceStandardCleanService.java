package com.bjgy.service;

import com.bjgy.api.module.data.governance.dto.CleanConfig;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataGovernanceMetadataStandardRelVO;
import com.bjgy.vo.DataGovernanceStandardCleanVO;
import com.bjgy.query.DataGovernanceStandardCleanQuery;
import com.bjgy.entity.DataGovernanceStandardCleanEntity;
import com.bjgy.vo.DataGovernanceStandardCodeVO;

import java.util.List;

/**
 * 数据治理-标准清洗
 */
public interface DataGovernanceStandardCleanService extends BaseService<DataGovernanceStandardCleanEntity> {

    PageResult<DataGovernanceStandardCleanVO> page(DataGovernanceStandardCleanQuery query);

	DataGovernanceStandardCleanVO save(DataGovernanceStandardCleanVO vo);

	DataGovernanceStandardCleanVO update(DataGovernanceStandardCleanVO vo);

    void delete(List<Long> idList);

	DataGovernanceStandardCleanEntity getByMetadataId(Long metadataId);

	String getMetadataColumnType(Long metadataId);

	List<DataGovernanceStandardCodeVO> listStandardCode(Long metadataId);

	DataGovernanceMetadataStandardRelVO getMetadataStandardRel(Long metadataId);

	DataGovernanceStandardCleanVO release(Long id);

	DataGovernanceStandardCleanVO cancel(Long id);

	void handRun(Long id);
}
