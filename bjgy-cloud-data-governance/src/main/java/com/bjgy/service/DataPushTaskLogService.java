package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataPushTaskLogVO;
import com.bjgy.query.DataPushTaskLogQuery;
import com.bjgy.entity.DataPushTaskLogEntity;

import java.util.List;

/**
 * 数据治理-数据推送任务日志
 */
public interface DataPushTaskLogService extends BaseService<DataPushTaskLogEntity> {

    PageResult<DataPushTaskLogVO> page(DataPushTaskLogQuery query);

    void save(DataPushTaskLogVO vo);

    void update(DataPushTaskLogVO vo);

    void delete(List<Long> idList);
}
