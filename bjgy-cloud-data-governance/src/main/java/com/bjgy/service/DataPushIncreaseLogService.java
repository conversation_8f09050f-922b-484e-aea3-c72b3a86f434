package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataPushIncreaseLogVO;
import com.bjgy.query.DataPushIncreaseLogQuery;
import com.bjgy.entity.DataPushIncreaseLogEntity;

import java.util.List;

/**
 * 数据治理-数据推送增量日志
 */
public interface DataPushIncreaseLogService extends BaseService<DataPushIncreaseLogEntity> {

    PageResult<DataPushIncreaseLogVO> page(DataPushIncreaseLogQuery query);

    void save(DataPushIncreaseLogVO vo);

    void update(DataPushIncreaseLogVO vo);

    void delete(List<Long> idList);
}
