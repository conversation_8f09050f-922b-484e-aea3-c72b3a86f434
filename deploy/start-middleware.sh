#!/bin/bash

# bjgy-cloud 中间件启动脚本
# 独立启动所有中间件服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "bjgy-network"; then
        log_info "创建Docker网络: bjgy-network"
        docker network create bjgy-network
    else
        log_info "Docker网络已存在: bjgy-network"
    fi
}

# 启动中间件
start_middleware() {
    log_info "启动中间件服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.middleware.yml up -d
    else
        docker compose -f docker-compose.middleware.yml up -d
    fi
    
    log_success "中间件服务启动命令已执行"
}

# 检查服务健康状态
check_health() {
    log_info "检查中间件服务健康状态..."
    
    local services=(
        "mysql:3306:MySQL数据库"
        "redis:6379:Redis缓存"
        "nacos:8848:Nacos注册中心"
        "minio:9000:MinIO对象存储"
        "rabbitmq:15672:RabbitMQ消息队列"
        "elasticsearch:9200:Elasticsearch搜索引擎"
        "kibana:5601:Kibana可视化"
        "flink-jobmanager:8081:Flink JobManager"
        "prometheus:9090:Prometheus监控"
        "grafana:3000:Grafana可视化"
    )
    
    echo ""
    echo "服务健康检查结果:"
    echo "----------------------------------------"
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port desc <<< "$service_info"
        
        if curl -f "http://localhost:${port}" &> /dev/null 2>&1 || \
           nc -z localhost "$port" &> /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} ${desc} (端口:${port}) - 健康"
        else
            echo -e "${RED}✗${NC} ${desc} (端口:${port}) - 不健康或未启动"
        fi
    done
    
    echo "----------------------------------------"
}

# 显示服务信息
show_services_info() {
    echo ""
    log_success "中间件服务启动完成！"
    echo ""
    echo "服务访问地址:"
    echo "  MySQL数据库:         localhost:3306 (用户名/密码: root/123456)"
    echo "  Redis缓存:           localhost:6379"
    echo "  Nacos控制台:         http://localhost:8848/nacos (nacos/nacos)"
    echo "  MinIO控制台:         http://localhost:9001 (minioadmin/minioadmin123)"
    echo "  RabbitMQ管理界面:    http://localhost:15672 (admin/admin123)"
    echo "  Elasticsearch:       http://localhost:9200"
    echo "  Kibana:              http://localhost:5601"
    echo "  Flink Web UI:        http://localhost:8081"
    echo "  Prometheus:          http://localhost:9090"
    echo "  Grafana:             http://localhost:3000 (admin/admin123)"
    echo ""
    echo "数据卷:"
    echo "  MySQL数据:           mysql_data"
    echo "  Redis数据:           redis_data"
    echo "  Nacos数据:           nacos_data, nacos_logs"
    echo "  MinIO数据:           minio_data"
    echo "  RabbitMQ数据:        rabbitmq_data"
    echo "  Elasticsearch数据:   elasticsearch_data"
    echo "  Flink数据:           flink_data, flink_logs"
    echo "  Prometheus数据:      prometheus_data"
    echo "  Grafana数据:         grafana_data"
    echo ""
    echo "常用命令:"
    echo "  查看服务状态:        docker-compose -f docker-compose.middleware.yml ps"
    echo "  查看服务日志:        docker-compose -f docker-compose.middleware.yml logs -f [服务名]"
    echo "  停止中间件:          docker-compose -f docker-compose.middleware.yml down"
    echo "  重启服务:            docker-compose -f docker-compose.middleware.yml restart [服务名]"
    echo ""
}

# 等待服务启动
wait_for_services() {
    log_info "等待关键服务启动..."
    
    # 等待MySQL
    log_info "等待MySQL启动..."
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec bjgy-mysql mysqladmin ping -h localhost -u root -p123456 &> /dev/null; then
            log_success "MySQL启动成功"
            break
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log_info "等待MySQL启动... (${attempt}/${max_attempts})"
        fi
        sleep 2
        ((attempt++))
    done
    
    # 等待Nacos
    log_info "等待Nacos启动..."
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8848/nacos/v1/console/health/readiness &> /dev/null; then
            log_success "Nacos启动成功"
            break
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log_info "等待Nacos启动... (${attempt}/${max_attempts})"
        fi
        sleep 2
        ((attempt++))
    done
    
    # 等待Redis
    log_info "等待Redis启动..."
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec bjgy-redis redis-cli ping &> /dev/null; then
            log_success "Redis启动成功"
            break
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log_info "等待Redis启动... (${attempt}/${max_attempts})"
        fi
        sleep 2
        ((attempt++))
    done
    
    log_success "关键服务启动完成"
}

# 主函数
main() {
    local action=${1:-"start"}
    
    case "$action" in
        "start")
            log_info "启动bjgy-cloud中间件服务..."
            check_docker_compose
            create_network
            start_middleware
            wait_for_services
            check_health
            show_services_info
            ;;
        "status")
            check_health
            ;;
        "stop")
            log_info "停止中间件服务..."
            if command -v docker-compose &> /dev/null; then
                docker-compose -f docker-compose.middleware.yml down
            else
                docker compose -f docker-compose.middleware.yml down
            fi
            log_success "中间件服务已停止"
            ;;
        "restart")
            log_info "重启中间件服务..."
            if command -v docker-compose &> /dev/null; then
                docker-compose -f docker-compose.middleware.yml restart
            else
                docker compose -f docker-compose.middleware.yml restart
            fi
            wait_for_services
            log_success "中间件服务已重启"
            ;;
        *)
            echo "用法: $0 [start|status|stop|restart]"
            echo ""
            echo "操作:"
            echo "  start   - 启动中间件服务 (默认)"
            echo "  status  - 检查服务状态"
            echo "  stop    - 停止中间件服务"
            echo "  restart - 重启中间件服务"
            exit 1
            ;;
    esac
}

# 参数处理
case "$1" in
    -h|--help)
        echo "bjgy-cloud 中间件管理脚本"
        echo ""
        echo "用法: $0 [ACTION]"
        echo ""
        echo "操作:"
        echo "  start   - 启动中间件服务 (默认)"
        echo "  status  - 检查服务状态"
        echo "  stop    - 停止中间件服务"
        echo "  restart - 重启中间件服务"
        echo ""
        echo "示例:"
        echo "  $0          # 启动中间件"
        echo "  $0 start    # 启动中间件"
        echo "  $0 status   # 检查状态"
        echo "  $0 stop     # 停止中间件"
        echo "  $0 restart  # 重启中间件"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
