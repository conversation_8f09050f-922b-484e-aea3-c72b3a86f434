# 生产环境配置
PROFILE=prod
VERSION=v2.0.0

# 中间件连接配置 - 生产环境
NACOS_HOST=prod-nacos.bjgy.com
NACOS_PORT=8848
NACOS_NAMESPACE=prod

# 数据库配置
MYSQL_HOST=prod-mysql.bjgy.com
MYSQL_PORT=3306
MYSQL_DATABASE=srt_cloud_prod
MYSQL_USERNAME=bjgy_prod
MYSQL_PASSWORD=prod_strong_password_2024

# Redis配置
REDIS_HOST=prod-redis.bjgy.com
REDIS_PORT=6379
REDIS_PASSWORD=prod_redis_strong_password
REDIS_DATABASE=0

# JVM配置 - 生产环境优化
JAVA_OPTS_GATEWAY=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_SYSTEM=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_INTEGRATE=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_DEVELOPMENT=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_SERVICE=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_GOVERNANCE=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_ASSETS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_DATA_BI=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_QUARTZ=-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_MESSAGE=-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
JAVA_OPTS_MONITOR=-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps

# 日志配置 - 生产环境
LOGGING_LEVEL_ROOT=WARN
LOGGING_LEVEL_COM_BJGY=INFO
LOGGING_PATTERN_CONSOLE=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
LOGGING_PATTERN_FILE=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# 监控配置 - 生产环境
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED=true

# 安全配置 - 生产环境
SECURITY_JWT_SECRET=prod-super-strong-jwt-secret-key-2024-bjgy-cloud
SECURITY_JWT_EXPIRATION=7200

# 连接池配置 - 生产环境优化
SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=50
SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=10
SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT=30000
SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT=600000
SPRING_DATASOURCE_HIKARI_MAX_LIFETIME=1800000
SPRING_DATASOURCE_HIKARI_LEAK_DETECTION_THRESHOLD=60000

# Feign配置 - 生产环境
FEIGN_CLIENT_CONFIG_DEFAULT_CONNECT_TIMEOUT=10000
FEIGN_CLIENT_CONFIG_DEFAULT_READ_TIMEOUT=30000
FEIGN_HYSTRIX_ENABLED=true

# 线程池配置 - 生产环境
SPRING_TASK_EXECUTION_POOL_CORE_SIZE=16
SPRING_TASK_EXECUTION_POOL_MAX_SIZE=32
SPRING_TASK_EXECUTION_POOL_QUEUE_CAPACITY=200

# 缓存配置
SPRING_CACHE_TYPE=redis
SPRING_CACHE_REDIS_TIME_TO_LIVE=7200000

# 性能配置
SERVER_TOMCAT_MAX_THREADS=200
SERVER_TOMCAT_MIN_SPARE_THREADS=10
SERVER_TOMCAT_MAX_CONNECTIONS=8192
SERVER_TOMCAT_ACCEPT_COUNT=100

# 文件上传配置 - 生产环境
SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=500MB
SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=500MB
SPRING_SERVLET_MULTIPART_LOCATION=/tmp

# 健康检查配置
MANAGEMENT_HEALTH_DISKSPACE_ENABLED=true
MANAGEMENT_HEALTH_DISKSPACE_THRESHOLD=1GB
MANAGEMENT_HEALTH_DB_ENABLED=true
MANAGEMENT_HEALTH_REDIS_ENABLED=true

# 外部服务配置
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRY_COUNT=3

# 消息队列配置 - 生产环境
SPRING_RABBITMQ_HOST=prod-rabbitmq.bjgy.com
SPRING_RABBITMQ_PORT=5672
SPRING_RABBITMQ_USERNAME=bjgy_mq_prod
SPRING_RABBITMQ_PASSWORD=prod_rabbitmq_password
SPRING_RABBITMQ_VIRTUAL_HOST=/bjgy-cloud-prod

# Elasticsearch配置 - 生产环境
SPRING_ELASTICSEARCH_URIS=http://prod-elasticsearch.bjgy.com:9200
SPRING_ELASTICSEARCH_USERNAME=elastic
SPRING_ELASTICSEARCH_PASSWORD=prod_elasticsearch_password

# MinIO配置 - 生产环境
MINIO_ENDPOINT=http://prod-minio.bjgy.com:9000
MINIO_ACCESS_KEY=prod_minio_access_key
MINIO_SECRET_KEY=prod_minio_secret_key
MINIO_BUCKET_NAME=bjgy-cloud-prod
