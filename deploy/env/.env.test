# 测试环境配置
PROFILE=test
VERSION=test

# 中间件连接配置 - 测试环境
NACOS_HOST=test-nacos.bjgy.com
NACOS_PORT=8848
NACOS_NAMESPACE=test

# 数据库配置
MYSQL_HOST=test-mysql.bjgy.com
MYSQL_PORT=3306
MYSQL_DATABASE=srt_cloud_test
MYSQL_USERNAME=bjgy_test
MYSQL_PASSWORD=test_password_123

# Redis配置
REDIS_HOST=test-redis.bjgy.com
REDIS_PORT=6379
REDIS_PASSWORD=test_redis_password
REDIS_DATABASE=1

# JVM配置 - 测试环境
JAVA_OPTS_GATEWAY=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_SYSTEM=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_INTEGRATE=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_DEVELOPMENT=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_SERVICE=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_GOVERNANCE=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_ASSETS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_DATA_BI=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_QUARTZ=-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_MESSAGE=-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_MONITOR=-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# 日志配置
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_BJGY=INFO

# 监控配置
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized

# 安全配置
SECURITY_JWT_SECRET=test-jwt-secret-key-2024
SECURITY_JWT_EXPIRATION=3600

# 连接池配置
SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=30
SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=5
SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT=30000

# Feign配置
FEIGN_CLIENT_CONFIG_DEFAULT_CONNECT_TIMEOUT=30000
FEIGN_CLIENT_CONFIG_DEFAULT_READ_TIMEOUT=60000

# 文件上传配置
SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=200MB
SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=200MB
