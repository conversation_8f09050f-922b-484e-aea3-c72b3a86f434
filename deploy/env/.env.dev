# 开发环境配置
PROFILE=dev
VERSION=latest

# 中间件连接配置 - 开发环境
NACOS_HOST=localhost
NACOS_PORT=8848
NACOS_NAMESPACE=

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=srt_cloud
MYSQL_USERNAME=root
MYSQL_PASSWORD=123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JVM配置 - 开发环境
JAVA_OPTS_GATEWAY=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_SYSTEM=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_DATA_INTEGRATE=-Xms512m -Xmx1g -XX:+UseG1GC
JAVA_OPTS_DATA_DEVELOPMENT=-Xms512m -Xmx1g -XX:+UseG1GC
JAVA_OPTS_DATA_SERVICE=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_DATA_GOVERNANCE=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_DATA_ASSETS=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_DATA_BI=-Xms256m -Xmx512m -XX:+UseG1GC
JAVA_OPTS_QUARTZ=-Xms128m -Xmx256m -XX:+UseG1GC
JAVA_OPTS_MESSAGE=-Xms128m -Xmx256m -XX:+UseG1GC
JAVA_OPTS_MONITOR=-Xms128m -Xmx256m -XX:+UseG1GC

# 日志配置
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_BJGY=DEBUG

# 监控配置
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=*
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always

# 开发环境特殊配置
SPRING_DEVTOOLS_RESTART_ENABLED=true
SPRING_DEVTOOLS_LIVERELOAD_ENABLED=true
