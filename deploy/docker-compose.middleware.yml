version: '3.8'

# 中间件服务 - 独立部署
# 可以单独启动，为应用程序提供基础服务

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: bjgy-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=srt_cloud
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../db:/docker-entrypoint-initdb.d
    networks:
      - bjgy-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: bjgy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bjgy-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ""
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nacos 服务注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: bjgy-nacos
    environment:
      - MODE=standalone
      - PREFER_HOST_MODE=hostname
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=nacos_config
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=123456
      - NACOS_AUTH_ENABLE=true
      - NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789
      - NACOS_AUTH_IDENTITY_KEY=serverIdentity
      - NACOS_AUTH_IDENTITY_VALUE=security
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - bjgy-network
    restart: unless-stopped
    volumes:
      - nacos_logs:/home/<USER>/logs
      - nacos_data:/home/<USER>/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: bjgy-minio
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - bjgy-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: bjgy-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
      - RABBITMQ_DEFAULT_VHOST=/bjgy-cloud
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:7.17.9
    container_name: bjgy-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - bootstrap.memory_lock=true
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - bjgy-network
    restart: unless-stopped
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana
  kibana:
    image: kibana:7.17.9
    container_name: bjgy-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=bjgy-kibana
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Zookeeper (Flink集群需要)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: bjgy-zookeeper
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
      - ZOOKEEPER_SYNC_LIMIT=2
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Flink JobManager
  flink-jobmanager:
    image: flink:1.16.2-scala_2.12-java8
    container_name: bjgy-flink-jobmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      - FLINK_PROPERTIES=jobmanager.rpc.address: flink-jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    volumes:
      - flink_data:/opt/flink/data
      - flink_logs:/opt/flink/log
    networks:
      - bjgy-network
    restart: unless-stopped
    depends_on:
      zookeeper:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/overview"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Flink TaskManager
  flink-taskmanager:
    image: flink:1.16.2-scala_2.12-java8
    container_name: bjgy-flink-taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      - FLINK_PROPERTIES=jobmanager.rpc.address: flink-jobmanager
    command: taskmanager
    volumes:
      - flink_data:/opt/flink/data
      - flink_logs:/opt/flink/log
    networks:
      - bjgy-network
    restart: unless-stopped
    depends_on:
      flink-jobmanager:
        condition: service_healthy
    deploy:
      replicas: 2

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: bjgy-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - bjgy-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: bjgy-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - bjgy-network
    restart: unless-stopped
    depends_on:
      - prometheus

volumes:
  mysql_data:
  redis_data:
  nacos_logs:
  nacos_data:
  minio_data:
  rabbitmq_data:
  elasticsearch_data:
  zookeeper_data:
  zookeeper_logs:
  flink_data:
  flink_logs:
  prometheus_data:
  grafana_data:

networks:
  bjgy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
