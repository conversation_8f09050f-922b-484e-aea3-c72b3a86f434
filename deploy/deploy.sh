#!/bin/bash

# bjgy-cloud Docker部署脚本
# 用于部署所有微服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker版本: $(docker --version)"
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose版本: $(docker-compose --version)"
    else
        log_info "Docker Compose版本: $(docker compose version)"
    fi
}

# 检查部署文件
check_deploy_files() {
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile文件不存在"
        exit 1
    fi
    
    log_info "部署文件检查通过"
}

# 创建必要的目录和文件
prepare_environment() {
    local profile=${1:-"dev"}

    log_info "准备部署环境 (profile: $profile)..."

    # 创建日志目录
    mkdir -p logs/{gateway,system,data-integrate,data-development,data-service,data-governance,data-assets,data-bi,quartz,message,monitor}

    # 创建网络
    if ! docker network ls | grep -q "bjgy-network"; then
        log_info "创建Docker网络: bjgy-network"
        docker network create bjgy-network
    fi

    # 检查环境配置文件
    local env_file="env/.env.${profile}"
    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi

    # 导出环境变量
    export $(grep -v '^#' "$env_file" | xargs)
    log_info "已加载环境配置: $env_file"

    log_success "环境准备完成"
}

# 构建镜像
build_images() {
    log_info "开始构建Docker镜像..."
    
    if [ -f "build-images.sh" ]; then
        chmod +x build-images.sh
        ./build-images.sh "$1"
    else
        log_warning "build-images.sh不存在，跳过镜像构建"
    fi
}

# 启动中间件服务
start_middleware() {
    log_info "启动中间件服务..."

    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.middleware.yml up -d
    else
        docker compose -f docker-compose.middleware.yml up -d
    fi

    log_info "等待中间件服务启动..."
    sleep 60

    # 检查关键服务是否启动成功
    check_middleware_health
}

# 检查中间件健康状态
check_middleware_health() {
    local services=("mysql:3306" "redis:6379" "nacos:8848")

    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"

        local max_attempts=30
        local attempt=1

        while [ $attempt -le $max_attempts ]; do
            case "$service" in
                "mysql")
                    if docker exec bjgy-mysql mysqladmin ping -h localhost -u root -p123456 &> /dev/null; then
                        log_success "MySQL启动成功"
                        break
                    fi
                    ;;
                "redis")
                    if docker exec bjgy-redis redis-cli ping &> /dev/null; then
                        log_success "Redis启动成功"
                        break
                    fi
                    ;;
                "nacos")
                    if curl -f http://localhost:8848/nacos/v1/console/health/readiness &> /dev/null; then
                        log_success "Nacos启动成功"
                        break
                    fi
                    ;;
            esac

            log_info "等待${service}启动... (${attempt}/${max_attempts})"
            sleep 10
            ((attempt++))
        done

        if [ $attempt -gt $max_attempts ]; then
            log_error "${service}启动超时"
            exit 1
        fi
    done
}

# 启动应用程序服务
start_applications() {
    local profile=${1:-"dev"}

    log_info "启动应用程序服务 (profile: $profile)..."

    # 按顺序启动服务
    local services=(
        "gateway"
        "system"
        "data-integrate"
        "data-development"
        "data-service"
        "data-governance"
        "data-assets"
        "data-bi"
        "quartz"
        "message"
        "monitor"
    )

    for service in "${services[@]}"; do
        log_info "启动服务: $service"

        if command -v docker-compose &> /dev/null; then
            docker-compose --env-file "env/.env.${profile}" up -d "$service"
        else
            docker compose --env-file "env/.env.${profile}" up -d "$service"
        fi

        # 等待服务启动
        sleep 15
    done
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
    
    echo ""
    log_info "服务健康检查..."
    
    # 检查各服务健康状态
    local services=(
        "gateway:8082"
        "system:8083"
        "data-integrate:8084"
        "data-development:8085"
        "data-service:8086"
        "data-governance:8087"
        "data-assets:8088"
        "data-bi:8089"
        "quartz:8090"
        "message:8091"
        "monitor:8092"
    )
    
    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        
        if curl -f "http://localhost:${port}/actuator/health" &> /dev/null; then
            log_success "${service} (端口:${port}) - 健康"
        else
            log_warning "${service} (端口:${port}) - 不健康或未启动"
        fi
    done
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "bjgy-cloud部署完成！"
    echo ""
    echo "服务访问地址:"
    echo "  Nacos控制台:     http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
    echo "  API网关:         http://localhost:8082"
    echo "  系统服务:        http://localhost:8083"
    echo "  数据集成服务:    http://localhost:8084"
    echo "  数据开发服务:    http://localhost:8085"
    echo "  数据服务:        http://localhost:8086"
    echo "  数据治理服务:    http://localhost:8087"
    echo "  数据资产服务:    http://localhost:8088"
    echo "  数据BI服务:      http://localhost:8089"
    echo "  定时任务服务:    http://localhost:8090"
    echo "  消息服务:        http://localhost:8091"
    echo "  监控服务:        http://localhost:8092"
    echo ""
    echo "数据库连接:"
    echo "  MySQL:           localhost:3306 (用户名/密码: root/123456)"
    echo "  Redis:           localhost:6379"
    echo ""
    echo "常用命令:"
    echo "  查看日志:        docker-compose logs -f [服务名]"
    echo "  停止服务:        ./stop.sh"
    echo "  重启服务:        docker-compose restart [服务名]"
    echo ""
}

# 主函数
main() {
    local mode=${1:-"apps"}
    local profile=${2:-"dev"}
    local version=${3:-"latest"}

    log_info "开始部署bjgy-cloud微服务平台..."
    log_info "部署模式: $mode"
    log_info "环境配置: $profile"
    log_info "镜像版本: $version"

    # 检查环境
    check_requirements
    check_deploy_files
    prepare_environment "$profile"

    case "$mode" in
        "middleware")
            start_middleware
            ;;
        "apps")
            build_images "$version"
            start_applications "$profile"
            check_services
            show_deployment_info
            ;;
        "build")
            build_images "$version"
            ;;
        "full")
            start_middleware
            build_images "$version"
            start_applications "$profile"
            check_services
            show_deployment_info
            ;;
        *)
            log_error "未知的部署模式: $mode"
            show_help
            exit 1
            ;;
    esac
}

# 帮助信息
show_help() {
    echo "用法: $0 [MODE] [PROFILE] [VERSION]"
    echo ""
    echo "参数:"
    echo "  MODE       部署模式:"
    echo "             apps       - 仅部署应用程序 (默认)"
    echo "             middleware - 仅部署中间件"
    echo "             build      - 仅构建镜像"
    echo "             full       - 完整部署 (中间件+应用程序)"
    echo "  PROFILE    环境配置:"
    echo "             dev        - 开发环境 (默认)"
    echo "             test       - 测试环境"
    echo "             prod       - 生产环境"
    echo "  VERSION    镜像版本标签 (默认: latest)"
    echo ""
    echo "示例:"
    echo "  $0                           # 部署应用程序，开发环境"
    echo "  $0 apps dev latest          # 部署应用程序，开发环境，latest版本"
    echo "  $0 middleware               # 仅部署中间件"
    echo "  $0 apps test v2.0.0         # 部署应用程序，测试环境，v2.0.0版本"
    echo "  $0 apps prod v2.0.0         # 部署应用程序，生产环境，v2.0.0版本"
    echo "  $0 build                    # 仅构建镜像"
    echo "  $0 full dev                 # 完整部署，开发环境"
    echo ""
    echo "环境配置文件:"
    echo "  env/.env.dev   - 开发环境配置"
    echo "  env/.env.test  - 测试环境配置"
    echo "  env/.env.prod  - 生产环境配置"
}

# 参数处理
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
