#!/bin/bash

# bjgy-cloud Docker镜像构建脚本
# 用于构建所有微服务的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    log_info "Docker版本: $(docker --version)"
}

# 检查项目根目录
check_project_root() {
    if [ ! -f "../pom.xml" ]; then
        log_error "请在deploy目录下执行此脚本"
        exit 1
    fi
}

# 构建单个服务镜像
build_service_image() {
    local service_name=$1
    local image_tag=$2
    
    log_info "开始构建 ${service_name} 镜像..."
    
    if docker build -t "${image_tag}" \
        --build-arg SERVICE_NAME="${service_name}" \
        -f Dockerfile \
        ..; then
        log_success "${service_name} 镜像构建成功: ${image_tag}"
    else
        log_error "${service_name} 镜像构建失败"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始构建bjgy-cloud微服务Docker镜像..."
    
    # 检查环境
    check_docker
    check_project_root
    
    # 获取版本号
    VERSION=${1:-"latest"}
    log_info "使用版本标签: ${VERSION}"
    
    # 定义服务列表
    declare -A services=(
        ["bjgy-cloud-gateway"]="bjgy-cloud/gateway:${VERSION}"
        ["bjgy-cloud-system"]="bjgy-cloud/system:${VERSION}"
        ["bjgy-cloud-data-integrate"]="bjgy-cloud/data-integrate:${VERSION}"
        ["bjgy-cloud-data-development/bjgy-cloud-develop-server"]="bjgy-cloud/data-development:${VERSION}"
        ["bjgy-cloud-data-service"]="bjgy-cloud/data-service:${VERSION}"
        ["bjgy-cloud-data-governance"]="bjgy-cloud/data-governance:${VERSION}"
        ["bjgy-cloud-data-assets"]="bjgy-cloud/data-assets:${VERSION}"
        ["bjgy-cloud-data-bi"]="bjgy-cloud/data-bi:${VERSION}"
        ["bjgy-cloud-module/bjgy-cloud-quartz"]="bjgy-cloud/quartz:${VERSION}"
        ["bjgy-cloud-module/bjgy-cloud-message"]="bjgy-cloud/message:${VERSION}"
        ["bjgy-cloud-module/bjgy-cloud-monitor"]="bjgy-cloud/monitor:${VERSION}"
    )
    
    # 构建计数器
    local success_count=0
    local total_count=${#services[@]}
    
    # 构建所有服务镜像
    for service_name in "${!services[@]}"; do
        image_tag="${services[$service_name]}"
        
        if build_service_image "$service_name" "$image_tag"; then
            ((success_count++))
        else
            log_warning "跳过 ${service_name} 镜像构建"
        fi
        
        echo "----------------------------------------"
    done
    
    # 构建结果统计
    log_info "构建完成统计:"
    log_info "成功: ${success_count}/${total_count}"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有镜像构建成功！"
    else
        log_warning "部分镜像构建失败，请检查日志"
    fi
    
    # 显示构建的镜像
    log_info "已构建的镜像列表:"
    docker images | grep "bjgy-cloud" | head -20
}

# 帮助信息
show_help() {
    echo "用法: $0 [VERSION]"
    echo ""
    echo "参数:"
    echo "  VERSION    镜像版本标签 (默认: latest)"
    echo ""
    echo "示例:"
    echo "  $0                # 使用latest标签"
    echo "  $0 v2.0.0        # 使用v2.0.0标签"
    echo "  $0 dev           # 使用dev标签"
}

# 参数处理
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
