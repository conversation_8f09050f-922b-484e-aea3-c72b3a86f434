# 多阶段构建 Dockerfile for bjgy-cloud 微服务
FROM maven:3.8.6-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制 pom.xml 文件
COPY pom.xml .
COPY bjgy-cloud-framework/pom.xml bjgy-cloud-framework/
COPY bjgy-cloud-api/pom.xml bjgy-cloud-api/
COPY bjgy-cloud-module/pom.xml bjgy-cloud-module/
COPY bjgy-cloud-data-integrate/pom.xml bjgy-cloud-data-integrate/
COPY bjgy-cloud-system/pom.xml bjgy-cloud-system/
COPY bjgy-cloud-gateway/pom.xml bjgy-cloud-gateway/
COPY bjgy-cloud-data-development/pom.xml bjgy-cloud-data-development/
COPY bjgy-cloud-data-service/pom.xml bjgy-cloud-data-service/
COPY bjgy-cloud-data-governance/pom.xml bjgy-cloud-data-governance/
COPY bjgy-cloud-data-assets/pom.xml bjgy-cloud-data-assets/
COPY bjgy-cloud-data-bi/pom.xml bjgy-cloud-data-bi/

# 复制子模块 pom.xml
COPY bjgy-cloud-framework/bjgy-cloud-common/pom.xml bjgy-cloud-framework/bjgy-cloud-common/
COPY bjgy-cloud-framework/bjgy-cloud-security/pom.xml bjgy-cloud-framework/bjgy-cloud-security/
COPY bjgy-cloud-framework/bjgy-cloud-mybatis/pom.xml bjgy-cloud-framework/bjgy-cloud-mybatis/
COPY bjgy-cloud-framework/bjgy-cloud-dbswitch/pom.xml bjgy-cloud-framework/bjgy-cloud-dbswitch/
COPY bjgy-cloud-framework/bjgy-cloud-flink/pom.xml bjgy-cloud-framework/bjgy-cloud-flink/
COPY bjgy-cloud-framework/bjgy-cloud-data-lineage/pom.xml bjgy-cloud-framework/bjgy-cloud-data-lineage/
COPY bjgy-cloud-framework/bjgy-cloud-seatunnel-rest/pom.xml bjgy-cloud-framework/bjgy-cloud-seatunnel-rest/

COPY bjgy-cloud-module/bjgy-cloud-quartz/pom.xml bjgy-cloud-module/bjgy-cloud-quartz/
COPY bjgy-cloud-module/bjgy-cloud-message/pom.xml bjgy-cloud-module/bjgy-cloud-message/
COPY bjgy-cloud-module/bjgy-cloud-monitor/pom.xml bjgy-cloud-module/bjgy-cloud-monitor/

COPY bjgy-cloud-data-development/bjgy-cloud-develop-base/pom.xml bjgy-cloud-data-development/bjgy-cloud-develop-base/
COPY bjgy-cloud-data-development/bjgy-cloud-develop-etl/pom.xml bjgy-cloud-data-development/bjgy-cloud-develop-etl/
COPY bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.14/pom.xml bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.14/
COPY bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.16/pom.xml bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.16/
COPY bjgy-cloud-data-development/bjgy-cloud-develop-server/pom.xml bjgy-cloud-data-development/bjgy-cloud-develop-server/

# 下载依赖
RUN mvn dependency:go-offline -B

# 复制源代码
COPY . .

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行时镜像
FROM openjdk:8-jre-alpine

# 安装必要的工具
RUN apk add --no-cache curl bash tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1000 app && adduser -u 1000 -G app -s /bin/sh -D app

# 设置工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs && chown -R app:app /app

# 切换到应用用户
USER app

# 复制构建的jar文件
ARG SERVICE_NAME
COPY --from=builder --chown=app:app /app/${SERVICE_NAME}/target/*.jar app.jar

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:${SERVER_PORT:-8080}/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.profiles.active=${SPRING_PROFILES_ACTIVE:-dev}", "-jar", "app.jar"]
