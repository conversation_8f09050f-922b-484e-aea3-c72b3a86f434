# bjgy-cloud Docker部署指南

本目录包含了bjgy-cloud微服务平台的Docker部署脚本和配置文件，采用**中间件与应用程序分离**的架构设计。

## 🏗️ 架构设计

### 分离式部署架构
- **中间件服务**: MySQL、Redis、Nacos、MinIO、RabbitMQ、Elasticsearch等基础服务
- **应用程序服务**: 11个Spring Boot微服务应用
- **环境隔离**: 通过profile区分开发、测试、生产环境
- **独立部署**: 中间件和应用程序可以独立部署和管理

## 📁 目录结构

```
deploy/
├── Dockerfile                    # 应用程序多阶段构建Dockerfile
├── docker-compose.yml          # 应用程序服务配置
├── docker-compose.middleware.yml # 中间件服务配置
├── build-images.sh             # 应用镜像构建脚本
├── deploy.sh                   # 统一部署脚本
├── start-middleware.sh         # 中间件启动脚本
├── start-apps.sh               # 应用程序启动脚本
├── stop.sh                     # 服务停止脚本
├── env/                        # 环境配置目录
│   ├── .env.dev               # 开发环境配置
│   ├── .env.test              # 测试环境配置
│   └── .env.prod              # 生产环境配置
├── nacos_config.zip           # Nacos配置文件
└── README.md                  # 本文档
```

## 🚀 快速开始

### 1. 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 8GB 内存
- 至少 20GB 磁盘空间

### 2. 分离式部署 (推荐)

#### 步骤1: 启动中间件
```bash
cd deploy
chmod +x *.sh

# 启动所有中间件服务
./start-middleware.sh
```

#### 步骤2: 启动应用程序
```bash
# 开发环境
./start-apps.sh start dev latest

# 测试环境
./start-apps.sh start test v2.0.0

# 生产环境
./start-apps.sh start prod v2.0.0
```

### 3. 一键部署

```bash
# 完整部署 - 开发环境
./deploy.sh full dev

# 完整部署 - 生产环境
./deploy.sh full prod v2.0.0

# 仅部署应用程序 - 开发环境
./deploy.sh apps dev

# 仅部署中间件
./deploy.sh middleware
```

### 4. 开发环境快速启动

```bash
# 启动开发环境所需的基础服务
docker-compose -f docker-compose.dev.yml up -d
```

## 📋 服务列表

### 中间件服务
| 服务名称 | 端口 | 描述 | 访问地址 |
|---------|------|------|----------|
| mysql | 3306 | 数据库 | localhost:3306 |
| redis | 6379 | 缓存 | localhost:6379 |
| nacos | 8848 | 服务注册中心 | http://localhost:8848/nacos |
| minio | 9000/9001 | 对象存储 | http://localhost:9001 |
| rabbitmq | 5672/15672 | 消息队列 | http://localhost:15672 |
| elasticsearch | 9200 | 搜索引擎 | http://localhost:9200 |
| kibana | 5601 | 日志可视化 | http://localhost:5601 |
| flink-jobmanager | 8081 | Flink集群 | http://localhost:8081 |
| prometheus | 9090 | 监控系统 | http://localhost:9090 |
| grafana | 3000 | 监控可视化 | http://localhost:3000 |

### 应用程序服务
| 服务名称 | 端口 | 描述 | 健康检查 |
|---------|------|------|----------|
| gateway | 8082 | API网关 | http://localhost:8082/actuator/health |
| system | 8083 | 系统服务 | http://localhost:8083/actuator/health |
| data-integrate | 8084 | 数据集成服务 | http://localhost:8084/actuator/health |
| data-development | 8085 | 数据开发服务 | http://localhost:8085/actuator/health |
| data-service | 8086 | 数据服务 | http://localhost:8086/actuator/health |
| data-governance | 8087 | 数据治理服务 | http://localhost:8087/actuator/health |
| data-assets | 8088 | 数据资产服务 | http://localhost:8088/actuator/health |
| data-bi | 8089 | 数据BI服务 | http://localhost:8089/actuator/health |
| quartz | 8090 | 定时任务服务 | http://localhost:8090/actuator/health |
| message | 8091 | 消息服务 | http://localhost:8091/actuator/health |
| monitor | 8092 | 监控服务 | http://localhost:8092/actuator/health |

## 🛠️ 常用命令

### 中间件管理
```bash
# 启动中间件
./start-middleware.sh

# 检查中间件状态
./start-middleware.sh status

# 停止中间件
./start-middleware.sh stop

# 重启中间件
./start-middleware.sh restart
```

### 应用程序管理
```bash
# 启动应用程序 - 开发环境
./start-apps.sh start dev latest

# 启动应用程序 - 生产环境
./start-apps.sh start prod v2.0.0

# 检查应用程序状态
./start-apps.sh status

# 停止应用程序
./start-apps.sh stop

# 重启应用程序
./start-apps.sh restart dev
```

### 镜像构建
```bash
# 构建所有应用镜像
./build-images.sh latest

# 构建指定版本镜像
./build-images.sh v2.0.0
```

### 统一部署
```bash
# 完整部署 - 开发环境
./deploy.sh full dev

# 仅部署中间件
./deploy.sh middleware

# 仅部署应用程序
./deploy.sh apps prod v2.0.0
```

### Docker Compose命令
```bash
# 中间件服务
docker-compose -f docker-compose.middleware.yml ps
docker-compose -f docker-compose.middleware.yml logs -f [服务名]

# 应用程序服务
docker-compose --env-file env/.env.dev ps
docker-compose --env-file env/.env.dev logs -f [服务名]
```

## ⚙️ 配置说明

### 环境配置文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `env/.env.dev` | 开发环境 | 本地开发，连接本地中间件 |
| `env/.env.test` | 测试环境 | 连接测试环境中间件 |
| `env/.env.prod` | 生产环境 | 连接生产环境中间件，高性能配置 |

### 环境变量说明

#### 基础配置
- `PROFILE`: 环境标识 (dev/test/prod)
- `VERSION`: 镜像版本标签
- `NACOS_HOST/PORT/NAMESPACE`: Nacos连接配置
- `MYSQL_HOST/PORT/DATABASE`: 数据库连接配置
- `REDIS_HOST/PORT/PASSWORD`: Redis连接配置

#### JVM配置
每个服务都有独立的JVM配置，格式为 `JAVA_OPTS_[服务名]`：
- 开发环境: 较小内存配置 (256m-1g)
- 测试环境: 中等内存配置 (512m-2g)
- 生产环境: 高性能配置 (1g-4g) + GC调优

### Nacos配置

1. 启动中间件后，访问 http://localhost:8848/nacos
2. 使用用户名/密码: nacos/nacos 登录
3. 导入 `nacos_config.zip` 配置文件
4. 根据环境修改 `datasource.yaml` 中的连接信息

### 数据库初始化

- 中间件启动时会自动初始化数据库
- SQL脚本位于 `../db/` 目录
- 支持增量更新脚本

## 🚀 生产环境部署

### 方案一: 分离式部署 (推荐)

#### 1. 准备生产环境配置
```bash
# 编辑生产环境配置
vim env/.env.prod

# 修改关键配置项:
# - NACOS_HOST: 生产环境Nacos地址
# - MYSQL_HOST: 生产环境数据库地址
# - REDIS_HOST: 生产环境Redis地址
# - 各种密码和密钥
```

#### 2. 部署中间件 (如果需要)
```bash
# 如果需要在本地部署中间件
./start-middleware.sh

# 或者使用外部中间件，跳过此步骤
```

#### 3. 部署应用程序
```bash
# 构建生产镜像
./build-images.sh v2.0.0

# 部署应用程序
./start-apps.sh start prod v2.0.0
```

### 方案二: 一键部署

```bash
# 完整部署 (中间件 + 应用程序)
./deploy.sh full prod v2.0.0

# 仅部署应用程序 (连接外部中间件)
./deploy.sh apps prod v2.0.0
```

### 生产环境网络配置

```bash
# 创建生产网络 (如果不存在)
docker network create bjgy-network
```

## 📊 监控和日志

### 健康检查

#### 应用程序健康检查
- 健康检查URL: `http://localhost:{port}/actuator/health`
- 检查间隔: 30秒
- 超时时间: 10秒
- 重试次数: 3次
- 启动等待时间: 60-90秒

#### 中间件健康检查
- MySQL: `mysqladmin ping`
- Redis: `redis-cli ping`
- Nacos: `curl http://localhost:8848/nacos/v1/console/health/readiness`
- 其他服务: HTTP健康检查端点

### 日志管理

#### 应用程序日志
```bash
# 容器日志
docker-compose logs -f [服务名]

# 应用日志文件 (挂载到宿主机)
./logs/gateway/
./logs/system/
./logs/data-integrate/
# ... 其他服务
```

#### 中间件日志
```bash
# 中间件容器日志
docker-compose -f docker-compose.middleware.yml logs -f [服务名]

# 持久化日志
nacos_logs/
flink_logs/
```

### 性能监控

#### 应用程序监控
- Actuator端点: `/actuator/metrics`
- Prometheus指标: `/actuator/prometheus` (生产环境)
- JVM监控: `/actuator/metrics/jvm.*`
- 自定义指标: `/actuator/metrics/custom.*`

#### 中间件监控
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin123)
- Flink Web UI: http://localhost:8081
- Nacos监控: http://localhost:8848/nacos

## 🔧 故障排除

### 常见问题

#### 1. 应用程序启动失败
```bash
# 查看应用程序日志
./start-apps.sh status
docker-compose logs [服务名]

# 检查环境配置
cat env/.env.dev  # 或对应环境

# 检查网络连接
docker network ls | grep bjgy-network
```

#### 2. 中间件连接失败
```bash
# 检查中间件状态
./start-middleware.sh status

# 检查Nacos连接
curl http://localhost:8848/nacos/v1/console/health/readiness

# 检查数据库连接
docker exec bjgy-mysql mysqladmin ping -h localhost -u root -p123456

# 检查Redis连接
docker exec bjgy-redis redis-cli ping
```

#### 3. 服务注册失败
```bash
# 检查Nacos服务列表
curl "http://localhost:8848/nacos/v1/ns/instance/list?serviceName=bjgy-cloud-gateway"

# 检查应用程序配置
# 确认 NACOS_HOST 配置正确
grep NACOS_HOST env/.env.dev
```

#### 4. 内存不足
```bash
# 查看容器资源使用
docker stats

# 调整JVM内存配置
# 编辑对应环境的 .env 文件中的 JAVA_OPTS_* 变量
vim env/.env.dev
```

#### 5. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8082

# 修改端口配置
# 在 docker-compose.yml 中修改端口映射
```

### 性能优化建议

#### 1. JVM调优
```bash
# 开发环境 - 较小内存配置
JAVA_OPTS_GATEWAY=-Xms256m -Xmx512m -XX:+UseG1GC

# 生产环境 - 高性能配置
JAVA_OPTS_GATEWAY=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+PrintGCDetails
```

#### 2. 数据库优化
```bash
# 调整连接池配置
SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=50
SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=10

# 监控连接池使用情况
curl http://localhost:8083/actuator/metrics/hikaricp.connections.active
```

#### 3. 网络优化
```bash
# 使用专用网络
docker network create bjgy-network

# 调整超时参数
FEIGN_CLIENT_CONFIG_DEFAULT_CONNECT_TIMEOUT=10000
FEIGN_CLIENT_CONFIG_DEFAULT_READ_TIMEOUT=30000
```

#### 4. 容器资源限制
```yaml
# 在生产环境中设置资源限制
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
    reservations:
      memory: 1G
      cpus: '0.5'
```

## 安全建议

1. **生产环境安全**
   - 修改默认密码
   - 使用强密码
   - 配置防火墙规则
   - 启用HTTPS

2. **数据安全**
   - 定期备份数据
   - 加密敏感配置
   - 限制网络访问
   - 监控异常访问

3. **容器安全**
   - 使用非root用户运行
   - 定期更新基础镜像
   - 扫描安全漏洞
   - 限制容器权限

## 💾 备份和恢复

### 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -uroot -p123456 srt_cloud > backup.sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE

# 备份Nacos配置
# 通过Nacos控制台导出配置
```

### 数据恢复

```bash
# 恢复MySQL数据
docker-compose exec -T mysql mysql -uroot -p123456 srt_cloud < backup.sql

# 恢复Nacos配置
# 通过Nacos控制台导入配置
```

## 📚 最佳实践

### 开发环境
1. 使用 `./start-middleware.sh` 启动本地中间件
2. 使用 `./start-apps.sh start dev` 启动应用程序
3. 开启热重载和调试模式
4. 使用较小的JVM内存配置

### 测试环境
1. 连接独立的测试环境中间件
2. 使用 `./start-apps.sh start test` 部署
3. 配置适中的资源限制
4. 启用必要的监控指标

### 生产环境
1. 使用外部高可用中间件集群
2. 使用 `./start-apps.sh start prod v2.0.0` 部署
3. 配置完整的监控和告警
4. 定期备份数据和配置
5. 实施蓝绿部署或滚动更新

### 安全建议
1. 修改所有默认密码
2. 使用强密码和密钥
3. 限制网络访问权限
4. 定期更新镜像和依赖
5. 启用容器安全扫描

## 📞 联系支持

如有问题，请联系：
- **技术支持**: <EMAIL>
- **文档地址**: https://docs.bjgy.com
- **问题反馈**: https://github.com/bjgy/bjgy-cloud/issues
- **社区讨论**: https://github.com/bjgy/bjgy-cloud/discussions

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 开源协议。
