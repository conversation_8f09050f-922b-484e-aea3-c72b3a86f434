#!/bin/bash

# bjgy-cloud Docker停止脚本
# 用于停止所有微服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有bjgy-cloud服务..."
    
    if [ -f "docker-compose.yml" ]; then
        if command -v docker-compose &> /dev/null; then
            docker-compose down
        else
            docker compose down
        fi
        log_success "所有服务已停止"
    else
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
}

# 停止特定服务
stop_specific_services() {
    local services=("$@")
    
    log_info "停止指定服务: ${services[*]}"
    
    for service in "${services[@]}"; do
        log_info "停止服务: $service"
        
        if command -v docker-compose &> /dev/null; then
            docker-compose stop "$service"
        else
            docker compose stop "$service"
        fi
    done
    
    log_success "指定服务已停止"
}

# 停止并删除所有容器、网络、卷
cleanup_all() {
    log_warning "这将删除所有容器、网络和数据卷，数据将丢失！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理所有Docker资源..."
        
        if command -v docker-compose &> /dev/null; then
            docker-compose down -v --remove-orphans
        else
            docker compose down -v --remove-orphans
        fi
        
        # 删除相关镜像
        log_info "删除bjgy-cloud相关镜像..."
        docker images | grep "bjgy-cloud" | awk '{print $3}' | xargs -r docker rmi -f
        
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示服务状态
show_status() {
    log_info "当前服务状态:"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

# 查看服务日志
show_logs() {
    local service=${1:-""}
    
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志:"
        
        if command -v docker-compose &> /dev/null; then
            docker-compose logs -f "$service"
        else
            docker compose logs -f "$service"
        fi
    else
        log_info "查看所有服务日志:"
        
        if command -v docker-compose &> /dev/null; then
            docker-compose logs -f
        else
            docker compose logs -f
        fi
    fi
}

# 重启服务
restart_services() {
    local services=("$@")
    
    if [ ${#services[@]} -eq 0 ]; then
        log_info "重启所有服务..."
        
        if command -v docker-compose &> /dev/null; then
            docker-compose restart
        else
            docker compose restart
        fi
        
        log_success "所有服务已重启"
    else
        log_info "重启指定服务: ${services[*]}"
        
        for service in "${services[@]}"; do
            log_info "重启服务: $service"
            
            if command -v docker-compose &> /dev/null; then
                docker-compose restart "$service"
            else
                docker compose restart "$service"
            fi
        done
        
        log_success "指定服务已重启"
    fi
}

# 主函数
main() {
    local action=${1:-"stop"}
    shift
    
    check_docker_compose
    
    case "$action" in
        "stop")
            if [ $# -eq 0 ]; then
                stop_all_services
            else
                stop_specific_services "$@"
            fi
            ;;
        "cleanup")
            cleanup_all
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$1"
            ;;
        "restart")
            restart_services "$@"
            ;;
        *)
            log_error "未知的操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 帮助信息
show_help() {
    echo "用法: $0 [ACTION] [SERVICES...]"
    echo ""
    echo "操作:"
    echo "  stop      停止服务 (默认)"
    echo "  cleanup   停止并删除所有容器、网络、卷"
    echo "  status    显示服务状态"
    echo "  logs      查看服务日志"
    echo "  restart   重启服务"
    echo ""
    echo "可用服务:"
    echo "  gateway, system, data-integrate, data-development,"
    echo "  data-service, data-governance, data-assets, data-bi,"
    echo "  quartz, message, monitor, mysql, redis, nacos"
    echo ""
    echo "示例:"
    echo "  $0                        # 停止所有服务"
    echo "  $0 stop gateway system    # 停止指定服务"
    echo "  $0 cleanup               # 清理所有资源"
    echo "  $0 status                # 查看服务状态"
    echo "  $0 logs gateway          # 查看网关日志"
    echo "  $0 restart               # 重启所有服务"
    echo "  $0 restart gateway       # 重启网关服务"
}

# 参数处理
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
