version: '3.8'

# 应用程序服务 - 独立部署
# 可以连接到已部署的中间件服务

services:

  # API 网关
  gateway:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-gateway
    image: bjgy-cloud/gateway:${VERSION:-latest}
    container_name: bjgy-gateway-${PROFILE:-dev}
    environment:
      - SPRING_PROFILES_ACTIVE=${PROFILE:-dev}
      - nacos_host=${NACOS_HOST:-localhost}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
      - SERVER_PORT=8082
      - JAVA_OPTS=${JAVA_OPTS_GATEWAY:--Xms512m -Xmx1024m -XX:+UseG1GC}
    ports:
      - "8082:8082"
    volumes:
      - ./logs/gateway:/app/logs
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 系统服务
  system:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-system
    image: bjgy-cloud/system:${VERSION:-latest}
    container_name: bjgy-system-${PROFILE:-dev}
    environment:
      - SPRING_PROFILES_ACTIVE=${PROFILE:-dev}
      - nacos_host=${NACOS_HOST:-localhost}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
      - SERVER_PORT=8083
      - JAVA_OPTS=${JAVA_OPTS_SYSTEM:--Xms512m -Xmx1024m -XX:+UseG1GC}
    ports:
      - "8083:8083"
    volumes:
      - ./logs/system:/app/logs
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 数据集成服务
  data-integrate:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-integrate
    image: bjgy-cloud/data-integrate:${VERSION:-latest}
    container_name: bjgy-data-integrate-${PROFILE:-dev}
    environment:
      - SPRING_PROFILES_ACTIVE=${PROFILE:-dev}
      - nacos_host=${NACOS_HOST:-localhost}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
      - SERVER_PORT=8084
      - JAVA_OPTS=${JAVA_OPTS_DATA_INTEGRATE:--Xms1g -Xmx2g -XX:+UseG1GC}
    ports:
      - "8084:8084"
    volumes:
      - ./logs/data-integrate:/app/logs
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # 数据开发服务
  data-development:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-development/bjgy-cloud-develop-server
    image: bjgy-cloud/data-development:${VERSION:-latest}
    container_name: bjgy-data-development-${PROFILE:-dev}
    environment:
      - SPRING_PROFILES_ACTIVE=${PROFILE:-dev}
      - nacos_host=${NACOS_HOST:-localhost}
      - nacos_port=${NACOS_PORT:-8848}
      - nacos_namespace=${NACOS_NAMESPACE:-}
      - SERVER_PORT=8085
      - JAVA_OPTS=${JAVA_OPTS_DATA_DEVELOPMENT:--Xms1g -Xmx2g -XX:+UseG1GC}
    ports:
      - "8085:8085"
    volumes:
      - ./logs/data-development:/app/logs
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # 数据服务
  data-service:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-service
    container_name: bjgy-data-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8086
    ports:
      - "8086:8086"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据治理服务
  data-governance:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-governance
    container_name: bjgy-data-governance
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8087
    ports:
      - "8087:8087"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据资产服务
  data-assets:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-assets
    container_name: bjgy-data-assets
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8088
    ports:
      - "8088:8088"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据BI服务
  data-bi:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-data-bi
    container_name: bjgy-data-bi
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8089
    ports:
      - "8089:8089"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 定时任务服务
  quartz:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-module/bjgy-cloud-quartz
    container_name: bjgy-quartz
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8090
    ports:
      - "8090:8090"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 消息服务
  message:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-module/bjgy-cloud-message
    container_name: bjgy-message
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8091
    ports:
      - "8091:8091"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8091/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务
  monitor:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
      args:
        SERVICE_NAME: bjgy-cloud-module/bjgy-cloud-monitor
    container_name: bjgy-monitor
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - nacos_host=nacos
      - nacos_port=8848
      - nacos_namespace=
      - SERVER_PORT=8092
    ports:
      - "8092:8092"
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - bjgy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8092/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  gateway_logs:
  system_logs:
  data_integrate_logs:
  data_development_logs:
  data_service_logs:
  data_governance_logs:
  data_assets_logs:
  data_bi_logs:
  quartz_logs:
  message_logs:
  monitor_logs:

networks:
  bjgy-network:
    external: true
