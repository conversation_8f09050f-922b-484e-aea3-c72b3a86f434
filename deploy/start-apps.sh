#!/bin/bash

# bjgy-cloud 应用程序启动脚本
# 独立启动所有应用程序服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查网络
check_network() {
    if ! docker network ls | grep -q "bjgy-network"; then
        log_info "创建Docker网络: bjgy-network"
        docker network create bjgy-network
    else
        log_info "Docker网络已存在: bjgy-network"
    fi
}

# 准备环境
prepare_environment() {
    local profile=${1:-"dev"}
    
    log_info "准备应用程序环境 (profile: $profile)..."
    
    # 创建日志目录
    mkdir -p logs/{gateway,system,data-integrate,data-development,data-service,data-governance,data-assets,data-bi,quartz,message,monitor}
    
    # 检查环境配置文件
    local env_file="env/.env.${profile}"
    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi
    
    log_info "使用环境配置: $env_file"
    log_success "环境准备完成"
}

# 构建镜像
build_images() {
    local version=${1:-"latest"}
    
    log_info "构建应用程序镜像 (版本: $version)..."
    
    if [ -f "build-images.sh" ]; then
        chmod +x build-images.sh
        ./build-images.sh "$version"
    else
        log_warning "build-images.sh不存在，跳过镜像构建"
    fi
}

# 启动应用程序
start_applications() {
    local profile=${1:-"dev"}
    
    log_info "启动应用程序服务 (profile: $profile)..."
    
    # 按顺序启动服务
    local services=(
        "gateway"
        "system"
        "data-integrate"
        "data-development"
        "data-service"
        "data-governance"
        "data-assets"
        "data-bi"
        "quartz"
        "message"
        "monitor"
    )
    
    for service in "${services[@]}"; do
        log_info "启动服务: $service"
        
        if command -v docker-compose &> /dev/null; then
            docker-compose --env-file "env/.env.${profile}" up -d "$service"
        else
            docker compose --env-file "env/.env.${profile}" up -d "$service"
        fi
        
        # 等待服务启动
        sleep 15
    done
    
    log_success "应用程序服务启动完成"
}

# 检查服务健康状态
check_health() {
    log_info "检查应用程序服务健康状态..."
    
    local services=(
        "gateway:8082:API网关"
        "system:8083:系统服务"
        "data-integrate:8084:数据集成服务"
        "data-development:8085:数据开发服务"
        "data-service:8086:数据服务"
        "data-governance:8087:数据治理服务"
        "data-assets:8088:数据资产服务"
        "data-bi:8089:数据BI服务"
        "quartz:8090:定时任务服务"
        "message:8091:消息服务"
        "monitor:8092:监控服务"
    )
    
    echo ""
    echo "应用程序健康检查结果:"
    echo "----------------------------------------"
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port desc <<< "$service_info"
        
        if curl -f "http://localhost:${port}/actuator/health" &> /dev/null; then
            echo -e "${GREEN}✓${NC} ${desc} (端口:${port}) - 健康"
        else
            echo -e "${RED}✗${NC} ${desc} (端口:${port}) - 不健康或未启动"
        fi
    done
    
    echo "----------------------------------------"
}

# 等待服务启动
wait_for_services() {
    log_info "等待应用程序服务启动..."
    
    local services=("gateway:8082" "system:8083")
    
    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        
        local max_attempts=60
        local attempt=1
        
        log_info "等待${service}服务启动..."
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f "http://localhost:${port}/actuator/health" &> /dev/null; then
                log_success "${service}服务启动成功"
                break
            fi
            
            if [ $((attempt % 10)) -eq 0 ]; then
                log_info "等待${service}服务启动... (${attempt}/${max_attempts})"
            fi
            sleep 3
            ((attempt++))
        done
        
        if [ $attempt -gt $max_attempts ]; then
            log_warning "${service}服务启动超时，但继续执行"
        fi
    done
    
    log_success "关键服务启动检查完成"
}

# 显示服务信息
show_services_info() {
    local profile=${1:-"dev"}
    
    echo ""
    log_success "应用程序服务启动完成！"
    echo ""
    echo "环境配置: $profile"
    echo ""
    echo "服务访问地址:"
    echo "  API网关:             http://localhost:8082"
    echo "  系统服务:            http://localhost:8083"
    echo "  数据集成服务:        http://localhost:8084"
    echo "  数据开发服务:        http://localhost:8085"
    echo "  数据服务:            http://localhost:8086"
    echo "  数据治理服务:        http://localhost:8087"
    echo "  数据资产服务:        http://localhost:8088"
    echo "  数据BI服务:          http://localhost:8089"
    echo "  定时任务服务:        http://localhost:8090"
    echo "  消息服务:            http://localhost:8091"
    echo "  监控服务:            http://localhost:8092"
    echo ""
    echo "健康检查端点:"
    echo "  所有服务:            http://localhost:808X/actuator/health"
    echo "  监控指标:            http://localhost:808X/actuator/metrics"
    echo ""
    echo "日志目录:"
    echo "  应用日志:            ./logs/[服务名]/"
    echo ""
    echo "常用命令:"
    echo "  查看服务状态:        docker-compose ps"
    echo "  查看服务日志:        docker-compose logs -f [服务名]"
    echo "  停止应用程序:        ./stop.sh"
    echo "  重启服务:            docker-compose restart [服务名]"
    echo ""
}

# 主函数
main() {
    local action=${1:-"start"}
    local profile=${2:-"dev"}
    local version=${3:-"latest"}
    
    case "$action" in
        "start")
            log_info "启动bjgy-cloud应用程序服务..."
            log_info "环境: $profile, 版本: $version"
            check_docker_compose
            check_network
            prepare_environment "$profile"
            build_images "$version"
            start_applications "$profile"
            wait_for_services
            check_health
            show_services_info "$profile"
            ;;
        "build")
            log_info "构建应用程序镜像..."
            build_images "$profile"  # 这里profile作为version使用
            ;;
        "status")
            check_health
            ;;
        "stop")
            log_info "停止应用程序服务..."
            if command -v docker-compose &> /dev/null; then
                docker-compose down
            else
                docker compose down
            fi
            log_success "应用程序服务已停止"
            ;;
        "restart")
            log_info "重启应用程序服务..."
            if command -v docker-compose &> /dev/null; then
                docker-compose --env-file "env/.env.${profile}" restart
            else
                docker compose --env-file "env/.env.${profile}" restart
            fi
            wait_for_services
            log_success "应用程序服务已重启"
            ;;
        *)
            echo "用法: $0 [ACTION] [PROFILE] [VERSION]"
            echo ""
            echo "操作:"
            echo "  start   - 启动应用程序服务 (默认)"
            echo "  build   - 仅构建镜像"
            echo "  status  - 检查服务状态"
            echo "  stop    - 停止应用程序服务"
            echo "  restart - 重启应用程序服务"
            echo ""
            echo "环境:"
            echo "  dev     - 开发环境 (默认)"
            echo "  test    - 测试环境"
            echo "  prod    - 生产环境"
            echo ""
            echo "示例:"
            echo "  $0                      # 启动应用程序，开发环境"
            echo "  $0 start dev latest     # 启动应用程序，开发环境，latest版本"
            echo "  $0 start test v2.0.0    # 启动应用程序，测试环境，v2.0.0版本"
            echo "  $0 build v2.0.0         # 构建v2.0.0版本镜像"
            echo "  $0 status               # 检查服务状态"
            exit 1
            ;;
    esac
}

# 参数处理
case "$1" in
    -h|--help)
        echo "bjgy-cloud 应用程序管理脚本"
        echo ""
        echo "用法: $0 [ACTION] [PROFILE] [VERSION]"
        echo ""
        echo "操作:"
        echo "  start   - 启动应用程序服务 (默认)"
        echo "  build   - 仅构建镜像"
        echo "  status  - 检查服务状态"
        echo "  stop    - 停止应用程序服务"
        echo "  restart - 重启应用程序服务"
        echo ""
        echo "环境配置:"
        echo "  dev     - 开发环境 (默认)"
        echo "  test    - 测试环境"
        echo "  prod    - 生产环境"
        echo ""
        echo "示例:"
        echo "  $0                      # 启动应用程序，开发环境"
        echo "  $0 start dev latest     # 启动应用程序，开发环境，latest版本"
        echo "  $0 start test v2.0.0    # 启动应用程序，测试环境，v2.0.0版本"
        echo "  $0 start prod v2.0.0    # 启动应用程序，生产环境，v2.0.0版本"
        echo "  $0 build v2.0.0         # 构建v2.0.0版本镜像"
        echo "  $0 status               # 检查服务状态"
        echo "  $0 stop                 # 停止应用程序"
        echo "  $0 restart dev          # 重启应用程序，开发环境"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
