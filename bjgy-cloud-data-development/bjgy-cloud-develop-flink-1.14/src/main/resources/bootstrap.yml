#数据开发
server:
  port: 8093

spring:
  mvc:
    servlet:
      load-on-startup: 1
  application:
    name: bjgy-cloud-develop-flink114
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_host:************}:${nacos_port:8848}
        # 命名空间，默认：public
        namespace: ${nacos_namespace:}
        service: ${spring.application.name}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        # 指定配置
        extension-configs:
          - data-id: datasource.yaml
            refresh: true
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 1024MB
# feign 配置
feign:
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
        loggerLevel: basic
  okhttp:
    enabled: true

logging:
  # 只有配置了日志文件，才能被监控收集
  file:
    name: logs/${spring.application.name}.log
#  endpoints config
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
