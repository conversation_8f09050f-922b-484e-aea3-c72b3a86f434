package com.bjgy.api;

import lombok.RequiredArgsConstructor;
import com.bjgy.api.module.data.development.DataProductionScheduleApi;
import com.bjgy.api.module.data.development.constant.ExecuteType;
import com.bjgy.api.module.data.development.dto.DataProductionScheduleDto;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.convert.DataProductionScheduleConvert;
import com.bjgy.entity.DataProductionScheduleRecordEntity;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataProductionScheduleRecordService;
import com.bjgy.service.DataProductionScheduleService;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DataAccessApiImpl
 */
@RestController
@RequiredArgsConstructor
public class DataProductionScheduleApiImpl implements DataProductionScheduleApi {

	private final DataProductionScheduleService scheduleService;
	private final DataProductionScheduleRecordService recordService;

	@Override
	public Result<DataProductionScheduleDto> getById(Long id) {
		return Result.ok(DataProductionScheduleConvert.INSTANCE.convertDto(scheduleService.getById(id)));
	}

	@Override
	public Result<String> scheduleRun(Long id) {
		return Result.ok(scheduleService.scheduleRun(id.intValue(), ExecuteType.SCHEDULE));
	}

	@Override
	public Result<Boolean> scheduleComplete(Integer recordId) {
		DataProductionScheduleRecordEntity record = recordService.getById(recordId);
		//记录被删除了，返回true
		if (record == null) {
			return Result.ok(true);
		}
		return Result.ok(CommonRunStatus.SUCCESS.getCode().equals(record.getRunStatus()) || CommonRunStatus.FAILED.getCode().equals(record.getRunStatus()));
	}
}
