package com.bjgy.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import com.bjgy.api.module.data.development.DataProductionTaskApi;
import com.bjgy.api.module.data.development.dto.DataProductionTaskDto;
import com.bjgy.convert.DataProductionTaskConvert;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataProductionTaskService;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DataAccessApiImpl
 */
@RestController
@RequiredArgsConstructor
public class DataProductionTaskApiImpl implements DataProductionTaskApi {

	private final DataProductionTaskService taskService;

	@Override
	public Result<DataProductionTaskDto> getByDbId(Long databaseId) {
		LambdaQueryWrapper<DataProductionTaskEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataProductionTaskEntity::getDatabaseId, databaseId).last(" limit 1");
		DataProductionTaskEntity one = taskService.getOne(wrapper);
		return Result.ok(DataProductionTaskConvert.INSTANCE.convertDto(one));
	}
}
