<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bjgy-cloud-data-development</artifactId>
        <groupId>com.bjgy</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bjgy-cloud-develop-base</artifactId>

    <properties>
        <flink.version>1.14.6</flink.version>
        <scala.binary.version>2.12</scala.binary.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>bjgy-cloud-api</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!--使用log42j-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>bjgy-cloud-mybatis</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>bjgy-cloud-dbswitch</artifactId>
            <version>2.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>calcite-core</artifactId>
                    <groupId>org.apache.calcite</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>calcite-server</artifactId>
                    <groupId>org.apache.calcite</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hadoop-common</artifactId>
                    <groupId>org.apache.hadoop</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hadoop-hdfs</artifactId>
                    <groupId>org.apache.hadoop</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-springdoc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-client-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-gateway-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-core-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-metadata-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-alert-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-daemon</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- hadoop -->
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-client-hadoop</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-math3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-process</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>2.6.11</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--使用1.14的依赖完成代码编译-->
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-core-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-math3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-executor-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-math3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-gateway-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-client-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-catalog-mysql-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <!--默认选flink1.14-->
        <dependency>
            <groupId>com.bjgy</groupId>
            <artifactId>flink-1.14</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>flink-sql-connector-oracle-cdc</artifactId>
                    <groupId>com.ververica</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>1.14.6</version>
        </dependency>
    </dependencies>

</project>
