<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bjgy.dao.DataProductionTaskStatementDao">

    <resultMap type="com.bjgy.entity.DataProductionTaskStatementEntity" id="dataProductionTaskStatementMap">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="projectId" column="project_id"/>
        <result property="statement" column="statement"/>
        <result property="version" column="version"/>
        <result property="deleted" column="deleted"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="updataByTaskId" parameterType="com.bjgy.entity.DataProductionTaskStatementEntity">
        UPDATE data_production_task_statement SET statement=#{statement} WHERE task_id=#{taskId}
    </update>

</mapper>
