<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bjgy.dao.DataProductionTaskInstanceDao">

    <resultMap type="com.bjgy.entity.DataProductionTaskInstanceEntity" id="dataProductionTaskInstanceMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="projectId" column="project_id"/>
        <result property="taskId" column="task_id"/>
        <result property="step" column="step"/>
        <result property="clusterId" column="cluster_id"/>
        <result property="jid" column="jid"/>
        <result property="executeSql" column="execute_sql"/>
        <result property="executeNo" column="execute_no"/>
        <result property="status" column="status"/>
        <result property="historyId" column="history_id"/>
        <result property="finishTime" column="finish_time"/>
        <result property="duration" column="duration"/>
        <result property="error" column="error"/>
        <result property="failedRestartCount" column="failed_restart_count"/>
        <result property="deleted" column="deleted"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="getJobInstanceByTaskId" resultType="com.bjgy.entity.DataProductionTaskInstanceEntity">
        select *
        from data_production_task_instance
        where task_id = #{id}
        order by create_time desc,id desc limit 1
    </select>
    <select id="listJobInstanceActive" resultType="com.bjgy.entity.DataProductionTaskInstanceEntity">
        select *
        from data_production_task_instance
        where status not in ('FAILED', 'CANCELED', 'FINISHED', 'UNKNOWN')
        order by id desc
    </select>

</mapper>
