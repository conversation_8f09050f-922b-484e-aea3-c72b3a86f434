<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bjgy.dao.DataProductionScheduleDao">

    <resultMap type="com.bjgy.entity.DataProductionScheduleEntity" id="dataProductionScheduleMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="name" column="name"/>
        <result property="ifCycle" column="if_cycle"/>
        <result property="cron" column="cron"/>
        <result property="note" column="note"/>
        <result property="status" column="status"/>
        <result property="releaseTime" column="release_time"/>
        <result property="releaseUserId" column="release_user_id"/>
        <result property="edges" column="edges"/>
        <result property="version" column="version"/>
        <result property="deleted" column="deleted"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="changeStutus" parameterType="com.bjgy.entity.DataProductionScheduleEntity">
        UPDATE data_production_schedule SET status=#{status},release_time=#{releaseTime},release_user_id=#{releaseUserId}
        WHERE id=#{id}
    </update>

</mapper>
