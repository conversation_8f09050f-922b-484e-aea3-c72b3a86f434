package com.bjgy.config;

import lombok.RequiredArgsConstructor;
import com.bjgy.service.DataProductionSysConfigService;
import org.springframework.stereotype.Component;

/**
 * @ClassName DataProductionSysConfigListener
 * <AUTHOR>
 * @Date 2022/12/27 9:32
 */
@Component
@RequiredArgsConstructor
public class RedisMessageListener {

	private final DataProductionSysConfigService sysConfigService;

	/**
	 * 接收订阅的消息
	 *
	 * @param message
	 */
	public void onMessage(String message) {
		if (message.contains(RedisMessageConfig.DATA_PRO_SYS_CONFIG_TOPIC)) {
			sysConfigService.initSysConfig();
		}
	}
}
