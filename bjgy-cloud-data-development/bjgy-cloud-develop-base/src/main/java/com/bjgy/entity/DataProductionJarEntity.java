package com.bjgy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.mybatis.entity.BaseEntity;

/**
 * 数据生产-jar管理
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_production_jar")
public class DataProductionJarEntity extends BaseEntity {

	/**
	* 项目（租户id）
	*/
	private Long projectId;

	private Long orgId;

	/**
	* 名称
	*/
	private String name;

	private Integer submitType;
	/**
	* 文件地址
	*/
	private String path;

	/**
	* 主类
	*/
	private String mainClass;

	/**
	* 参数
	*/
	private String params;

	/**
	* 备注
	*/
	private String note;







}
