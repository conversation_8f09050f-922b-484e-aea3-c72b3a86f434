package com.bjgy.dao;

import com.bjgy.entity.DataProductionCatalogueEntity;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.framework.mybatis.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

/**
* 数据生产任务
*
* @<NAME_EMAIL>
* @since 1.0.0 2022-12-05
*/
@Mapper
public interface DataProductionTaskDao extends BaseDao<DataProductionTaskEntity> {

	void updateInfoByCatalogue(DataProductionCatalogueEntity entity);
}
