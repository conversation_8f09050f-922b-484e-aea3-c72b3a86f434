package com.bjgy.dao;

import com.bjgy.entity.DataProductionTaskInstanceEntity;
import com.bjgy.framework.mybatis.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 数据生产任务实例
*
* @<NAME_EMAIL>
* @since 1.0.0 2022-12-20
*/
@Mapper
public interface DataProductionTaskInstanceDao extends BaseDao<DataProductionTaskInstanceEntity> {

	DataProductionTaskInstanceEntity getJobInstanceByTaskId(@Param("id") Integer id);

	List<DataProductionTaskInstanceEntity> listJobInstanceActive();
}
