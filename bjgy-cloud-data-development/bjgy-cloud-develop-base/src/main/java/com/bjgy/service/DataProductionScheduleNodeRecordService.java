package com.bjgy.service;

import com.bjgy.entity.DataProductionScheduleNodeRecordEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProductionScheduleNodeRecordVO;

import java.util.List;

/**
 * 数据生产-作业调度节点记录
 */
public interface DataProductionScheduleNodeRecordService extends BaseService<DataProductionScheduleNodeRecordEntity> {

    void save(DataProductionScheduleNodeRecordVO vo);

    void update(DataProductionScheduleNodeRecordVO vo);

    void delete(List<Long> idList);
}
