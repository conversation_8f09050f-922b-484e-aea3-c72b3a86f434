package com.bjgy.service;

import com.bjgy.api.module.data.development.constant.ExecuteType;
import com.bjgy.dto.Flow;
import com.bjgy.entity.DataProductionScheduleEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.model.ConsoleLog;
import com.bjgy.query.DataProductionScheduleQuery;
import com.bjgy.query.DataProductionScheduleRecordQuery;
import com.bjgy.vo.DataProductionScheduleNodeRecordVO;
import com.bjgy.vo.DataProductionScheduleRecordVO;
import com.bjgy.vo.DataProductionScheduleVO;

import java.util.List;

/**
 * 数据生产-作业调度
 */
public interface DataProductionScheduleService extends BaseService<DataProductionScheduleEntity> {

    PageResult<DataProductionScheduleVO> page(DataProductionScheduleQuery query);

    void save(Flow flow);

	Flow get(Long id);

	String scheduleRun(Integer id, ExecuteType executeType);

	void delete(List<Long> idList);

	String run(Integer id);

	ConsoleLog getLog(Integer recordId);

	List<DataProductionScheduleNodeRecordVO> listNodeRecord(Integer recordId);

	PageResult<DataProductionScheduleRecordVO> pageRecord(DataProductionScheduleRecordQuery query);

	void delRecord(List<Long> idList);

	DataProductionScheduleNodeRecordVO getNodeRecord(Integer nodeRecordId);

	void release(Integer id);

	void cancle(Integer id);

	void runActive();
}
