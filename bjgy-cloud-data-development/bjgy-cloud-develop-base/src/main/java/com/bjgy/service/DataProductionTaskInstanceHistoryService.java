package com.bjgy.service;

import com.bjgy.entity.DataProductionTaskInstanceHistoryEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionTaskInstanceHistoryQuery;
import com.bjgy.vo.DataProductionTaskInstanceHistoryVO;

import java.util.List;

/**
 * 数据生产任务实例历史
 */
public interface DataProductionTaskInstanceHistoryService extends BaseService<DataProductionTaskInstanceHistoryEntity> {

	PageResult<DataProductionTaskInstanceHistoryVO> page(DataProductionTaskInstanceHistoryQuery query);

	void save(DataProductionTaskInstanceHistoryVO vo);

	void update(DataProductionTaskInstanceHistoryVO vo);

	void delete(List<Long> idList);

	DataProductionTaskInstanceHistoryEntity getJobHistory(Integer id);

	DataProductionTaskInstanceHistoryEntity refreshJobHistory(Integer id, String jobManagerHost, String jid, boolean needSave);

	DataProductionTaskInstanceHistoryEntity getJobHistoryInfo(DataProductionTaskInstanceHistoryEntity jobHistoryJson);

	String getInstanceError(Integer historyId);

	DataProductionTaskInstanceHistoryVO getByHistoryId(Integer historyId);
}
