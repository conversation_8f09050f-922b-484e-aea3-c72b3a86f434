package com.bjgy.service;

import com.bjgy.entity.DataProductionTaskHistoryEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionTaskHistoryQuery;
import com.bjgy.vo.DataProductionTaskHistoryVO;

import java.util.List;

/**
 * 数据生产任务历史
 */
public interface DataProductionTaskHistoryService extends BaseService<DataProductionTaskHistoryEntity> {

    PageResult<DataProductionTaskHistoryVO> page(DataProductionTaskHistoryQuery query);

    void save(DataProductionTaskHistoryVO vo);

    void update(DataProductionTaskHistoryVO vo);

    void delete(List<Long> idList);

	void pullResult(DataProductionTaskHistoryEntity historyEntity);
}
