package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProductionJarConvert;
import com.bjgy.dao.DataProductionJarDao;
import com.bjgy.dao.DataProductionTaskDao;
import com.bjgy.entity.DataProductionJarEntity;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataProductionJarQuery;
import com.bjgy.service.DataProductionJarService;
import com.bjgy.vo.DataProductionJarVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据生产-jar管理
 */
@Service
@AllArgsConstructor
public class DataProductionJarServiceImpl extends BaseServiceImpl<DataProductionJarDao, DataProductionJarEntity> implements DataProductionJarService {

	private final DataProductionTaskDao dataProductionTaskDao;

	@Override
	public PageResult<DataProductionJarVO> page(DataProductionJarQuery query) {
		IPage<DataProductionJarEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataProductionJarConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataProductionJarEntity> getWrapper(DataProductionJarQuery query) {
		LambdaQueryWrapper<DataProductionJarEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataProductionJarEntity::getName, query.getName())
				.eq(query.getSubmitType() != null, DataProductionJarEntity::getSubmitType, query.getSubmitType())
				.orderByDesc(DataProductionJarEntity::getId);
		dataScopeWithOrgId(wrapper);

		return wrapper;
	}

	@Override
	public void save(DataProductionJarVO vo) {
		DataProductionJarEntity entity = DataProductionJarConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataProductionJarVO vo) {
		DataProductionJarEntity entity = DataProductionJarConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
		for (Long id : idList) {
			LambdaQueryWrapper<DataProductionTaskEntity> wrapper = Wrappers.lambdaQuery();
			wrapper.eq(DataProductionTaskEntity::getJarId, id).last("limit 1");
			DataProductionTaskEntity taskEntity = dataProductionTaskDao.selectOne(wrapper);
			if (taskEntity != null) {
				throw new ServerException(String.format("要删除的 Jar 包存在数据生产任务【%s】与之关联，不可删除！", taskEntity.getName()));
			}
		}
	}

	@Override
	public List<DataProductionJarVO> listAllByRunType(Integer jarRunType) {
		LambdaQueryWrapper<DataProductionJarEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(DataProductionJarEntity::getSubmitType, jarRunType);
		dataScopeWithOrgId(wrapper);
		return DataProductionJarConvert.INSTANCE.convertList(list(wrapper));
	}

}
