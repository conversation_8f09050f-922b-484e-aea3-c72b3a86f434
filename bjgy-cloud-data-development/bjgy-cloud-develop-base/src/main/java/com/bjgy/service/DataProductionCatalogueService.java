package com.bjgy.service;

import com.bjgy.entity.DataProductionCatalogueEntity;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProductionCatalogueVO;

import java.util.List;

/**
 * 数据生产目录
 */
public interface DataProductionCatalogueService extends BaseService<DataProductionCatalogueEntity> {

    void save(DataProductionCatalogueVO vo);

    void update(DataProductionCatalogueVO vo);

    void delete(Long id);

	List<TreeNodeVo> listTree();
}
