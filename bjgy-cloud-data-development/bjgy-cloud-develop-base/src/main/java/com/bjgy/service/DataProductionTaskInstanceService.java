package com.bjgy.service;

import com.bjgy.entity.DataProductionTaskInstanceEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionTaskInstanceQuery;
import com.bjgy.vo.DataProductionTaskInstanceVO;

import java.util.List;

/**
 * 数据生产任务实例
 */
public interface DataProductionTaskInstanceService extends BaseService<DataProductionTaskInstanceEntity> {

    PageResult<DataProductionTaskInstanceVO> page(DataProductionTaskInstanceQuery query);

    void save(DataProductionTaskInstanceVO vo);

    void update(DataProductionTaskInstanceVO vo);

    void delete(List<Long> idList);

	DataProductionTaskInstanceEntity getByIdWithoutTenant(Integer id);

	DataProductionTaskInstanceEntity getJobInstanceByTaskId(Integer id);

	List<DataProductionTaskInstanceEntity> listJobInstanceActive();
}
