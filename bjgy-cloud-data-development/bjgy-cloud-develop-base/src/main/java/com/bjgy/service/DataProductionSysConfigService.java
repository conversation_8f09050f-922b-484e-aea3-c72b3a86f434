package com.bjgy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.bjgy.entity.DataProductionSysConfigEntity;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProductionSysConfigVO;

import java.util.List;
import java.util.Map;

/**
 * 数据生产-配置中心
 */
public interface DataProductionSysConfigService extends BaseService<DataProductionSysConfigEntity> {

    void save(DataProductionSysConfigVO vo);

    void update(DataProductionSysConfigVO vo);

    void delete(List<Long> idList);

	Map<String, Object> getAll();

	Map<String, Object> getAll(Long projectId);

	void updateSysConfigByJson(JsonNode para);

	void initSysConfig();
}
