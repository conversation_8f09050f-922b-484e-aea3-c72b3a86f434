package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProductionTaskInstanceConvert;
import com.bjgy.dao.DataProductionTaskDao;
import com.bjgy.dao.DataProductionTaskInstanceDao;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.entity.DataProductionTaskInstanceEntity;
import com.bjgy.flink.gateway.GatewayType;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataProductionTaskInstanceQuery;
import com.bjgy.service.DataProductionTaskInstanceService;
import com.bjgy.vo.DataProductionTaskInstanceVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据生产任务实例
 */
@Service
@AllArgsConstructor
public class DataProductionTaskInstanceServiceImpl extends BaseServiceImpl<DataProductionTaskInstanceDao, DataProductionTaskInstanceEntity> implements DataProductionTaskInstanceService {

	private final DataProductionTaskDao taskDao;

    @Override
    public PageResult<DataProductionTaskInstanceVO> page(DataProductionTaskInstanceQuery query) {
        IPage<DataProductionTaskInstanceEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(DataProductionTaskInstanceConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<DataProductionTaskInstanceEntity> getWrapper(DataProductionTaskInstanceQuery query){
        LambdaQueryWrapper<DataProductionTaskInstanceEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(DataProductionTaskInstanceVO vo) {
        DataProductionTaskInstanceEntity entity = DataProductionTaskInstanceConvert.INSTANCE.convert(vo);
		baseMapper.insert(entity);
    }

    @Override
    public void update(DataProductionTaskInstanceVO vo) {
        DataProductionTaskInstanceEntity entity = DataProductionTaskInstanceConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

	@Override
	public DataProductionTaskInstanceEntity getByIdWithoutTenant(Integer id) {
		DataProductionTaskInstanceEntity instanceEntity = baseMapper.selectById(id);
		DataProductionTaskEntity taskEntity = taskDao.selectById(instanceEntity.getTaskId());
		instanceEntity.setTaskType(GatewayType.getByCode(taskEntity.getType().toString()).getLongValue());
		return instanceEntity;
	}

	@Override
	public DataProductionTaskInstanceEntity getJobInstanceByTaskId(Integer id) {
		return baseMapper.getJobInstanceByTaskId(id);
	}

	@Override
	public List<DataProductionTaskInstanceEntity> listJobInstanceActive() {
		return baseMapper.listJobInstanceActive();
	}

}
