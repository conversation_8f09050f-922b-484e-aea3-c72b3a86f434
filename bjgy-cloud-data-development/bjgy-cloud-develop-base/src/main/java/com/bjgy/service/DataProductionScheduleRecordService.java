package com.bjgy.service;

import com.bjgy.entity.DataProductionScheduleRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionScheduleRecordQuery;
import com.bjgy.vo.DataProductionScheduleRecordVO;

import java.util.List;

/**
 * 数据生产-作业调度记录
 */
public interface DataProductionScheduleRecordService extends BaseService<DataProductionScheduleRecordEntity> {

    PageResult<DataProductionScheduleRecordVO> page(DataProductionScheduleRecordQuery query);

    void save(DataProductionScheduleRecordVO vo);

    void update(DataProductionScheduleRecordVO vo);

    void delete(List<Long> idList);

}
