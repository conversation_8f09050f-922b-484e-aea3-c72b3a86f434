package com.bjgy.service;

import com.bjgy.entity.DataProductionClusterConfigurationEntity;
import com.bjgy.flink.gateway.result.TestResult;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionClusterConfigurationQuery;
import com.bjgy.vo.DataProductionClusterConfigurationVO;

import java.util.List;
import java.util.Map;

/**
 * 数据生产-集群配置
 */
public interface DataProductionClusterConfigurationService extends BaseService<DataProductionClusterConfigurationEntity> {

    PageResult<DataProductionClusterConfigurationVO> page(DataProductionClusterConfigurationQuery query);

    void save(DataProductionClusterConfigurationVO vo);

    void update(DataProductionClusterConfigurationVO vo);

    void delete(List<Long> idList);

	DataProductionClusterConfigurationEntity getClusterConfigById(Integer clusterConfigurationId);

	Map<String, Object> getGatewayConfig(Long id);

	TestResult testGateway(DataProductionClusterConfigurationVO clusterConfiguration);

	TestResult testGatewayCommon(DataProductionClusterConfigurationVO clusterConfiguration);

	List<DataProductionClusterConfigurationVO> listAll();
}
