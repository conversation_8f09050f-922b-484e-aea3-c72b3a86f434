package com.bjgy.service;

import com.bjgy.entity.DataProductionClusterEntity;
import com.bjgy.flink.core.cluster.FlinkClusterInfo;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionClusterQuery;
import com.bjgy.vo.DataProductionClusterVO;

import java.util.List;

/**
 * 数据生产-集群实例
 */
public interface DataProductionClusterService extends BaseService<DataProductionClusterEntity> {

	FlinkClusterInfo checkHeartBeat(String hosts, String host);

    PageResult<DataProductionClusterVO> page(DataProductionClusterQuery query);

    void save(DataProductionClusterVO vo);

    void update(DataProductionClusterVO vo);

    void delete(List<Long> idList);

	void heartbeat(List<Long> idList);

	List<DataProductionClusterEntity> listAll();

	String buildEnvironmentAddress(boolean useRemote, Integer clusterId);

	String buildRemoteEnvironmentAddress(Integer id);

	String getJobManagerAddress(DataProductionClusterEntity cluster);

	String buildLocalEnvironmentAddress();

	DataProductionClusterEntity registersCluster(DataProductionClusterEntity autoRegistersCluster);

	Integer clearCluster();

	List<DataProductionClusterEntity> listAuto();

	Integer buildJobManagePort(String address);
}
