package com.bjgy.service;

import com.bjgy.entity.DataProductionTaskSavepointsEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionTaskSavepointsQuery;
import com.bjgy.vo.DataProductionTaskSavepointsVO;

import java.util.List;

/**
 * 数据生产-作业保存点
 */
public interface DataProductionTaskSavepointsService extends BaseService<DataProductionTaskSavepointsEntity> {

    PageResult<DataProductionTaskSavepointsVO> page(DataProductionTaskSavepointsQuery query);

    void save(DataProductionTaskSavepointsVO vo);

    void update(DataProductionTaskSavepointsVO vo);

    void delete(List<Long> idList);

	DataProductionTaskSavepointsEntity getLatestSavepointByTaskId(Long id);

	DataProductionTaskSavepointsEntity getEarliestSavepointByTaskId(Long id);
}
