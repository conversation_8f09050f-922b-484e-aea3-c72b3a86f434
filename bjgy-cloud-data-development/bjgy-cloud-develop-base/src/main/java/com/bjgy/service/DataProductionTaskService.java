package com.bjgy.service;

import com.bjgy.dto.StudioExecuteDto;
import com.bjgy.entity.DataProductionCatalogueEntity;
import com.bjgy.entity.DataProductionScheduleNodeRecordEntity;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.entity.DataProductionTaskInstanceEntity;
import com.bjgy.flink.common.model.JobStatus;
import com.bjgy.flink.common.result.SqlExplainResult;
import com.bjgy.flink.core.job.JobResult;
import com.bjgy.flink.core.result.SelectResult;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.model.ConsoleLog;
import com.bjgy.model.JobInfoDetail;
import com.bjgy.query.DataProductionTaskHistoryQuery;
import com.bjgy.query.DataProductionTaskSavepointsQuery;
import com.bjgy.vo.DataProductionTaskHistoryVO;
import com.bjgy.vo.DataProductionTaskInstanceHistoryVO;
import com.bjgy.vo.DataProductionTaskSavepointsVO;
import com.bjgy.vo.DataProductionTaskVO;

import java.util.List;

/**
 * 数据生产任务
 */
public interface DataProductionTaskService extends BaseService<DataProductionTaskEntity> {

	DataProductionTaskVO save(DataProductionTaskVO vo);

	DataProductionTaskVO update(DataProductionTaskVO vo);

	void delete(Long id);

	DataProductionTaskVO get(Long id);

	List<SqlExplainResult> explainSql(StudioExecuteDto studioExecuteDto);

	JobResult executeSql(StudioExecuteDto studioExecuteDto);

	JobResult justExecuteSql(StudioExecuteDto studioExecuteDto);

	DataProductionTaskInstanceEntity refreshJobInstance(Integer id, boolean isCoercive);

	JobStatus checkJobStatus(JobInfoDetail jobInfoDetail);

	DataProductionTaskEntity getTaskInfoById(Integer id);

	void handleJobDone(DataProductionTaskInstanceEntity jobInstance);

	JobResult submitTaskCommon(StudioExecuteDto studioExecuteDto);

	JobResult submitTask(Long taskId);

	JobResult scheduleTaskCommon(DataProductionScheduleNodeRecordEntity nodeRecord);

	JobResult scheduleTask(DataProductionScheduleNodeRecordEntity nodeRecord);

	ConsoleLog getConsoleLog();

	void clearLog();

	void endConsoleLog();

	SelectResult getJobDataByJobId(String jobId);

	void updateInfoByCatalogue(DataProductionCatalogueEntity entity);

	void delHistory(List<Long> idList);

	PageResult<DataProductionTaskHistoryVO> pageHistory(DataProductionTaskHistoryQuery query);

	String getInstanceError(Integer historyId);

	DataProductionTaskInstanceHistoryVO getHistoryClusterInfo(Integer historyId);

	void clearLogWithOutKey();

	void savepoint(Integer historyId, String type);

	boolean savepointTask(Integer historyId, String savePointType);

	PageResult<DataProductionTaskSavepointsVO> pageSavepoint(DataProductionTaskSavepointsQuery query);

	void delSavepoint(List<Long> idList);

	List<DataProductionTaskVO> listEnv();

	boolean savepointJobInstance(Integer jobInstanceId, Integer historyId, String savePointType);

	JobResult executeFlinkSqlCommon(StudioExecuteDto studioExecuteDto);

	List<SqlExplainResult> explainFlinkSqlCommon(StudioExecuteDto studioExecuteDto);

}
