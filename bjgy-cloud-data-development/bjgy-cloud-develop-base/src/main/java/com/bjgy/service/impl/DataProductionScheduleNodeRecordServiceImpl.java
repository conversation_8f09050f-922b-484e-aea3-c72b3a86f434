package com.bjgy.service.impl;

import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProductionScheduleNodeRecordConvert;
import com.bjgy.dao.DataProductionScheduleNodeRecordDao;
import com.bjgy.entity.DataProductionScheduleNodeRecordEntity;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.service.DataProductionScheduleNodeRecordService;
import com.bjgy.vo.DataProductionScheduleNodeRecordVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据生产-作业调度节点记录
 */
@Service
@AllArgsConstructor
public class DataProductionScheduleNodeRecordServiceImpl extends BaseServiceImpl<DataProductionScheduleNodeRecordDao, DataProductionScheduleNodeRecordEntity> implements DataProductionScheduleNodeRecordService {


    @Override
    public void save(DataProductionScheduleNodeRecordVO vo) {
        DataProductionScheduleNodeRecordEntity entity = DataProductionScheduleNodeRecordConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(DataProductionScheduleNodeRecordVO vo) {
        DataProductionScheduleNodeRecordEntity entity = DataProductionScheduleNodeRecordConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}
