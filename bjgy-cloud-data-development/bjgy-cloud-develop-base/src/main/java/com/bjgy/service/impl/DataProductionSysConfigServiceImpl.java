package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import com.bjgy.api.module.data.integrate.DataProjectApi;
import com.bjgy.config.RedisMessageConfig;
import com.bjgy.convert.DataProductionSysConfigConvert;
import com.bjgy.dao.DataProductionSysConfigDao;
import com.bjgy.entity.DataProductionSysConfigEntity;
import com.bjgy.flink.common.assertion.Asserts;
import com.bjgy.flink.common.model.ProjectSystemConfiguration;
import com.bjgy.flink.common.model.SystemConfiguration;
import com.bjgy.framework.common.cache.RedisCache;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.service.DataProductionSysConfigService;
import com.bjgy.vo.DataProductionSysConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 数据生产-配置中心
 */
@Service
@AllArgsConstructor
public class DataProductionSysConfigServiceImpl extends BaseServiceImpl<DataProductionSysConfigDao, DataProductionSysConfigEntity> implements DataProductionSysConfigService {

	private final DataProjectApi dataProjectApi;
	private final RedisCache redisCache;

	@Override
	public void save(DataProductionSysConfigVO vo) {
		DataProductionSysConfigEntity entity = DataProductionSysConfigConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataProductionSysConfigVO vo) {
		DataProductionSysConfigEntity entity = DataProductionSysConfigConvert.INSTANCE.convert(vo);
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public Map<String, Object> getAll() {
		Map<String, Object> map = getByProjectId(getProjectId());
		SystemConfiguration systemConfiguration = ProjectSystemConfiguration.getByProjectId(getProjectId());
		systemConfiguration.addConfiguration(map);
		return map;
	}

	@Override
	public Map<String, Object> getAll(Long projectId) {
		Map<String, Object> map = getByProjectId(projectId);
		SystemConfiguration systemConfiguration = ProjectSystemConfiguration.getByProjectId(projectId);
		systemConfiguration.addConfiguration(map);
		return map;
	}

	private Map<String, Object> getByProjectId(Long projectId) {
		Map<String, Object> map = new HashMap<>();
		LambdaQueryWrapper<DataProductionSysConfigEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataProductionSysConfigEntity::getProjectId, projectId);
		List<DataProductionSysConfigEntity> sysConfigs = list(wrapper);
		for (DataProductionSysConfigEntity item : sysConfigs) {
			map.put(item.getName(), item.getValue());
		}
		return map;
	}

	@Override
	public void updateSysConfigByJson(JsonNode node) {
		Long projectId = getProjectId();
		if (node != null && node.isObject()) {
			Iterator<Map.Entry<String, JsonNode>> it = node.fields();
			while (it.hasNext()) {
				Map.Entry<String, JsonNode> entry = it.next();
				String name = entry.getKey();
				String value = entry.getValue().asText();
				DataProductionSysConfigEntity config = getOne(new QueryWrapper<DataProductionSysConfigEntity>().eq("name", name).eq("project_id", projectId));
				DataProductionSysConfigEntity newConfig = new DataProductionSysConfigEntity();
				newConfig.setValue(value);
				if (Asserts.isNull(config)) {
					newConfig.setName(name);
					newConfig.setProjectId(projectId);
					save(newConfig);
				} else {
					newConfig.setId(config.getId());
					newConfig.setProjectId(projectId);
					updateById(newConfig);
				}
			}
		}
		SystemConfiguration systemConfiguration = ProjectSystemConfiguration.getByProjectId(projectId);
		systemConfiguration.setConfiguration(node);
		//通知各节点更新
		redisCache.convertAndSend(RedisMessageConfig.DATA_PRO_SYS_CONFIG_TOPIC, RedisMessageConfig.DATA_PRO_SYS_CONFIG_TOPIC);
	}

	@Override
	public void initSysConfig() {
		//初始化每个项目（租户）的配置中心
		List<DataProjectCacheBean> projects = dataProjectApi.getProjectList().getData();
		for (DataProjectCacheBean project : projects) {
			ProjectSystemConfiguration.getByProjectId(project.getId()).setConfiguration(SingletonObject.OBJECT_MAPPER.valueToTree(getAll(project.getId())));
		}
	}

}
