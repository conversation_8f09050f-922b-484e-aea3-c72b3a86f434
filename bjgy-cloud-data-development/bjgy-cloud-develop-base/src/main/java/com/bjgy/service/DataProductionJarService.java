package com.bjgy.service;

import com.bjgy.entity.DataProductionJarEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProductionJarQuery;
import com.bjgy.vo.DataProductionJarVO;

import java.util.List;

/**
 * 数据生产-jar管理
 */
public interface DataProductionJarService extends BaseService<DataProductionJarEntity> {

    PageResult<DataProductionJarVO> page(DataProductionJarQuery query);

    void save(DataProductionJarVO vo);

    void update(DataProductionJarVO vo);

    void delete(List<Long> idList);

	List<DataProductionJarVO> listAllByRunType(Integer jarRunType);
}
