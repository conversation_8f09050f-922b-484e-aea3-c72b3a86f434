package com.bjgy.convert;

import com.bjgy.entity.DataProductionJarEntity;
import com.bjgy.vo.DataProductionJarVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-jar管理
*
* @<NAME_EMAIL>
* @since 1.0.0 2023-11-13
*/
@Mapper
public interface DataProductionJarConvert {
    DataProductionJarConvert INSTANCE = Mappers.getMapper(DataProductionJarConvert.class);

    DataProductionJarEntity convert(DataProductionJarVO vo);

    DataProductionJarVO convert(DataProductionJarEntity entity);

    List<DataProductionJarVO> convertList(List<DataProductionJarEntity> list);

}