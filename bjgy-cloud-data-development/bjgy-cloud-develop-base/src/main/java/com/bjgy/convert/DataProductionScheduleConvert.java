package com.bjgy.convert;

import com.bjgy.api.module.data.development.dto.DataProductionScheduleDto;
import com.bjgy.entity.DataProductionScheduleEntity;
import com.bjgy.vo.DataProductionScheduleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-作业调度
*/
@Mapper
public interface DataProductionScheduleConvert {
    DataProductionScheduleConvert INSTANCE = Mappers.getMapper(DataProductionScheduleConvert.class);

    DataProductionScheduleEntity convert(DataProductionScheduleVO vo);

	DataProductionScheduleDto convertDto(DataProductionScheduleEntity entity);

    DataProductionScheduleVO convert(DataProductionScheduleEntity entity);

    List<DataProductionScheduleVO> convertList(List<DataProductionScheduleEntity> list);

}
