package com.bjgy.convert;

import com.bjgy.entity.DataProductionClusterEntity;
import com.bjgy.vo.DataProductionClusterVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-集群实例
*
* @<NAME_EMAIL>
* @since 1.0.0 2022-12-01
*/
@Mapper
public interface DataProductionClusterConvert {
    DataProductionClusterConvert INSTANCE = Mappers.getMapper(DataProductionClusterConvert.class);

    DataProductionClusterEntity convert(DataProductionClusterVO vo);

    DataProductionClusterVO convert(DataProductionClusterEntity entity);

    List<DataProductionClusterVO> convertList(List<DataProductionClusterEntity> list);

}