package com.bjgy.convert;

import com.bjgy.entity.DataProductionCatalogueEntity;
import com.bjgy.vo.DataProductionCatalogueVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产目录
*
* @<NAME_EMAIL>
* @since 2.0.0 2022-11-27
*/
@Mapper
public interface DataProductionCatalogueConvert {
    DataProductionCatalogueConvert INSTANCE = Mappers.getMapper(DataProductionCatalogueConvert.class);

    DataProductionCatalogueEntity convert(DataProductionCatalogueVO vo);

    DataProductionCatalogueVO convert(DataProductionCatalogueEntity entity);

    List<DataProductionCatalogueVO> convertList(List<DataProductionCatalogueEntity> list);

}