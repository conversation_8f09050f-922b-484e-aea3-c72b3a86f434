package com.bjgy.convert;

import com.bjgy.api.module.data.development.dto.DataProductionTaskDto;
import com.bjgy.entity.DataProductionTaskEntity;
import com.bjgy.vo.DataProductionTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产任务
*/
@Mapper
public interface DataProductionTaskConvert {
    DataProductionTaskConvert INSTANCE = Mappers.getMapper(DataProductionTaskConvert.class);

    DataProductionTaskEntity convert(DataProductionTaskVO vo);

	DataProductionTaskDto convertDto(DataProductionTaskEntity entity);

    DataProductionTaskVO convert(DataProductionTaskEntity entity);

    List<DataProductionTaskVO> convertList(List<DataProductionTaskEntity> list);

}
