package com.bjgy.convert;

import com.bjgy.entity.DataProductionClusterConfigurationEntity;
import com.bjgy.vo.DataProductionClusterConfigurationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-集群配置
*
* @<NAME_EMAIL>
* @since 1.0.0 2022-12-20
*/
@Mapper
public interface DataProductionClusterConfigurationConvert {
    DataProductionClusterConfigurationConvert INSTANCE = Mappers.getMapper(DataProductionClusterConfigurationConvert.class);

    DataProductionClusterConfigurationEntity convert(DataProductionClusterConfigurationVO vo);

    DataProductionClusterConfigurationVO convert(DataProductionClusterConfigurationEntity entity);

    List<DataProductionClusterConfigurationVO> convertList(List<DataProductionClusterConfigurationEntity> list);

}