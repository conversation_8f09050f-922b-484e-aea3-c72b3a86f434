package com.bjgy.convert;

import com.bjgy.entity.DataProductionTaskSavepointsEntity;
import com.bjgy.vo.DataProductionTaskSavepointsVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-作业保存点
*
* @<NAME_EMAIL>
* @since 1.0.0 2023-01-08
*/
@Mapper
public interface DataProductionTaskSavepointsConvert {
    DataProductionTaskSavepointsConvert INSTANCE = Mappers.getMapper(DataProductionTaskSavepointsConvert.class);

    DataProductionTaskSavepointsEntity convert(DataProductionTaskSavepointsVO vo);

    DataProductionTaskSavepointsVO convert(DataProductionTaskSavepointsEntity entity);

    List<DataProductionTaskSavepointsVO> convertList(List<DataProductionTaskSavepointsEntity> list);

}