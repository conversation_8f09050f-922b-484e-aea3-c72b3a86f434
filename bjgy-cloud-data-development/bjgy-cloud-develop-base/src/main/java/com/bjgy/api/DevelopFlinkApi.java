package com.bjgy.api;

import com.bjgy.dto.SavepointDto;
import com.bjgy.dto.StudioExecuteDto;
import com.bjgy.entity.DataProductionScheduleNodeRecordEntity;
import com.bjgy.flink.common.result.SqlExplainResult;
import com.bjgy.flink.core.job.JobResult;
import com.bjgy.flink.gateway.result.TestResult;
import com.bjgy.vo.DataProductionClusterConfigurationVO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName DevelopFlinkApi
 * <AUTHOR>
 * @Date 2023/12/5 16:55
 */
public interface DevelopFlinkApi {

	@PostMapping(value = "/api/develop/test-gateway")
	TestResult testGateway(@RequestBody DataProductionClusterConfigurationVO vo);

	@PostMapping(value = "/api/develop/explain-flink-sql")
	List<SqlExplainResult> explainFlinkSql(@RequestBody StudioExecuteDto studioExecuteDto);

	@PostMapping(value = "/api/develop/execute-flink-sql")
	JobResult executeFlinkSql(@RequestBody StudioExecuteDto studioExecuteDto);

	@PostMapping(value = "/api/develop/submit-task")
	JobResult submitTask(@RequestBody StudioExecuteDto studioExecuteDto);

	@PostMapping(value = "/api/develop/schedule-task")
	JobResult scheduleTask(@RequestBody DataProductionScheduleNodeRecordEntity nodeRecord);

	@PostMapping(value = "/api/develop/savepoint")
	Boolean savepointJobInstance(@RequestBody SavepointDto savepointDto);
}
