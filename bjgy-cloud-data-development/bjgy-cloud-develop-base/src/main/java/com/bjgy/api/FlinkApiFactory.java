package com.bjgy.api;

import com.bjgy.api.flink114.Flink114Api;
import com.bjgy.api.flink116.Flink116Api;
import com.bjgy.flink.common.context.SpringContextUtils;
import com.bjgy.framework.common.exception.ServerException;

/**
 * @ClassName FlinkApiFactory
 */
public class FlinkApiFactory {
	public static DevelopFlinkApi getByVersion(FlinkVersion flinkVersion) {
		if (FlinkVersion.FLINK114.equals(flinkVersion)) {
			return SpringContextUtils.getBeanByClass(Flink114Api.class);
		} else if (FlinkVersion.FLINK116.equals(flinkVersion)) {
			return SpringContextUtils.getBeanByClass(Flink116Api.class);
		}
		throw new ServerException("Unsupported Flink Version");
	}
}
