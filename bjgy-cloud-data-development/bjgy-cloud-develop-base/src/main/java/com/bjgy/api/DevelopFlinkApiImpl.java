package com.bjgy.api;

import lombok.RequiredArgsConstructor;
import com.bjgy.dto.SavepointDto;
import com.bjgy.dto.StudioExecuteDto;
import com.bjgy.entity.DataProductionScheduleNodeRecordEntity;
import com.bjgy.flink.common.result.SqlExplainResult;
import com.bjgy.flink.core.job.JobResult;
import com.bjgy.flink.gateway.result.TestResult;
import com.bjgy.service.DataProductionClusterConfigurationService;
import com.bjgy.service.DataProductionTaskService;
import com.bjgy.vo.DataProductionClusterConfigurationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName DataAccessApiImpl
 * <AUTHOR>
 * @Date 2022/10/26 11:50
 */
public class DevelopFlinkApiImpl implements DevelopFlinkApi {

	@Autowired
	private DataProductionClusterConfigurationService clusterConfigurationService;
	@Autowired
	private DataProductionTaskService taskService;

	@Override
	public TestResult testGateway(DataProductionClusterConfigurationVO clusterConfiguration) {
		return clusterConfigurationService.testGatewayCommon(clusterConfiguration);
	}

	@Override
	public List<SqlExplainResult> explainFlinkSql(StudioExecuteDto studioExecuteDto) {
		return taskService.explainFlinkSqlCommon(studioExecuteDto);
	}

	@Override
	public JobResult executeFlinkSql(StudioExecuteDto studioExecuteDto) {
		return taskService.executeFlinkSqlCommon(studioExecuteDto);
	}

	@Override
	public JobResult submitTask(StudioExecuteDto studioExecuteDto) {
		return taskService.submitTaskCommon(studioExecuteDto);
	}

	@Override
	public JobResult scheduleTask(DataProductionScheduleNodeRecordEntity nodeRecord) {
		return taskService.scheduleTaskCommon(nodeRecord);
	}

	@Override
	public Boolean savepointJobInstance(SavepointDto savepointDto) {
		return taskService.savepointJobInstance(savepointDto.getInstanceId(), savepointDto.getHistoryId(), savepointDto.getSavePointType());
	}
}
