package com.bjgy.api.flink114;

import com.bjgy.api.DevelopFlinkApi;
import com.bjgy.api.ServerNames;
import com.bjgy.flink.gateway.result.TestResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName DataProductionTaskApi
 */
@FeignClient(name = ServerNames.DEVELOP_FLINK_114)
public interface Flink114Api extends DevelopFlinkApi {

}
