package com.bjgy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.common.utils.DateUtils;
import com.bjgy.framework.common.utils.TreeNode;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据开发-工具类别
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "数据开发-工具类别")
public class DataProdToolsCategoryVO extends TreeNode<DataProdToolsCategoryVO> implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键id")
	private Long id;
	private Integer type;
	private String path;
	private Integer builtIn;
	private String icon;

	@Schema(description = "名称")
	private String name;

	@Schema(description = "父级ID")
	private Long pid;

	@Schema(description = "顺序")
	private Integer orderNo;

	private Long projectId;
	private Long orgId;

	@Schema(description = "版本号")
	private Integer version;

	@Schema(description = "删除标识  0：正常   1：已删除")
	private Integer deleted;

	@Schema(description = "创建者")
	private Long creator;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新者")
	private Long updater;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}
