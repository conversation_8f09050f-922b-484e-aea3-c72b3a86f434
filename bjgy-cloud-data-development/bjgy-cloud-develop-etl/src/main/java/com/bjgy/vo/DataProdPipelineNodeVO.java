package com.bjgy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.bjgy.framework.common.utils.DateUtils;

import java.io.Serializable;
import java.util.Date;

/**
* 数据生产-生产流程节点
*/
@Data
@Schema(description = "数据生产-生产流程节点")
public class DataProdPipelineNodeVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键id")
	private Long id;

	@Schema(description = "关联的流程id")
	private Long pipelineId;

	@Schema(description = "机构id")
	private Long orgId;

	@Schema(description = "项目（租户）id")
	private Long projectId;

	@Schema(description = "节点编号")
	private String no;
	@Schema(description = "依赖节点编号")
	private String dependNo;

	@Schema(description = "执行顺序")
	private Integer sort;

	@Schema(description = "节点名称")
	private String name;
	private String icon;

	@Schema(description = "节点类型")
	private String type;
	private Integer toolType;

	@Schema(description = "节点json数据")
	private String nodeJson;

	@Schema(description = "横坐标")
	private Integer x;

	@Schema(description = "纵坐标")
	private Integer y;

	@Schema(description = "节点描述")
	private String note;

	@Schema(description = "关联的工具目录id")
	private Long toolCategoryId;

	@Schema(description = "遇错是否继续（0-否 1-是）")
	private Integer failGoOn;

	@Schema(description = "节点权重")
	private Integer weight;

	@Schema(description = "超时时长")
	private Integer overTimes;

	@Schema(description = "版本号")
	private Integer version;

	@Schema(description = "删除标识  0：正常   1：已删除")
	private Integer deleted;

	@Schema(description = "创建者")
	private Long creator;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新者")
	private Long updater;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}
