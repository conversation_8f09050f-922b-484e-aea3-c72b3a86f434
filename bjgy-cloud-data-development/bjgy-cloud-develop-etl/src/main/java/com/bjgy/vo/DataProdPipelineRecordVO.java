package com.bjgy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.bjgy.framework.common.utils.DateUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据生产-etl流程记录
 */
@Data
@Schema(description = "数据生产-etl流程记录")
public class DataProdPipelineRecordVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键id")
	private Long id;

	@Schema(description = "调度名称")
	private String name;

	@Schema(description = "流程id")
	private Long pipelineId;

	@Schema(description = "机构id")
	private Long orgId;

	@Schema(description = "项目（租户）id")
	private Long projectId;

	@Schema(description = "当前状态 字典 run_status")
	private Integer runStatus;

	@Schema(description = "开始时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date startTime;

	@Schema(description = "结束时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date endTime;

	@Schema(description = "运行日志")
	private String log;

	@Schema(description = "1-手动 2-调度")
	private Integer executeType;

	@Schema(description = "历史配置json")
	private String configJson;

	@Schema(description = "版本号")
	private Integer version;

	@Schema(description = "删除标识  0：正常   1：已删除")
	private Integer deleted;

	@Schema(description = "创建者")
	private Long creator;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新者")
	private Long updater;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}
