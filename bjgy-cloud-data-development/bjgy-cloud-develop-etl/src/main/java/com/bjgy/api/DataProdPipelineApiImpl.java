package com.bjgy.api;

import lombok.RequiredArgsConstructor;
import com.bjgy.api.module.data.development.DataProdPipelineApi;
import com.bjgy.api.module.data.development.constant.ExecuteType;
import com.bjgy.api.module.data.development.dto.DataProdPipelineDto;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.convert.DataProdPipelineConvert;
import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.service.DataProdPipelineRecordService;
import com.bjgy.service.DataProdPipelineService;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DataAccessApiImpl
 */
@RestController
@RequiredArgsConstructor
public class DataProdPipelineApiImpl implements DataProdPipelineApi {

	private final DataProdPipelineService pipelineService;
	private final DataProdPipelineRecordService pipelineRecordService;

	@Override
	public DataProdPipelineDto getById(Long id) {
		return DataProdPipelineConvert.INSTANCE.convertDto(pipelineService.getById(id));
	}

	@Override
	public Long scheduleRun(Long id) {
		return pipelineService.scheduleRun(id, ExecuteType.SCHEDULE);
	}

	@Override
	public Boolean scheduleComplete(Long recordId) {
		DataProdPipelineRecordEntity record = pipelineRecordService.getById(recordId);
		if (record == null) {
			return true;
		}
		return CommonRunStatus.SUCCESS.getCode().equals(record.getRunStatus()) || CommonRunStatus.FAILED.getCode().equals(record.getRunStatus());
	}
}
