package com.bjgy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName FlowNodeDto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PipelineNode {
	private String id;
	private String type;
	private Integer x;
	private Integer y;
	private PipelineNodeProperties properties;

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		PipelineNode flowNode = (PipelineNode) o;
		return Objects.equals(id, flowNode.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id);
	}
}
