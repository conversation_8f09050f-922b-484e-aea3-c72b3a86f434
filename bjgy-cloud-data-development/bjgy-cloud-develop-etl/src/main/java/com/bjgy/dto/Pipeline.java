package com.bjgy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ScheduleFlowDto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Pipeline {
	private Long id;
	private Long orgId;
	private Long recordId;
	//任务类型 2-一次性 3-周期性
	private Integer taskType;
	private String name;
	private String cron;
	private String description;
	private Integer status;
	private Date releaseTime;
	private Integer releaseUserId;
	private List<PipelineNode> nodes;
	private List<PipelineEdge> edges;
}
