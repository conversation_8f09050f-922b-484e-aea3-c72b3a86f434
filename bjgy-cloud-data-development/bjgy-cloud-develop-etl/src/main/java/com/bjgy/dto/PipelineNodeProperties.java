package com.bjgy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName FlowNodeProperties
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PipelineNodeProperties {
	public static final Map<String, Object> SUCCESS_STYLE = new HashMap<String, Object>() {
		{
			put("border", "3px solid #06c733");
		}
	};
	public static final Map<String, Object> FALIE_STYLE = new HashMap<String, Object>() {
		{
			put("border", "3px solid #e30000");
		}
	};
	/**
	 * 库里的node的Id
	 */
	private Long id;
	//流程记录id（运行时用）
	private Long recordId;
	//节点记录id（运行时用）
	private Long nodeRecordId;
	//节点是否是结束节点（运行时用）
	private boolean ifEnd;
	private String icon;
	private String name;
	private Long toolCategoryId;
	private String toolCategoryName;
	private Integer weight;
	private Integer toolType;
	private Map<String, Object> nodeJson;
	private String note;
	private Integer failGoOn;
	private Integer overTimes;
	private Map<String, Object> style;
	//CommonRunStatus
	private Integer runStatus = 1;
}
