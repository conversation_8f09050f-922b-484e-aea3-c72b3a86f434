package com.bjgy.convert;

import com.bjgy.api.module.data.development.dto.DataProdPipelineDto;
import com.bjgy.entity.DataProdPipelineEntity;
import com.bjgy.vo.DataProdPipelineVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据开发-etl流程
*/
@Mapper
public interface DataProdPipelineConvert {
    DataProdPipelineConvert INSTANCE = Mappers.getMapper(DataProdPipelineConvert.class);

    DataProdPipelineEntity convert(DataProdPipelineVO vo);

    DataProdPipelineVO convert(DataProdPipelineEntity entity);

	DataProdPipelineDto convertDto(DataProdPipelineEntity entity);

    List<DataProdPipelineVO> convertList(List<DataProdPipelineEntity> list);

}
