package com.bjgy.convert;

import com.bjgy.entity.DataProdPipelineNodeEntity;
import com.bjgy.vo.DataProdPipelineNodeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-生产流程节点
*/
@Mapper
public interface DataProdPipelineNodeConvert {
    DataProdPipelineNodeConvert INSTANCE = Mappers.getMapper(DataProdPipelineNodeConvert.class);

    DataProdPipelineNodeEntity convert(DataProdPipelineNodeVO vo);

    DataProdPipelineNodeVO convert(DataProdPipelineNodeEntity entity);

    List<DataProdPipelineNodeVO> convertList(List<DataProdPipelineNodeEntity> list);

}