package com.bjgy.convert;

import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-etl节点记录
*/
@Mapper
public interface DataProdPipelineNodeRecordConvert {
    DataProdPipelineNodeRecordConvert INSTANCE = Mappers.getMapper(DataProdPipelineNodeRecordConvert.class);

    DataProdPipelineNodeRecordEntity convert(DataProdPipelineNodeRecordVO vo);

    DataProdPipelineNodeRecordVO convert(DataProdPipelineNodeRecordEntity entity);

    List<DataProdPipelineNodeRecordVO> convertList(List<DataProdPipelineNodeRecordEntity> list);

}