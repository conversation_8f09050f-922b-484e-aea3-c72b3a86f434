package com.bjgy.convert;

import com.bjgy.entity.DataProdToolsCategoryEntity;
import com.bjgy.vo.DataProdToolsCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据开发-工具类别
*/
@Mapper
public interface DataProdToolsCategoryConvert {
    DataProdToolsCategoryConvert INSTANCE = Mappers.getMapper(DataProdToolsCategoryConvert.class);

    DataProdToolsCategoryEntity convert(DataProdToolsCategoryVO vo);

    DataProdToolsCategoryVO convert(DataProdToolsCategoryEntity entity);

    List<DataProdToolsCategoryVO> convertList(List<DataProdToolsCategoryEntity> list);

}