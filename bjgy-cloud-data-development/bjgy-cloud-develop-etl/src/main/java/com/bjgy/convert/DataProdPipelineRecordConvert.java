package com.bjgy.convert;

import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.vo.DataProdPipelineRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 数据生产-etl流程记录
*/
@Mapper
public interface DataProdPipelineRecordConvert {
    DataProdPipelineRecordConvert INSTANCE = Mappers.getMapper(DataProdPipelineRecordConvert.class);

    DataProdPipelineRecordEntity convert(DataProdPipelineRecordVO vo);

    DataProdPipelineRecordVO convert(DataProdPipelineRecordEntity entity);

    List<DataProdPipelineRecordVO> convertList(List<DataProdPipelineRecordEntity> list);

}