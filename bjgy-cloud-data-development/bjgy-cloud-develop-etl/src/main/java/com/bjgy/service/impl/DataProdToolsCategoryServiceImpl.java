package com.bjgy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdToolsCategoryConvert;
import com.bjgy.dao.DataProdToolsCategoryDao;
import com.bjgy.entity.DataProdToolsCategoryEntity;
import com.bjgy.framework.common.constant.Constant;
import com.bjgy.framework.common.utils.TreeUtils;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.framework.security.user.SecurityUser;
import com.bjgy.framework.security.user.UserDetail;
import com.bjgy.service.DataProdToolsCategoryService;
import com.bjgy.vo.DataProdToolsCategoryVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据开发-工具类别
 */
@Service
@AllArgsConstructor
public class DataProdToolsCategoryServiceImpl extends BaseServiceImpl<DataProdToolsCategoryDao, DataProdToolsCategoryEntity> implements DataProdToolsCategoryService {


	@Override
	public void save(DataProdToolsCategoryVO vo) {
		DataProdToolsCategoryEntity entity = DataProdToolsCategoryConvert.INSTANCE.convert(vo);
		entity.setBuiltIn(0);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
		baseMapper.insert(entity);
	}

	@Override
	public void update(DataProdToolsCategoryVO vo) {
		DataProdToolsCategoryEntity entity = DataProdToolsCategoryConvert.INSTANCE.convert(vo);
		entity.setBuiltIn(0);
		entity.setPath(recursionPath(entity, null));
		entity.setProjectId(getProjectId());
		updateById(entity);
	}

	private String recursionPath(DataProdToolsCategoryEntity categoryEntity, String path) {
		if (StringUtil.isBlank(path)) {
			path = categoryEntity.getName();
		}
		if (categoryEntity.getPid() != 0) {
			DataProdToolsCategoryEntity parent = getById(categoryEntity.getPid());
			path = parent.getName() + "/" + path;
			return recursionPath(parent, path);
		}
		return path;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
	}

	@Override
	public List<DataProdToolsCategoryVO> listTree(Integer folder) {
		LambdaQueryWrapper<DataProdToolsCategoryEntity> wrapper = new LambdaQueryWrapper<>();
		UserDetail user = SecurityUser.getUser();
		if (folder != null) {
			wrapper.eq(DataProdToolsCategoryEntity::getType, -1);
		}
		wrapper.eq(DataProdToolsCategoryEntity::getProjectId, getProjectId()).or().eq(DataProdToolsCategoryEntity::getProjectId, -1)
				.orderByAsc(DataProdToolsCategoryEntity::getOrderNo);
		List<Long> dataScopeList = user.getDataScopeList();
		List<Long> orgList;
		if (dataScopeList != null) {
			orgList = new ArrayList<>(dataScopeList);
		} else {
			orgList = new ArrayList<>();
		}
		orgList.add(-1L);
		StringBuilder sqlFilter = new StringBuilder();
		if (!user.getSuperAdmin().equals(Constant.SUPER_ADMIN)) {
			// 机构数据过滤，如果角色分配了机构的数据权限，则过滤，仅适用于有机构id的表
			sqlFilter.append(" org_id");
			sqlFilter.append(" IN( ").append(StrUtil.join(",", orgList)).append(" ) ");
			wrapper.apply(sqlFilter.toString());
		}
		List<DataProdToolsCategoryEntity> categoryEntities = baseMapper.selectList(wrapper);
		return TreeUtils.build(DataProdToolsCategoryConvert.INSTANCE.convertList(categoryEntities), Constant.ROOT);
	}

}
