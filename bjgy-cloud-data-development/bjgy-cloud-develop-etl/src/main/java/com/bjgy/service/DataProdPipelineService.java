package com.bjgy.service;

import com.bjgy.api.module.data.development.constant.ExecuteType;
import com.bjgy.dto.ConsoleLog;
import com.bjgy.dto.Pipeline;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import com.bjgy.vo.DataProdPipelineVO;
import com.bjgy.query.DataProdPipelineQuery;
import com.bjgy.entity.DataProdPipelineEntity;

import java.util.List;

/**
 * 数据开发-etl流程
 */
public interface DataProdPipelineService extends BaseService<DataProdPipelineEntity> {

    PageResult<DataProdPipelineVO> page(DataProdPipelineQuery query);

    Long save(Pipeline pipeline);

    void update(DataProdPipelineVO vo);

    void delete(List<Long> idList);

	Pipeline get(Long id);

	Long run(Long id);

	Long scheduleRun(Long id, ExecuteType executeType);

	ConsoleLog getLog(Integer recordId);

	List<DataProdPipelineNodeRecordVO> listNodeRecord(Integer recordId);

	void release(Long id);

	void cancle(Long id);

	void stop(Long recordId);
}
