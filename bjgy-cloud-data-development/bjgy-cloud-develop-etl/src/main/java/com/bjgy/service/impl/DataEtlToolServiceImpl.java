package com.bjgy.service.impl;

import lombok.extern.slf4j.Slf4j;
import com.bjgy.service.DataEtlToolService;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.KafkaFuture;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 数据开发-ETL工具服务实现
 */
@Slf4j
@Service
public class DataEtlToolServiceImpl implements DataEtlToolService {

    @Override
    public String testKafkaConnection(Map<String, Object> params) {
        AdminClient adminClient = null;
        try {
            // 创建Kafka管理客户端
            adminClient = createAdminClient(params);

            // 尝试获取集群信息来测试连接
            ListTopicsResult listTopicsResult = adminClient.listTopics();
            KafkaFuture<Set<String>> future = listTopicsResult.names();

            // 设置超时时间为10秒
            Set<String> topics = future.get(10, TimeUnit.SECONDS);

            log.info("Kafka连接测试成功，发现 {} 个主题", topics.size());
            return "连接成功！发现 " + topics.size() + " 个主题";

        } catch (Exception e) {
            log.error("Kafka连接测试失败", e);
            String errorMsg = e.getMessage();
            if (errorMsg.contains("Connection refused")) {
                throw new RuntimeException("连接被拒绝，请检查Kafka服务器地址和端口");
            } else if (errorMsg.contains("Authentication")) {
                throw new RuntimeException("认证失败，请检查用户名和密码");
            } else if (errorMsg.contains("timeout")) {
                throw new RuntimeException("连接超时，请检查网络连接和服务器状态");
            } else {
                throw new RuntimeException("连接失败：" + errorMsg);
            }
        } finally {
            if (adminClient != null) {
                try {
                    adminClient.close();
                } catch (Exception e) {
                    log.warn("关闭Kafka管理客户端失败", e);
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> listKafkaTopics(Map<String, Object> params) {
        AdminClient adminClient = null;
        try {
            // 创建Kafka管理客户端
            adminClient = createAdminClient(params);

            // 获取主题列表
            ListTopicsResult listTopicsResult = adminClient.listTopics();
            Set<String> topicNames = listTopicsResult.names().get(10, TimeUnit.SECONDS);

            // 获取主题详细信息
            DescribeTopicsResult describeTopicsResult = adminClient.describeTopics(topicNames);
            Map<String, TopicDescription> topicDescriptions = describeTopicsResult.all().get(10, TimeUnit.SECONDS);

            // 构建返回结果
            List<Map<String, Object>> topics = new ArrayList<>();
            for (Map.Entry<String, TopicDescription> entry : topicDescriptions.entrySet()) {
                Map<String, Object> topicInfo = new HashMap<>();
                topicInfo.put("name", entry.getKey());
                topicInfo.put("partitions", entry.getValue().partitions().size());
                topics.add(topicInfo);
            }

            // 按主题名称排序
            topics.sort(Comparator.comparing(a -> String.valueOf(a.get("name"))));

            log.info("获取Kafka主题列表成功，共 {} 个主题", topics.size());
            return topics;

        } catch (Exception e) {
            log.error("获取Kafka主题列表失败", e);
            String errorMsg = e.getMessage();
            if (errorMsg.contains("Connection refused")) {
                throw new RuntimeException("连接被拒绝，请检查Kafka服务器地址和端口");
            } else if (errorMsg.contains("Authentication")) {
                throw new RuntimeException("认证失败，请检查用户名和密码");
            } else if (errorMsg.contains("timeout")) {
                throw new RuntimeException("连接超时，请检查网络连接和服务器状态");
            } else {
                throw new RuntimeException("获取主题列表失败：" + errorMsg);
            }
        } finally {
            if (adminClient != null) {
                try {
                    adminClient.close();
                } catch (Exception e) {
                    log.warn("关闭Kafka管理客户端失败", e);
                }
            }
        }
    }

    /**
     * 创建Kafka管理客户端
     */
    private AdminClient createAdminClient(Map<String, Object> params) {
        Properties props = new Properties();

        // 基础配置
        String bootstrapServers = (String) params.get("bootstrapServers");
        if (StringUtils.isBlank(bootstrapServers)) {
            throw new RuntimeException("Bootstrap服务器地址不能为空");
        }
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);

        // 安全配置
        String securityProtocol = (String) params.get("securityProtocol");
        if (StringUtils.isNotBlank(securityProtocol) && !"PLAINTEXT".equals(securityProtocol)) {
            props.put("security.protocol", securityProtocol);

            if (securityProtocol.contains("SASL")) {
                String saslMechanism = (String) params.get("saslMechanism");
                if (StringUtils.isNotBlank(saslMechanism)) {
                    props.put("sasl.mechanism", saslMechanism);
                }

                String saslUsername = (String) params.get("saslUsername");
                String saslPassword = (String) params.get("saslPassword");
                if (StringUtils.isNotBlank(saslUsername) && StringUtils.isNotBlank(saslPassword)) {
                    String jaasConfig = String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                            saslUsername, saslPassword);
                    props.put("sasl.jaas.config", jaasConfig);
                }
            }
        }

        // 设置超时时间
        props.put("request.timeout.ms", "10000");
        props.put("connections.max.idle.ms", "10000");

        return AdminClient.create(props);
    }
}
