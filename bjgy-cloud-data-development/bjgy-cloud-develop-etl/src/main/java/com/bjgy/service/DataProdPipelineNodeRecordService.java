package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import com.bjgy.query.DataProdPipelineNodeRecordQuery;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;

import java.util.List;

/**
 * 数据生产-etl节点记录
 */
public interface DataProdPipelineNodeRecordService extends BaseService<DataProdPipelineNodeRecordEntity> {

    PageResult<DataProdPipelineNodeRecordVO> page(DataProdPipelineNodeRecordQuery query);

    void save(DataProdPipelineNodeRecordVO vo);

    void update(DataProdPipelineNodeRecordVO vo);

    void delete(List<Long> idList);
}
