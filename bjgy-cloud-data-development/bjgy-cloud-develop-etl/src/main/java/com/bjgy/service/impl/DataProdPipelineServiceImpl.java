package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import com.bjgy.api.module.data.development.constant.ExecuteType;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.api.module.quartz.QuartzDataProdPipelineApi;
import com.bjgy.convert.DataProdPipelineConvert;
import com.bjgy.convert.DataProdPipelineNodeRecordConvert;
import com.bjgy.dao.DataProdPipelineDao;
import com.bjgy.dao.DataProdPipelineNodeDao;
import com.bjgy.dao.DataProdPipelineNodeRecordDao;
import com.bjgy.dao.DataProdPipelineRecordDao;
import com.bjgy.dao.DataProdToolsCategoryDao;
import com.bjgy.dto.ConsoleLog;
import com.bjgy.dto.Pipeline;
import com.bjgy.dto.PipelineEdge;
import com.bjgy.dto.PipelineNode;
import com.bjgy.dto.PipelineNodeProperties;
import com.bjgy.entity.DataProdPipelineEntity;
import com.bjgy.entity.DataProdPipelineNodeEntity;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.engine.constant.ToolCategoryEnum;
import com.bjgy.etl.engine.factory.EtlFactory;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.flink.common.utils.ThreadUtil;
import com.bjgy.framework.common.exception.ServerException;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.framework.security.user.SecurityUser;
import com.bjgy.query.DataProdPipelineQuery;
import com.bjgy.service.DataProdPipelineService;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import com.bjgy.vo.DataProdPipelineVO;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.data.util.CastUtils;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Stack;
import java.util.stream.Collectors;

/**
 * 数据开发-etl流程
 */
@Service
@AllArgsConstructor
public class DataProdPipelineServiceImpl extends BaseServiceImpl<DataProdPipelineDao, DataProdPipelineEntity> implements DataProdPipelineService {

	private final DataProdPipelineNodeDao nodeDao;
	private final DataProdToolsCategoryDao toolsCategoryDao;
	private final DataProdPipelineRecordDao recordDao;
	private final DataProdPipelineNodeRecordDao nodeRecordDao;
	private final QuartzDataProdPipelineApi quartzApi;

	@Override
	public PageResult<DataProdPipelineVO> page(DataProdPipelineQuery query) {
		IPage<DataProdPipelineEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataProdPipelineConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataProdPipelineEntity> getWrapper(DataProdPipelineQuery query) {
		LambdaQueryWrapper<DataProdPipelineEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataProdPipelineEntity::getName, query.getName())
				.eq(query.getStatus() != null, DataProdPipelineEntity::getStatus, query.getStatus());
		dataScopeWithOrgId(wrapper);
		return wrapper;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long save(Pipeline pipeline) {
		insertOrUpdate(pipeline);
		return pipeline.getId();
	}

	private void insertOrUpdate(Pipeline pipeline) {
		if (pipeline.getTaskType() == 3 && !CronExpression.isValidExpression(pipeline.getCron())) {
			throw new ServerException("cron 表达式有误，请检查后重新填写！");
		}
		DataProdPipelineEntity pipelineEntity = DataProdPipelineEntity.builder().id(pipeline.getId())
				.projectId(getProjectId()).orgId(pipeline.getOrgId()).taskType(pipeline.getTaskType())
				.name(pipeline.getName()).cron(pipeline.getCron()).description(pipeline.getDescription()).status(pipeline.getStatus()).releaseTime(pipeline.getReleaseTime()).releaseUserId(pipeline.getReleaseUserId()).build();
		List<PipelineNode> nodes = pipeline.getNodes();
		List<PipelineEdge> edges = pipeline.getEdges();
		//寻找入度为0的节点
		List<PipelineNode> startNodes = getStartNodes(nodes, edges);
		//检查闭环
		checkClosedLoop(edges);
		//获取节点执行list
		Set<PipelineNode> runNodeSet = new LinkedHashSet<>(6);
		buildRunNodes(runNodeSet, startNodes, nodes, edges);
		if (pipelineEntity.getId() == null) {
			pipelineEntity.setEdges(JSONUtil.toJsonString(edges));
			baseMapper.insert(pipelineEntity);
			//转换前端传过来的节点为entity类型
			List<DataProdPipelineNodeEntity> clientNodes = getNodesByNodeSet(pipelineEntity, runNodeSet);
			//新增节点
			clientNodes.forEach(nodeDao::insert);
		} else {
			List<DataProdPipelineNodeEntity> clientNodes = getNodesByNodeSet(pipelineEntity, runNodeSet);
			pipelineEntity.setEdges(JSONUtil.toJsonString(edges));
			baseMapper.updateById(pipelineEntity);
			//获取库中的节点
			Map<String, Object> queryMap = new HashMap<>();
			queryMap.put("pipeline_id", pipelineEntity.getId());
			List<DataProdPipelineNodeEntity> dbNodes = nodeDao.selectByMap(queryMap);
			//查询clientNodes的properties的id为空的
			List<DataProdPipelineNodeEntity> insertNodes = clientNodes.stream().filter(item -> item.getId() == null).collect(Collectors.toList());
			insertNodes.forEach(nodeDao::insert);
			//查询clientNodes的properties的id不为空的
			clientNodes = getNodesByNodeSet(pipelineEntity, runNodeSet);
			List<DataProdPipelineNodeEntity> updateNodes = clientNodes.stream().filter(item -> item.getId() != null).collect(Collectors.toList());
			updateNodes.forEach(nodeDao::updateById);
			//查询库里有，nodeSet里没有的，则是需要删除的
			for (DataProdPipelineNodeEntity dbNode : dbNodes) {
				if (clientNodes.stream().noneMatch(item -> dbNode.getNo().equals(item.getNo()))) {
					nodeDao.deleteById(dbNode.getId());
				}
			}
		}
		pipeline.setId(pipelineEntity.getId());
	}

	private List<DataProdPipelineNodeEntity> getNodesByNodeSet(DataProdPipelineEntity pipeline, Set<PipelineNode> runNodeSet) {
		List<DataProdPipelineNodeEntity> clientNodes = new ArrayList<>(10);
		int i = 0;
		for (PipelineNode pipelineNode : runNodeSet) {
			i++;
			DataProdPipelineNodeEntity nodeEntity = new DataProdPipelineNodeEntity();
			nodeEntity.setId(pipelineNode.getProperties().getId());
			nodeEntity.setOrgId(pipeline.getOrgId());
			nodeEntity.setPipelineId(pipeline.getId());
			nodeEntity.setProjectId(pipeline.getProjectId());
			nodeEntity.setNo(pipelineNode.getId());
			nodeEntity.setSort(i);
			nodeEntity.setName(pipelineNode.getProperties().getName());
			Map<String, Object> nodeJson = pipelineNode.getProperties().getNodeJson();
			nodeEntity.setNodeJson(StringUtil.toJson(nodeJson));
			nodeEntity.setIcon(pipelineNode.getProperties().getIcon());
			nodeEntity.setType(pipelineNode.getType());
			nodeEntity.setX(pipelineNode.getX());
			nodeEntity.setY(pipelineNode.getY());
			nodeEntity.setToolType(pipelineNode.getProperties().getToolType());
			nodeEntity.setNote(pipelineNode.getProperties().getNote());
			nodeEntity.setToolCategoryId(pipelineNode.getProperties().getToolCategoryId());
			nodeEntity.setFailGoOn(pipelineNode.getProperties().getFailGoOn());
			nodeEntity.setWeight(pipelineNode.getProperties().getWeight());
			nodeEntity.setOverTimes(pipelineNode.getProperties().getOverTimes());
			if (nodeEntity.getToolCategoryId().equals(ToolCategoryEnum.DATA_MERGE.getId())) {
				List<String> sourceNodes = CastUtils.cast(nodeJson.get("sourceNodes"));
				nodeEntity.setDependNo(String.join(",", sourceNodes));
			} else {
				nodeEntity.setDependNo((String) nodeJson.get("dependNodeNo"));
			}
			clientNodes.add(nodeEntity);
		}
		return clientNodes;
	}

	private List<PipelineNode> getStartNodes(List<PipelineNode> nodes, List<PipelineEdge> edges) {
		if (nodes.isEmpty()) {
			throw new ServerException("流程不能为空！");
		}
		List<PipelineNode> startNodes = new ArrayList<>(1);
		for (PipelineNode node : nodes) {
			//如果没有找到targetNodeId为该节点的边，说明该节点为起始节点
			if (edges.stream().noneMatch(item -> node.getId().equals(item.getTargetNodeId()))) {
				startNodes.add(node);
			}
		}
		return startNodes;
	}

	private void buildRunNodes(Set<PipelineNode> nodeSet, List<PipelineNode> startNodes, List<PipelineNode> nodes, List<PipelineEdge> edges) {
		if (startNodes.isEmpty()) {
			return;
		}
		//按权重逆序，权重越高越在前面
		startNodes.sort((item1, item2) -> item2.getProperties().getWeight().compareTo(item1.getProperties().getWeight()));
		for (PipelineNode startNode : startNodes) {
			if (nodes.contains(startNode)) {
				nodeSet.remove(startNode);
			}
			nodeSet.add(startNode);
		}
		//获取子节点，添加到set中
		List<PipelineNode> childNodes = new ArrayList<>(2);
		for (PipelineNode startNode : startNodes) {
			//获取以node为父亲的子节点
			List<PipelineEdge> collect = edges.stream().filter(item -> startNode.getId().equals(item.getSourceNodeId())).collect(Collectors.toList());
			for (PipelineEdge flowEdge : collect) {
				PipelineNode flowNode = nodes.stream().filter(item -> item.getId().equals(flowEdge.getTargetNodeId())).findFirst().get();
				childNodes.add(flowNode);
			}
		}
		//递归子节点的子节点
		buildRunNodes(nodeSet, childNodes, nodes, edges);
	}

	/**
	 * 有向图中判断是否有环
	 */
	private void checkClosedLoop(List<PipelineEdge> edges) {
		// 习惯上转换成临接表的形式
		Map<String, List<String>> adj = buildDag(edges);
		// 定义一个节点状态数组 判断是否访问过
		Map<String, Boolean> visited = new HashMap<>();
		Stack<String> visitedStack;
		Set<String> keySet = adj.keySet();
		for (String key : keySet) {
			visited.put(key, false);
		}
		// 引用传递 函数内部修改值后退出函数可见
		for (String key : keySet) {
			visitedStack = new Stack<>();
			// 如果没有进行访问 则进行深度优先搜索回溯
			if (!visited.get(key)) {
				boolean dfs = dgDfsCycle(adj, key, visited, visitedStack);
				if (dfs) {
					throw new ServerException("流程图存在闭环，请检查！");
				} else {
					visited.put(key, false);
					visitedStack.pop();
				}
			}
		}
	}

	/**
	 * 构建 有向图 邻接表
	 *
	 * @return
	 */
	private Map<String, List<String>> buildDag(List<PipelineEdge> edges) {

		Map<String, List<String>> adj = new HashMap<>();
		if (CollectionUtils.isEmpty(edges)) {
			return adj;
		}
		for (PipelineEdge edg : edges) {
			String node1 = edg.getSourceNodeId();
			String node2 = edg.getTargetNodeId();
			adj.computeIfAbsent(node1, k -> new ArrayList<>());
			adj.computeIfAbsent(node2, k -> new ArrayList<>());
			adj.get(node1).add(node2);
		}
		return adj;
	}

	/**
	 * @param adj     图的临接表
	 * @param current 当前节点
	 * @param visited 判断是否访问
	 */
	private static boolean dgDfsCycle(Map<String, List<String>> adj, String current, Map<String, Boolean> visited, Stack<String> visitedStack) {
		// 首先 访问当前节点 并进行标记
		visited.put(current, true);
		visitedStack.push(current);

		// 获取到当前节点能够到达的所有节点
		List<String> list = adj.get(current);
		for (String can : list) {
			// 如果节点没有被访问过
			if (!visited.get(can)) {
				// 当前节点就是父节点，循环的节点就是子节点
				return dgDfsCycle(adj, can, visited, visitedStack);
			}
			// 在节点被访问过的情况下 说明有环
			else {
				return visitedStack.contains(can);
			}
		}
		return false;
	}

	@Override
	public void update(DataProdPipelineVO vo) {
		DataProdPipelineEntity entity = DataProdPipelineConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
		for (Long id : idList) {
			//同步删除节点
			Map<String, Object> delMap = new HashMap<>();
			delMap.put("pipeline_id", id);
			nodeDao.deleteByMap(delMap);
		}
	}

	@Override
	public Pipeline get(Long id) {
		DataProdPipelineEntity pipelineEntity = baseMapper.selectById(id);
		Pipeline pipeline = new Pipeline();
		pipeline.setId(pipelineEntity.getId());
		pipeline.setOrgId(pipelineEntity.getOrgId());
		pipeline.setName(pipelineEntity.getName());
		pipeline.setCron(pipelineEntity.getCron());
		pipeline.setStatus(pipelineEntity.getStatus());
		pipeline.setReleaseUserId(pipelineEntity.getReleaseUserId());
		pipeline.setReleaseTime(pipelineEntity.getReleaseTime());
		pipeline.setTaskType(pipelineEntity.getTaskType());
		pipeline.setDescription(pipelineEntity.getDescription());
		pipeline.setEdges(JSONUtil.parseObject(pipelineEntity.getEdges(), new TypeReference<List<PipelineEdge>>() {
		}));
		List<PipelineNode> nodes = new ArrayList<>(6);
		pipeline.setNodes(nodes);
		//获取节点
		Map<String, Object> queryMap = new HashMap<>();
		queryMap.put("pipeline_id", id);
		List<DataProdPipelineNodeEntity> dbNodes = nodeDao.selectByMap(queryMap);
		for (DataProdPipelineNodeEntity dbNode : dbNodes) {
			PipelineNode pipelineNode = getFlowNode(dbNode);
			nodes.add(pipelineNode);
		}
		return pipeline;
	}

	@SneakyThrows
	private PipelineNode getFlowNode(DataProdPipelineNodeEntity dbNode) {
		PipelineNode pipelineNode = new PipelineNode();
		pipelineNode.setId(dbNode.getNo());
		pipelineNode.setType(dbNode.getType());
		pipelineNode.setX(dbNode.getX());
		pipelineNode.setY(dbNode.getY());
		PipelineNodeProperties properties = new PipelineNodeProperties();
		properties.setId(dbNode.getId());
		properties.setName(dbNode.getName());
		properties.setIcon(dbNode.getIcon());
		properties.setToolCategoryId(dbNode.getToolCategoryId());
		properties.setToolCategoryName(toolsCategoryDao.selectById(dbNode.getToolCategoryId()).getName());
		properties.setWeight(dbNode.getWeight());
		properties.setToolType(dbNode.getToolType());
		properties.setNote(dbNode.getNote());
		properties.setFailGoOn(dbNode.getFailGoOn());
		properties.setOverTimes(dbNode.getOverTimes());
		if (StringUtil.isNotBlank(dbNode.getNodeJson())) {
			properties.setNodeJson(SingletonObject.OBJECT_MAPPER.readValue(dbNode.getNodeJson(), new TypeReference<Map<String, Object>>() {
			}));
		}
		pipelineNode.setProperties(properties);
		return pipelineNode;
	}


	@Override
	public Long run(Long id) {
		return scheduleRun(id, ExecuteType.HAND);
	}

	@Override
	public Long scheduleRun(Long id, ExecuteType executeType) {
		DataProdPipelineEntity pipelineEntity = baseMapper.selectById(id);
		LambdaQueryWrapper<DataProdPipelineNodeEntity> nodeWrapper = Wrappers.lambdaQuery();
		nodeWrapper.eq(DataProdPipelineNodeEntity::getPipelineId, id)
				.orderByAsc(DataProdPipelineNodeEntity::getSort);
		List<DataProdPipelineNodeEntity> dbNodes = nodeDao.selectList(nodeWrapper);
		DataProdPipelineRecordEntity recordEntity = new DataProdPipelineRecordEntity();
		recordEntity.setCreator(pipelineEntity.getCreator());
		recordEntity.setOrgId(pipelineEntity.getOrgId());
		recordEntity.setProjectId(pipelineEntity.getProjectId());
		recordEntity.setName(pipelineEntity.getName());
		recordEntity.setPipelineId(id);
		recordEntity.setRunStatus(CommonRunStatus.RUNNING.getCode());
		recordEntity.setStartTime(new Date());
		recordEntity.setExecuteType(executeType.getValue());
		recordDao.insert(recordEntity);
		//新增调度日志
		List<PipelineNode> flowNodes = dbNodes.stream().map(this::getFlowNode).collect(Collectors.toList());
		int i = 0;
		//设置开始标识
		EtlEngine.builld().start(recordEntity.getId());
		for (DataProdPipelineNodeEntity dbNode : dbNodes) {
			//返回给前台的节点
			PipelineNode flowNode = flowNodes.stream().filter(item -> item.getId().equals(dbNode.getNo())).findFirst().get();
			flowNode.getProperties().setRunStatus(CommonRunStatus.SUCCESS.getCode());
			flowNode.getProperties().setStyle(PipelineNodeProperties.SUCCESS_STYLE);
			DataProdPipelineNodeRecordEntity nodeRecordEntity = new DataProdPipelineNodeRecordEntity();
			nodeRecordEntity.setCreator(recordEntity.getCreator());
			nodeRecordEntity.setOrgId(recordEntity.getOrgId());
			nodeRecordEntity.setProjectId(recordEntity.getProjectId());
			nodeRecordEntity.setPipelineId(id);
			nodeRecordEntity.setPipelineNodeId(dbNode.getId());
			nodeRecordEntity.setPipelineNodeNo(dbNode.getNo());
			nodeRecordEntity.setPipelineRecordId(recordEntity.getId());
			nodeRecordEntity.setToolCategoryId(dbNode.getToolCategoryId());
			nodeRecordEntity.setRunStatus(CommonRunStatus.RUNNING.getCode());
			nodeRecordEntity.setStartTime(new Date());
			nodeRecordEntity.setExecuteType(executeType.getValue());
			nodeRecordDao.insert(nodeRecordEntity);
			//根据nodeJson执行节点逻辑
			flowNode.getProperties().setNodeRecordId(nodeRecordEntity.getId());
			flowNode.getProperties().setRecordId(recordEntity.getId());
			//设置最后一个节点为结束节点
			if (i == dbNodes.size() - 1) {
				flowNode.getProperties().setIfEnd(true);
			}
			//异步执行每个节点
			ThreadUtil.etlPool.execute(() -> {
				EtlEngine etlEngine = EtlFactory.getEtlEngine(ToolCategoryEnum.getById(dbNode.getToolCategoryId()), flowNode);
				etlEngine.setPipelineList(flowNodes);
				etlEngine.run();
			});
			//看调试效果
			//ThreadUtil.sleep(10000);
			i++;
		}
		return recordEntity.getId();
	}

	@Override
	public ConsoleLog getLog(Integer recordId) {
		ConsoleLog consoleLog = new ConsoleLog();
		DataProdPipelineRecordEntity recordEntity = recordDao.selectById(recordId);
		String log = recordEntity.getLog();
		if (log != null) {
			consoleLog.setLog(log);
			consoleLog.setEnd(CommonRunStatus.SUCCESS.getCode().equals(recordEntity.getRunStatus()) || CommonRunStatus.FAILED.getCode().equals(recordEntity.getRunStatus()));
		}
		return consoleLog;
	}

	@Override
	public List<DataProdPipelineNodeRecordVO> listNodeRecord(Integer recordId) {
		LambdaQueryWrapper<DataProdPipelineNodeRecordEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DataProdPipelineNodeRecordEntity::getPipelineRecordId, recordId).orderByAsc(DataProdPipelineNodeRecordEntity::getId);
		List<DataProdPipelineNodeRecordEntity> nodeRecordEntities = nodeRecordDao.selectList(wrapper);
		if (nodeRecordEntities.isEmpty()) {
			return new ArrayList<>();
		}
		//获取节点之前的关系
		String edges = baseMapper.selectById(nodeRecordEntities.get(0).getPipelineId()).getEdges();
		List<PipelineEdge> flowEdges = JSONUtil.parseObject(edges, new TypeReference<List<PipelineEdge>>() {
		});
		assert flowEdges != null;
		List<DataProdPipelineNodeRecordVO> nodeRecords = DataProdPipelineNodeRecordConvert.INSTANCE.convertList(nodeRecordEntities);
		for (DataProdPipelineNodeRecordVO nodeRecord : nodeRecords) {
			nodeRecord.setEdgeId(flowEdges.stream().filter(item -> nodeRecord.getPipelineNodeNo().equals(item.getTargetNodeId())).findFirst().orElse(new PipelineEdge()).getId());
		}
		return nodeRecords;
	}

	@Override
	public void release(Long id) {
		quartzApi.release(id);
		//更新状态，发布人和发布时间
		DataProdPipelineEntity dbEntity = baseMapper.selectById(id);
		dbEntity.setStatus(1);
		dbEntity.setReleaseUserId(SecurityUser.getUserId().intValue());
		dbEntity.setReleaseTime(new Date());
		baseMapper.updateById(dbEntity);
	}

	@Override
	public void cancle(Long id) {
		quartzApi.cancle(id);
		DataProdPipelineEntity dbEntity = baseMapper.selectById(id);
		dbEntity.setStatus(0);
		dbEntity.setReleaseUserId(null);
		dbEntity.setReleaseTime(null);
		baseMapper.updateById(dbEntity);
	}

	@Override
	public void stop(Long recordId) {
		EtlEngine.builld().stop(recordId);
	}
}
