package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdPipelineNodeRecordConvert;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataProdPipelineNodeRecordQuery;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import com.bjgy.dao.DataProdPipelineNodeRecordDao;
import com.bjgy.service.DataProdPipelineNodeRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据生产-etl节点记录
 */
@Service
@AllArgsConstructor
public class DataProdPipelineNodeRecordServiceImpl extends BaseServiceImpl<DataProdPipelineNodeRecordDao, DataProdPipelineNodeRecordEntity> implements DataProdPipelineNodeRecordService {

    @Override
    public PageResult<DataProdPipelineNodeRecordVO> page(DataProdPipelineNodeRecordQuery query) {
        IPage<DataProdPipelineNodeRecordEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(DataProdPipelineNodeRecordConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<DataProdPipelineNodeRecordEntity> getWrapper(DataProdPipelineNodeRecordQuery query){
        LambdaQueryWrapper<DataProdPipelineNodeRecordEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(DataProdPipelineNodeRecordVO vo) {
        DataProdPipelineNodeRecordEntity entity = DataProdPipelineNodeRecordConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(DataProdPipelineNodeRecordVO vo) {
        DataProdPipelineNodeRecordEntity entity = DataProdPipelineNodeRecordConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}
