package com.bjgy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdPipelineRecordConvert;
import com.bjgy.dao.DataProdPipelineNodeRecordDao;
import com.bjgy.dao.DataProdPipelineRecordDao;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.impl.BaseServiceImpl;
import com.bjgy.query.DataProdPipelineRecordQuery;
import com.bjgy.service.DataProdPipelineRecordService;
import com.bjgy.vo.DataProdPipelineRecordVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.List;

/**
 * 数据生产-etl流程记录
 */
@Service
@AllArgsConstructor
public class DataProdPipelineRecordServiceImpl extends BaseServiceImpl<DataProdPipelineRecordDao, DataProdPipelineRecordEntity> implements DataProdPipelineRecordService {

	private final DataProdPipelineNodeRecordDao nodeRecordDao;

	@Override
	public PageResult<DataProdPipelineRecordVO> page(DataProdPipelineRecordQuery query) {
		IPage<DataProdPipelineRecordEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

		return new PageResult<>(DataProdPipelineRecordConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
	}

	private LambdaQueryWrapper<DataProdPipelineRecordEntity> getWrapper(DataProdPipelineRecordQuery query) {
		LambdaQueryWrapper<DataProdPipelineRecordEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtil.isNotBlank(query.getName()), DataProdPipelineRecordEntity::getName, query.getName())
				.eq(query.getRunStatus() != null, DataProdPipelineRecordEntity::getRunStatus, query.getRunStatus())
				.eq(query.getExecuteType() != null, DataProdPipelineRecordEntity::getExecuteType, query.getExecuteType())
				.gt(query.getStartTime() != null, DataProdPipelineRecordEntity::getStartTime, query.getStartTime())
				.lt(query.getEndTime() != null, DataProdPipelineRecordEntity::getEndTime, query.getEndTime());
		dataScopeWithOrgId(wrapper);
		wrapper.orderByDesc(DataProdPipelineRecordEntity::getId);
		return wrapper;
	}

	@Override
	public void save(DataProdPipelineRecordVO vo) {
		DataProdPipelineRecordEntity entity = DataProdPipelineRecordConvert.INSTANCE.convert(vo);

		baseMapper.insert(entity);
	}

	@Override
	public void update(DataProdPipelineRecordVO vo) {
		DataProdPipelineRecordEntity entity = DataProdPipelineRecordConvert.INSTANCE.convert(vo);

		updateById(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(List<Long> idList) {
		removeByIds(idList);
		LambdaQueryWrapper<DataProdPipelineNodeRecordEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.in(DataProdPipelineNodeRecordEntity::getPipelineRecordId, idList);
		nodeRecordDao.delete(wrapper);
	}

}
