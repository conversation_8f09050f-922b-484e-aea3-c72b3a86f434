package com.bjgy.service;

import java.util.List;
import java.util.Map;

/**
 * 数据开发-ETL工具服务
 */
public interface DataEtlToolService {

    /**
     * 测试Kafka连接
     *
     * @param params 连接参数
     * @return 测试结果
     */
    String testKafkaConnection(Map<String, Object> params);

    /**
     * 获取Kafka主题列表
     *
     * @param params 连接参数
     * @return 主题列表
     */
    List<Map<String, Object>> listKafkaTopics(Map<String, Object> params);
} 