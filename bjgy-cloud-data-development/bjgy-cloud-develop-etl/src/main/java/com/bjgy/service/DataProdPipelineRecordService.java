package com.bjgy.service;

import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.query.DataProdPipelineRecordQuery;
import com.bjgy.vo.DataProdPipelineRecordVO;

import java.util.List;

/**
 * 数据生产-etl流程记录
 */
public interface DataProdPipelineRecordService extends BaseService<DataProdPipelineRecordEntity> {

	PageResult<DataProdPipelineRecordVO> page(DataProdPipelineRecordQuery query);

	void save(DataProdPipelineRecordVO vo);

	void update(DataProdPipelineRecordVO vo);

	void delete(List<Long> idList);
}
