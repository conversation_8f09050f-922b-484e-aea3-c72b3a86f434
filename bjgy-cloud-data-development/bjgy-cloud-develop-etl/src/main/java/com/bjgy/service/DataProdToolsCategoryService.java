package com.bjgy.service;

import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.TreeNodeVo;
import com.bjgy.framework.mybatis.service.BaseService;
import com.bjgy.vo.DataProdToolsCategoryVO;
import com.bjgy.entity.DataProdToolsCategoryEntity;

import java.util.List;

/**
 * 数据开发-工具类别
 */
public interface DataProdToolsCategoryService extends BaseService<DataProdToolsCategoryEntity> {

    void save(DataProdToolsCategoryVO vo);

    void update(DataProdToolsCategoryVO vo);

    void delete(List<Long> idList);

	List<DataProdToolsCategoryVO> listTree(Integer folder);
}
