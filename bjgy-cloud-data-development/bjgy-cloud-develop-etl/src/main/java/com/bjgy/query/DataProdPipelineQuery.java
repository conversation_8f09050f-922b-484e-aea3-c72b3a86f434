package com.bjgy.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.common.query.Query;

import java.util.Date;

/**
* 数据开发-etl流程查询
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "数据开发-etl流程查询")
public class DataProdPipelineQuery extends Query {
	@Schema(description = "名称")
	private String name;
	@Schema(description = "0-未发布 1-已发布")
	private Integer status;
}
