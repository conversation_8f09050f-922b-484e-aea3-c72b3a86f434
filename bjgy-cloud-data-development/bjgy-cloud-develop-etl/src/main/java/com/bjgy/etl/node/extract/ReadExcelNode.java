package com.bjgy.etl.node.extract;

import lombok.Data;

/**
 * @ClassName 读取Excel文件
 */
@Data
public class ReadExcelNode {
    private String dependNodeNo;
    private String fileUrl;
    private String fileName;
    private String sheetName;
    private Integer startRow = 2;
    private Integer endRow = 0;
    private Integer headerRow = 1;
    private String startColumn = "A";
    private String endColumn = "";
    private Boolean skipEmptyRows = true;
    private Integer fetchSize = 5000;
} 