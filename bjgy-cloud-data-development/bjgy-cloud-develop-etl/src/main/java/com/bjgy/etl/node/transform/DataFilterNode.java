 /*
  * Copyright (c) 2024 天津数睿通科技有限公司
  * All rights reserved.
  */
 package com.bjgy.etl.node.transform;

 import lombok.Data;

 import java.util.List;

 /**
  * 数据过滤器节点配置类
  *
  * @ClassName DataFilterNode
  */
 @Data
 public class DataFilterNode {

	 // 基本配置
	 private String dependNodeNo;                 // 数据源节点编号
	 private String filterMode;                   // 过滤模式：SIMPLE/ADVANCED

	 // 简单过滤配置
	 private String logicalOperator;              // 逻辑操作符：AND/OR
	 private List<FilterCondition> filterConditions; // 过滤条件数组

	 // 高级过滤配置
	 private String expressionType;               // 表达式类型：CUSTOM/SQL
	 private String customExpression;             // 自定义表达式
	 private String sqlCondition;                 // SQL条件

	 // 数据处理选项
	 private Boolean enableDataValidation;        // 启用数据类型验证
	 private Boolean logFilteredData;             // 记录被过滤掉的数据

	 // 性能优化
	 private Integer batchSize;                   // 批处理大小
	 private Boolean enableParallelProcessing;    // 启用并行处理

	 // 错误处理
	 private String errorHandling;                // 错误处理策略：SKIP/STOP/LOG

	 /**
	  * 过滤条件
	  */
	 @Data
	 public static class FilterCondition {
		 private String fieldName;                // 字段名
		 private String operator;                 // 操作符
		 private String value;                    // 比较值
		 private String minValue;                 // 最小值（用于BETWEEN操作）
		 private String maxValue;                 // 最大值（用于BETWEEN操作）
		 private String dataType;                 // 数据类型：STRING/NUMBER/DATE/BOOLEAN
	 }
 }
