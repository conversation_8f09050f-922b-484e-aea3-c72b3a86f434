package com.bjgy.etl.node.load;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName 写入Kafka节点
 */
@Data
public class WriteKafkaNode {

    /**
     * 依赖节点编号
     */
    private String dependNodeNo;

    /**
     * Bootstrap服务器地址
     */
    private String bootstrapServers = "localhost:9092";

    /**
     * Kafka主题名称
     */
    private String topic;

    /**
     * 消息键策略：none-无键, field-字段值, uuid-UUID, hash-数据哈希
     */
    private String keyStrategy = "none";

    /**
     * 键字段名（当keyStrategy为field时使用）
     */
    private String keyField;

    /**
     * 分区策略：default-默认分区, round_robin-轮询分区, custom-自定义分区
     */
    private String partitionStrategy = "default";

    /**
     * 分区字段名（当partitionStrategy为custom时使用）
     */
    private String partitionField;

    /**
     * 批处理大小
     */
    private Integer batchSize = 100;

    /**
     * 发送超时时间（毫秒）
     */
    private Integer requestTimeoutMs = 30000;

    /**
     * 重试次数
     */
    private Integer retries = 3;

    /**
     * 安全协议：PLAINTEXT, SASL_PLAINTEXT, SASL_SSL, SSL
     */
    private String securityProtocol = "PLAINTEXT";

    /**
     * SASL机制：PLAIN, SCRAM-SHA-256, SCRAM-SHA-512
     */
    private String saslMechanism = "PLAIN";

    /**
     * SASL用户名
     */
    private String saslUsername;

    /**
     * SASL密码
     */
    private String saslPassword;

    /**
     * 消息格式：JSON, AVRO, STRING, CSV
     */
    private String messageFormat = "JSON";

    /**
     * 字符编码：UTF-8, GBK, ISO-8859-1
     */
    private String encoding = "UTF-8";

    /**
     * CSV分隔符（当messageFormat为CSV时使用）
     */
    private String csvDelimiter = ",";

    /**
     * 是否包含标题行（当messageFormat为CSV时使用）
     */
    private Boolean includeHeader = false;

    /**
     * 字段映射配置
     */
    private List<FieldMapping> fieldMappings = new ArrayList<>();

    /**
     * 确认机制：0-无确认, 1-Leader确认, all-全部副本确认
     */
    private String acks = "1";

    /**
     * 压缩类型：none, gzip, snappy, lz4, zstd
     */
    private String compressionType = "none";

    /**
     * 是否启用幂等生产者
     */
    private Boolean enableIdempotence = false;

    /**
     * 是否启用事务支持
     */
    private Boolean enableTransaction = false;

    /**
     * 事务ID（当enableTransaction为true时使用）
     */
    private String transactionalId;

    /**
     * 缓冲内存大小（MB）
     */
    private Integer bufferMemory = 32;

    /**
     * 空值处理策略：skip-跳过空值, send_null-发送空消息, default_value-替换为默认值
     */
    private String nullValueHandling = "skip";

    /**
     * 默认值（当nullValueHandling为default_value时使用）
     */
    private String defaultValue;

    /**
     * 是否异步发送
     */
    private Boolean asyncSend = true;

    /**
     * 字段映射内部类
     */
    @Data
    public static class FieldMapping {
        /**
         * 源字段名
         */
        private String sourceField;

        /**
         * 目标字段名
         */
        private String targetField;

        /**
         * 数据类型：NUMERIC, DATE, BOOLEAN, STRING
         */
        private String dataType = "STRING";

        /**
         * 格式化规则
         */
        private String format;
    }

    /**
     * 获取字段映射配置，确保不返回null
     */
    public List<FieldMapping> getFieldMappings() {
        if (fieldMappings == null) {
            fieldMappings = new ArrayList<>();
        }
        return fieldMappings;
    }
}
