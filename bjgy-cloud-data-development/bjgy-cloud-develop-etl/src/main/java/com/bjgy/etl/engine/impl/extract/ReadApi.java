package com.bjgy.etl.engine.impl.extract;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.integrate.DataAccessApi;
import com.bjgy.api.module.data.integrate.dto.DataApiDto;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.extract.ApiInputNode;
import com.bjgy.flink.common.context.SpringContextUtils;
import com.bjgy.flink.common.utils.ThreadUtil;
import com.bjgy.framework.common.apiauth.constant.ApiMethod;
import com.bjgy.framework.common.apiauth.model.RequestParam;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ReadApi
 */
@Slf4j
public class ReadApi extends EtlEngine {

	private final DataAccessApi dataAccessApi;
	private String dependNodeNo;

	public ReadApi(PipelineNode pipelineNode) {
		super(pipelineNode);
		this.dataAccessApi = SpringContextUtils.getBeanByClass(DataAccessApi.class);
	}

	@SneakyThrows
	@Override
	public void run() {
		initCache();
		Integer overTimes = pipelineNodeEntity.getOverTimes();
		long startTime = System.currentTimeMillis();
		long totalCount = 0;

		try {
			ApiInputNode apiInputNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, ApiInputNode.class);
			Integer fetchSize = apiInputNode.getFetchSize();
			dependNodeNo = apiInputNode.getDependNodeNo();

			// 等待依赖节点
			waitForDependNode(dependNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

			// 获取API信息
			DataApiDto dataApiDto = dataAccessApi.getApiById(apiInputNode.getApiId());
			if (dataApiDto == null) {
				throw new RuntimeException("API已被删除，无法获取数据");
			}

			appendLog(String.format("节点 【%s】 开始从API获取数据：%s (%s %s)",
					pipelineNode.getProperties().getName(),
					dataApiDto.getApiName(),
					dataApiDto.getRequestMethod(),
					dataApiDto.getRequestUrl()));

			// 执行API数据获取逻辑
			if (dataApiDto.getIfPage() == 1) {
				totalCount = fetchDataWithPagination(dataApiDto, fetchSize, startTime, overTimes);
			} else {
				totalCount = fetchDataSingle(dataApiDto, fetchSize, startTime, overTimes);
			}

			appendLog(String.format("节点 【%s】 运行成功结束，获取数据总数：%s", pipelineNode.getProperties().getName(), totalCount));
			successEnd();

		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，获取数据总数：%s，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			failEnd();
		}
	}

	/**
	 * 分页获取数据
	 */
	private long fetchDataWithPagination(DataApiDto dataApiDto, Integer fetchSize, long startTime, Integer overTimes) throws Exception {
		long totalCount = 0;
		int page = 1;
		int limit = 100; // 每页默认100条

		while (true) {
			//检查依赖节点是否失败
			dependFailedCheck(dependNodeNo);
			// 检查是否被停止
			if (isStop()) {
				appendLog(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查超时
			if (checkOverTime(startTime, System.currentTimeMillis(), overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 获取数据
			List<Map<String, Object>> dataList = getDataList(dataApiDto, page, limit);
			if (dataList.isEmpty()) {
				break;
			}

			// 处理数据
			for (Map<String, Object> dataMap : dataList) {
				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}

				// 推送到内存队列
				pushDataToDownstream(dataMap);
				totalCount++;

				// 控制内存队列大小
				while (getCurrentQueueSize() >= fetchSize) {
					dependFailedCheck(dependNodeNo);
					if (isStop()) {
						throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
					}
					ThreadUtil.sleep(100);
				}
			}

			// 判断是否还有更多数据
			if (dataList.size() < limit) {
				break;
			}

			page++;

			// 更新日志
			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条记录", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}
		}

		return totalCount;
	}

	/**
	 * 单次获取数据
	 */
	private long fetchDataSingle(DataApiDto dataApiDto, Integer fetchSize, long startTime, Integer overTimes) throws Exception {
		long totalCount = 0;

		// 获取数据
		List<Map<String, Object>> dataList = getDataList(dataApiDto, null, null);
		if (dataList.isEmpty()) {
			return 0;
		}

		// 处理数据
		for (Map<String, Object> dataMap : dataList) {
			dependFailedCheck(dependNodeNo);
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			if (checkOverTime(startTime, System.currentTimeMillis(), overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 推送到内存队列
			pushDataToDownstream(dataMap);
			totalCount++;

			// 更新日志
			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条记录", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}

			// 控制内存队列大小
			while (getCurrentQueueSize() >= fetchSize) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}
				ThreadUtil.sleep(100);
			}
		}

		return totalCount;
	}

	/**
	 * 获取数据列表，参考DataApiAccessTask的实现
	 */
	private List<Map<String, Object>> getDataList(DataApiDto dataApiDto, Integer page, Integer limit) {
		try {
			// 统一加一下分页参数
			if (dataApiDto.getIfPage() == 1 && page != null && limit != null) {
				List<RequestParam> requestParam = dataApiDto.getRequestParam();
				List<RequestParam> bodyFormData = dataApiDto.getBodyFormData();
				List<RequestParam> bodyUrlencoded = dataApiDto.getBodyUrlencoded();

				if (requestParam == null) {
					requestParam = new ArrayList<>();
					dataApiDto.setRequestParam(requestParam);
				}
				if (bodyFormData == null) {
					bodyFormData = new ArrayList<>();
					dataApiDto.setBodyFormData(bodyFormData);
				}
				if (bodyUrlencoded == null) {
					bodyUrlencoded = new ArrayList<>();
					dataApiDto.setBodyUrlencoded(bodyUrlencoded);
				}

				requestParam.add(new RequestParam(dataApiDto.getPageKey(), String.valueOf(page)));
				requestParam.add(new RequestParam(dataApiDto.getPageSizeKey(), String.valueOf(limit)));
				bodyFormData.add(new RequestParam(dataApiDto.getPageKey(), String.valueOf(page)));
				bodyFormData.add(new RequestParam(dataApiDto.getPageSizeKey(), String.valueOf(limit)));
				bodyUrlencoded.add(new RequestParam(dataApiDto.getPageKey(), String.valueOf(page)));
				bodyUrlencoded.add(new RequestParam(dataApiDto.getPageSizeKey(), String.valueOf(limit)));

				// 请求体加一下
				if (ApiMethod.POST.getName().equals(dataApiDto.getRequestMethod())) {
					String bodyRaw = StringUtil.isBlank(dataApiDto.getBodyRaw()) ? "{}" : dataApiDto.getBodyRaw();
					Map<String, Object> bodyMap = StringUtil.fromJson(bodyRaw, new TypeReference<Map<String, Object>>() {
					});
					bodyMap.put(dataApiDto.getPageKey(), page);
					bodyMap.put(dataApiDto.getPageSizeKey(), limit);
					dataApiDto.setBodyRaw(StringUtil.toJson(bodyMap));
				}
			}

			// 调接口获取数据
			return dataAccessApi.fetchApiData(dataApiDto).getData();
		} catch (Exception e) {
			log.error("Failed to fetch API data: {}", e.getMessage(), e);
			return new ArrayList<>();
		}
	}

	/**
	 * 获取当前队列大小
	 * 如果启用了数据复制机制，返回所有下游队列的最大大小
	 * 否则返回原有单一队列的大小
	 */
	private long getCurrentQueueSize() {
		if (isDataReplicationEnabled()) {
			// 数据复制机制：返回所有下游队列中的最大大小
			long maxQueueSize = 0;
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = downstreamDataKeys.get(downstreamNodeId);
				if (dataKey != null) {
					long queueSize = memoryManager.getQueueSize(dataKey);
					maxQueueSize = Math.max(maxQueueSize, queueSize);
				}
			}
			return maxQueueSize;
		} else {
			// 原有机制：返回单一队列大小
			return memoryManager.getQueueSize(NODE_DATA_KEY);
		}
	}
}
