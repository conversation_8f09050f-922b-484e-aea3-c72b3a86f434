package com.bjgy.etl.engine.constant;

import com.bjgy.framework.common.exception.ServerException;

public enum ToolCategoryEnum {
	/**
	 * 读取数据库表
	 */
	READ_DB(3L, "读取数据库表"),
	/**
	 * 写入数据库表
	 */
	INERT_DB(4L, "写入数据库表"),
	/**
	 * 读取Kafka
	 */
	READ_KAFKA(5L, "读取Kafka"),
	/**
	 * 写入Kafka
	 */
	WRITE_KAFKA(8L, "写入Kafka"),
	/**
	 * 字段名映射
	 */
	COLUMN_MAP(16L, "字段名映射"),
	/**
	 * Rest API输入
	 */
	REST_API(21L, "Rest API输入"),
	/**
	 * 读取Excel文件
	 */
	READ_EXCEL(25L, "读取Excel文件"),
	/**
	 * 输出Excel文件
	 */
	WRITE_EXCEL(26L, "输出Excel文件"),

	/**
	 * 数据合并
	 */
	DATA_MERGE(27L, "数据合并"),

	/**
	 * 数据过滤器
	 */
	DATA_FILTER(17L, "数据过滤器"),

	/**
	 * 数据清洗
	 */
	DATA_CLEAN(18L, "数据清洗"),

	/**
	 * 数据加密
	 */
	DATA_ENCRYPT(19L, "数据加密"),

	/**
	 * 数据脱敏
	 */
	DATA_DESENSITIZE(31L, "数据脱敏"),

	/**
	 * 数据解密
	 */
	DATA_DECRYPT(20L, "数据解密"),
	;


	private final Long id;
	private final String name;

	ToolCategoryEnum(Long id, String name) {
		this.id = id;
		this.name = name;
	}

	public Long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public static ToolCategoryEnum getById(Long id) {
		for (ToolCategoryEnum categoryEnum : ToolCategoryEnum.values()) {
			if (categoryEnum.getId().equals(id)) {
				return categoryEnum;
			}
		}
		throw new ServerException("不支持的组件类型！");
	}
}
