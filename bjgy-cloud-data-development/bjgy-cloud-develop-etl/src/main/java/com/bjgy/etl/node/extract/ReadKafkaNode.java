package com.bjgy.etl.node.extract;

import lombok.Data;

/**
 * @ClassName 读取Kafka消息
 */
@Data
public class ReadKafkaNode {
    private String dependNodeNo;
    
    // Kafka连接配置
    private String bootstrapServers = "localhost:9092";
    private String topic;
    private String groupId;
    
    // 消费配置
    private String autoOffsetReset = "latest";
    private Integer maxPollRecords = 500;
    private Integer pollTimeoutMs = 1000;
    private Integer maxPollIntervalMs = 300000;
    
    // 安全配置
    private String securityProtocol = "PLAINTEXT";
    private String saslMechanism = "PLAIN";
    private String saslUsername;
    private String saslPassword;
    
    // 数据格式配置
    private String messageFormat = "JSON";
    private String encoding = "UTF-8";
    
    // 高级配置
    private String runMode = "limited";
    private Long maxMessageCount = 10000L;
    private Boolean enableAutoCommit = true;
    private Integer autoCommitIntervalMs = 5000;
    private Integer sessionTimeoutMs = 30000;
    private Integer heartbeatIntervalMs = 3000;
} 