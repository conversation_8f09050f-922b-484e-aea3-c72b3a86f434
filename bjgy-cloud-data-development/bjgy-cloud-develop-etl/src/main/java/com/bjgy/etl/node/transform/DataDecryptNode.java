/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.node.transform;

import lombok.Data;

import java.util.List;

/**
 * 数据解密节点配置
 */
@Data
public class DataDecryptNode {

    /**
     * 依赖节点编号
     */
    private String dependNodeNo;

    /**
     * 解密模式：FIELD_LEVEL、ROW_LEVEL、CUSTOM
     */
    private String decryptMode;

    /**
     * 字段级解密配置
     */
    private List<DecryptionField> decryptFields;

    /**
     * 行级解密配置 - 加密字段名
     */
    private String encryptedFieldName;

    /**
     * 行级解密算法
     */
    private String rowDecryptAlgorithm;

    /**
     * 行级解密密钥/私钥
     */
    private String rowSecretKey;

    /**
     * 行级解密初始化向量
     */
    private String rowIV;

    /**
     * 数据序列化格式
     */
    private String serializationFormat;

    /**
     * 分隔符
     */
    private String delimiter;

    /**
     * 是否保留加密字段
     */
    private Boolean keepEncryptedField;

    /**
     * 脚本类型
     */
    private String scriptType;

    /**
     * JavaScript脚本
     */
    private String javascriptCode;

    /**
     * 批处理大小
     */
    private Integer batchSize;

    /**
     * 错误处理策略
     */
    private String errorHandling;

    /**
     * 是否输出统计信息
     */
    private Boolean outputStatistics;

    /**
     * 是否验证解密结果
     */
    private Boolean validateDecryption;

    /**
     * 解密字段配置
     */
    @Data
    public static class DecryptionField {
        /**
         * 加密字段名
         */
        private String encryptedFieldName;

        /**
         * 解密算法
         */
        private String algorithm;

        /**
         * 密钥/私钥
         */
        private String secretKey;

        /**
         * 初始化向量
         */
        private String iv;

        /**
         * 输出字段名
         */
        private String outputFieldName;

        /**
         * 是否保留加密字段
         */
        private Boolean keepEncrypted;
    }
}
