/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.engine.impl.transform;

import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.transform.DataEncryptNode;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.flink.common.utils.ThreadUtil;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据加密引擎
 *
 * @ClassName DataEncryptEngine
 */
@Slf4j
public class DataEncrypt extends EtlEngine {

	private DataEncryptNode dataEncryptNode;
	private final AtomicLong processedCount = new AtomicLong(0);
	private final AtomicLong encryptedCount = new AtomicLong(0);
	private final AtomicLong errorCount = new AtomicLong(0);
	private final long startTime = System.currentTimeMillis();

	public DataEncrypt(PipelineNode pipelineNode) {
		super(pipelineNode);
		this.dataEncryptNode = JSONUtil.parseObject(nodeJson, DataEncryptNode.class);
	}

	@Override
	public void run() {
		try {
			initCache();

			// 等待依赖节点
			waitForDependNode(dataEncryptNode.getDependNodeNo());

			appendLog(String.format("节点【%s】开始运行", pipelineNode.getProperties().getName()));

			// 根据加密模式执行不同的加密逻辑
			switch (dataEncryptNode.getEncryptMode()) {
				case "FIELD_LEVEL":
					executeFieldLevelEncryption();
					break;
				case "ROW_LEVEL":
					executeRowLevelEncryption();
					break;
				case "CUSTOM":
					executeCustomEncryption();
					break;
				default:
					throw new RuntimeException(String.format("节点【%s】不支持的加密模式: %s", pipelineNode.getProperties().getName(), dataEncryptNode.getEncryptMode()));
			}

			// 输出统计信息
			if (dataEncryptNode.getOutputStatistics()) {
				appendLog(String.format("节点【%s】加密完成 - 处理记录数: %d, 加密记录数: %d, 错误记录数: %d", pipelineNode.getProperties().getName(),
						processedCount.get(), encryptedCount.get(), errorCount.get()));
			}

			successEnd();

		} catch (Exception e) {
			log.error("数据加密节点运行失败", e);
			appendLog(String.format("节点【%s】数据加密节点运行失败: %s", pipelineNode.getProperties().getName(), e.getMessage()));
			failEnd();
		}
	}

	/**
	 * 执行字段级加密
	 */
	private void executeFieldLevelEncryption() {
		appendLog(String.format("节点【%s】开始执行字段级加密", pipelineNode.getProperties().getName()));
		String sourceDataKey = getSourceDataKey(dataEncryptNode.getDependNodeNo());

		List<Map<String, Object>> batchData = new ArrayList<>();
		int batchSize = dataEncryptNode.getBatchSize() != null ? dataEncryptNode.getBatchSize() : 1000;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataEncryptNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataEncryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batchData.add(data);
						if (batchData.size() >= batchSize) {
							processBatch(batchData);
							batchData.clear();
						}
					}
					// 处理最后剩余的数据
					if (!batchData.isEmpty()) {
						processBatch(batchData);
						batchData.clear();
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			batchData.add(data);
			if (batchData.size() >= batchSize) {
				processBatch(batchData);
				batchData.clear();
			}
		}
	}

	/**
	 * 执行行级加密
	 */
	private void executeRowLevelEncryption() {
		appendLog(String.format("节点【%s】开始执行行级加密", pipelineNode.getProperties().getName()));
		String sourceDataKey = getSourceDataKey(dataEncryptNode.getDependNodeNo());

		List<Map<String, Object>> batchData = new ArrayList<>();
		int batchSize = dataEncryptNode.getBatchSize() != null ? dataEncryptNode.getBatchSize() : 1000;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}
			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataEncryptNode.getDependNodeNo());


			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataEncryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batchData.add(data);
						if (batchData.size() >= batchSize) {
							processRowLevelBatch(batchData);
							batchData.clear();
						}
					}
					// 处理最后剩余的数据
					if (!batchData.isEmpty()) {
						processRowLevelBatch(batchData);
						batchData.clear();
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			batchData.add(data);
			if (batchData.size() >= batchSize) {
				processRowLevelBatch(batchData);
				batchData.clear();
			}
		}
	}

	/**
	 * 执行自定义加密
	 */
	private void executeCustomEncryption() {
		appendLog("开始执行自定义加密");
		String sourceDataKey = getSourceDataKey(dataEncryptNode.getDependNodeNo());

		ScriptEngine scriptEngine = null;
		if ("JAVASCRIPT".equals(dataEncryptNode.getScriptType())) {
			ScriptEngineManager manager = new ScriptEngineManager();
			scriptEngine = manager.getEngineByName("javascript");
		}

		while (true) {

			// 检查是否需要停止
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}
			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataEncryptNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataEncryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						try {
							Map<String, Object> encryptedData = executeCustomScript(data, scriptEngine);
							pushDataToDownstream(encryptedData);
							processedCount.incrementAndGet();
							encryptedCount.incrementAndGet();
						} catch (Exception e) {
							handleError("自定义加密脚本执行失败", e, data);
						}
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			try {
				Map<String, Object> encryptedData = executeCustomScript(data, scriptEngine);
				pushDataToDownstream(encryptedData);
				processedCount.incrementAndGet();
				encryptedCount.incrementAndGet();

			} catch (Exception e) {
				handleError("自定义加密脚本执行失败", e, data);
			}
		}
	}

	/**
	 * 处理批量数据（字段级加密）
	 */
	private void processBatch(List<Map<String, Object>> batchData) {
		for (Map<String, Object> data : batchData) {
			try {
				Map<String, Object> encryptedData = new HashMap<>(data);
				boolean hasEncryption = false;

				// 对每个配置的加密字段进行加密
				for (DataEncryptNode.EncryptionField field : dataEncryptNode.getEncryptFields()) {
					if (encryptedData.containsKey(field.getFieldName())) {
						Object originalValue = encryptedData.get(field.getFieldName());
						if (originalValue != null) {
							String encryptedValue = encryptValue(originalValue.toString(), field);

							// 设置输出字段名
							String outputFieldName = StringUtil.isNotBlank(field.getOutputFieldName())
									? field.getOutputFieldName()
									: field.getFieldName();

							encryptedData.put(outputFieldName, encryptedValue);

							// 是否保留原值
							if (!field.getKeepOriginal() && !outputFieldName.equals(field.getFieldName())) {
								encryptedData.remove(field.getFieldName());
							}

							hasEncryption = true;
						}
					}
				}

				// 添加元数据信息
				if (dataEncryptNode.getKeepMetadata() && hasEncryption) {
					Map<String, Object> metadata = new HashMap<>();
					metadata.put("encryptTime", System.currentTimeMillis());
					metadata.put("encryptMode", "FIELD_LEVEL");
					encryptedData.put("_encrypt_metadata", StringUtil.toJson(metadata));
				}

				pushDataToDownstream(encryptedData);
				processedCount.incrementAndGet();
				if (hasEncryption) {
					encryptedCount.incrementAndGet();
				}

			} catch (Exception e) {
				handleError("字段级加密处理失败", e, data);
			}
		}
	}

	/**
	 * 处理批量数据（行级加密）
	 */
	private void processRowLevelBatch(List<Map<String, Object>> batchData) {
		for (Map<String, Object> data : batchData) {
			try {
				// 根据字段模式选择要加密的数据
				Map<String, Object> dataToEncrypt = selectFieldsForEncryption(data);

				// 序列化数据
				String serializedData = serializeData(dataToEncrypt);

				// 加密序列化后的数据
				String encryptedData = encryptRowData(serializedData);

				// 构建输出数据
				Map<String, Object> outputData = new HashMap<>();
				if (dataEncryptNode.getKeepOriginalFields()) {
					outputData.putAll(data);
				}

				outputData.put(dataEncryptNode.getRowOutputFieldName(), encryptedData);

				// 添加元数据信息
				if (dataEncryptNode.getKeepMetadata()) {
					Map<String, Object> metadata = new HashMap<>();
					metadata.put("encryptTime", System.currentTimeMillis());
					metadata.put("encryptMode", "ROW_LEVEL");
					metadata.put("algorithm", dataEncryptNode.getRowEncryptAlgorithm());
					metadata.put("serializationFormat", dataEncryptNode.getSerializationFormat());
					outputData.put("_encrypt_metadata", StringUtil.toJson(metadata));
				}

				pushDataToDownstream(outputData);
				processedCount.incrementAndGet();
				encryptedCount.incrementAndGet();

			} catch (Exception e) {
				handleError("行级加密处理失败", e, data);
			}
		}
	}

	/**
	 * 加密单个字段值
	 */
	private String encryptValue(String value, DataEncryptNode.EncryptionField field) throws Exception {
		String algorithm = field.getAlgorithm();
		String secretKey = field.getSecretKey();
		String salt = field.getSalt();
		String iv = field.getIv();

		switch (algorithm) {
			case "AES256":
			case "AES128":
				return encryptAES(value, secretKey, iv, algorithm);
			case "DES":
				return encryptDES(value, secretKey, iv);
			case "3DES":
				return encrypt3DES(value, secretKey, iv);
			case "RSA":
				return encryptRSA(value, secretKey);
			case "MD5":
				return hashMD5(value, salt);
			case "SHA1":
				return hashSHA1(value, salt);
			case "SHA256":
				return hashSHA256(value, salt);
			case "SHA512":
				return hashSHA512(value, salt);
			case "BASE64":
				return Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8));
			default:
				throw new RuntimeException("不支持的加密算法: " + algorithm);
		}
	}

	/**
	 * AES加密
	 */
	private String encryptAES(String data, String key, String iv, String algorithm) throws Exception {
		// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
		String transformation;
		if (StringUtil.isBlank(iv)) {
			transformation = "AES/ECB/PKCS5Padding";
		} else {
			transformation = "AES/CBC/PKCS5Padding";
		}

		// 确保密钥长度正确
		byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
		if (algorithm.equals("AES256") && keyBytes.length != 32) {
			throw new RuntimeException("AES256密钥长度必须是32位，当前长度: " + keyBytes.length);
		} else if (algorithm.equals("AES128") && keyBytes.length != 16) {
			throw new RuntimeException("AES128密钥长度必须是16位，当前长度: " + keyBytes.length);
		}

		SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
		Cipher cipher = Cipher.getInstance(transformation);

		if (StringUtil.isNotBlank(iv)) {
			byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
			if (ivBytes.length != 16) {
				throw new RuntimeException("AES IV长度必须是16位，当前长度: " + ivBytes.length);
			}
			IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
		} else {
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
		}

		byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
		return Base64.getEncoder().encodeToString(encrypted);
	}

	private String encryptDES(String data, String key, String iv) throws Exception {
		// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
		String transformation;
		if (StringUtil.isBlank(iv)) {
			transformation = "DES/ECB/PKCS5Padding";
		} else {
			transformation = "DES/CBC/PKCS5Padding";
		}

		// 确保密钥长度正确（DES密钥必须是8字节）
		byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
		if (keyBytes.length != 8) {
			throw new RuntimeException("DES密钥长度必须是8位，当前长度: " + keyBytes.length);
		}

		SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DES");
		Cipher cipher = Cipher.getInstance(transformation);

		if (StringUtil.isNotBlank(iv)) {
			byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
			if (ivBytes.length != 8) {
				throw new RuntimeException("DES IV长度必须是8位，当前长度: " + ivBytes.length);
			}
			IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
		} else {
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
		}

		byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
		return Base64.getEncoder().encodeToString(encrypted);
	}

	private String encrypt3DES(String data, String key, String iv) throws Exception {
		// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
		String transformation;
		if (StringUtil.isBlank(iv)) {
			transformation = "DESede/ECB/PKCS5Padding";
		} else {
			transformation = "DESede/CBC/PKCS5Padding";
		}

		// 确保密钥长度正确（3DES密钥必须是24字节）
		byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
		if (keyBytes.length != 24) {
			throw new RuntimeException("3DES密钥长度必须是24位，当前长度: " + keyBytes.length);
		}

		SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DESede");
		Cipher cipher = Cipher.getInstance(transformation);

		if (StringUtil.isNotBlank(iv)) {
			byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
			if (ivBytes.length != 8) {
				throw new RuntimeException("3DES IV长度必须是8位，当前长度: " + ivBytes.length);
			}
			IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
		} else {
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
		}

		byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
		return Base64.getEncoder().encodeToString(encrypted);
	}

	/**
	 * RSA加密
	 */
	private String encryptRSA(String data, String publicKey) throws Exception {
		try {
			// 移除公钥字符串中的头尾标识和换行符
			String cleanPublicKey = publicKey
					.replace("-----BEGIN PUBLIC KEY-----", "")
					.replace("-----END PUBLIC KEY-----", "")
					.replaceAll("\\s", "");

			// Base64解码公钥
			byte[] keyBytes = Base64.getDecoder().decode(cleanPublicKey);

			// 生成公钥对象
			X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			PublicKey pubKey = keyFactory.generatePublic(spec);

			// 创建加密器
			Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
			cipher.init(Cipher.ENCRYPT_MODE, pubKey);

			// RSA加密有长度限制，需要分块处理
			byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
			int maxEncryptBlock = 117; // RSA 1024位密钥的最大加密块大小

			if (dataBytes.length <= maxEncryptBlock) {
				// 数据较小，直接加密
				byte[] encrypted = cipher.doFinal(dataBytes);
				return Base64.getEncoder().encodeToString(encrypted);
			} else {
				// 数据较大，分块加密
				StringBuilder result = new StringBuilder();
				int offset = 0;

				while (offset < dataBytes.length) {
					int blockSize = Math.min(maxEncryptBlock, dataBytes.length - offset);
					byte[] block = new byte[blockSize];
					System.arraycopy(dataBytes, offset, block, 0, blockSize);

					byte[] encrypted = cipher.doFinal(block);
					if (result.length() > 0) {
						result.append("|"); // 使用分隔符分隔加密块
					}
					result.append(Base64.getEncoder().encodeToString(encrypted));

					offset += blockSize;
				}

				return result.toString();
			}

		} catch (Exception e) {
			throw new RuntimeException("RSA加密失败: " + e.getMessage(), e);
		}
	}

	/**
	 * MD5哈希
	 */
	private String hashMD5(String data, String salt) throws Exception {
		MessageDigest md = MessageDigest.getInstance("MD5");
		String input = StringUtil.isNotBlank(salt) ? data + salt : data;
		byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
		return bytesToHex(digest);
	}

	/**
	 * SHA1哈希
	 */
	private String hashSHA1(String data, String salt) throws Exception {
		MessageDigest md = MessageDigest.getInstance("SHA-1");
		String input = StringUtil.isNotBlank(salt) ? data + salt : data;
		byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
		return bytesToHex(digest);
	}

	/**
	 * SHA256哈希
	 */
	private String hashSHA256(String data, String salt) throws Exception {
		MessageDigest md = MessageDigest.getInstance("SHA-256");
		String input = StringUtil.isNotBlank(salt) ? data + salt : data;
		byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
		return bytesToHex(digest);
	}

	/**
	 * SHA512哈希
	 */
	private String hashSHA512(String data, String salt) throws Exception {
		MessageDigest md = MessageDigest.getInstance("SHA-512");
		String input = StringUtil.isNotBlank(salt) ? data + salt : data;
		byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
		return bytesToHex(digest);
	}

	/**
	 * 字节数组转十六进制字符串
	 */
	private String bytesToHex(byte[] bytes) {
		StringBuilder result = new StringBuilder();
		for (byte b : bytes) {
			result.append(String.format("%02x", b));
		}
		return result.toString();
	}

	/**
	 * 根据字段模式选择要加密的字段
	 */
	private Map<String, Object> selectFieldsForEncryption(Map<String, Object> data) {
		Map<String, Object> selectedData = new HashMap<>();

		switch (dataEncryptNode.getFieldMode()) {
			case "ALL":
				selectedData.putAll(data);
				break;
			case "INCLUDE":
				if (StringUtil.isNotBlank(dataEncryptNode.getSpecifiedFields())) {
					String[] fields = dataEncryptNode.getSpecifiedFields().split(",");
					for (String field : fields) {
						String trimmedField = field.trim();
						if (data.containsKey(trimmedField)) {
							selectedData.put(trimmedField, data.get(trimmedField));
						}
					}
				}
				break;
			case "EXCLUDE":
				selectedData.putAll(data);
				if (StringUtil.isNotBlank(dataEncryptNode.getSpecifiedFields())) {
					String[] fields = dataEncryptNode.getSpecifiedFields().split(",");
					for (String field : fields) {
						selectedData.remove(field.trim());
					}
				}
				break;
		}

		return selectedData;
	}

	/**
	 * 序列化数据
	 */
	private String serializeData(Map<String, Object> data) {
		switch (dataEncryptNode.getSerializationFormat()) {
			case "JSON":
				return JSONUtil.toJsonString(data);
			case "XML":
				// 简化XML序列化实现
				StringBuilder xml = new StringBuilder("<data>");
				for (Map.Entry<String, Object> entry : data.entrySet()) {
					xml.append("<").append(entry.getKey()).append(">")
							.append(entry.getValue())
							.append("</").append(entry.getKey()).append(">");
				}
				xml.append("</data>");
				return xml.toString();
			case "CSV":
				StringBuilder csv = new StringBuilder();
				for (Map.Entry<String, Object> entry : data.entrySet()) {
					if (csv.length() > 0) csv.append(",");
					csv.append(entry.getValue());
				}
				return csv.toString();
			case "DELIMITED":
				String delimiter = StringUtil.isNotBlank(dataEncryptNode.getDelimiter())
						? dataEncryptNode.getDelimiter() : ",";
				StringBuilder delimited = new StringBuilder();
				for (Map.Entry<String, Object> entry : data.entrySet()) {
					if (delimited.length() > 0) delimited.append(delimiter);
					delimited.append(entry.getValue());
				}
				return delimited.toString();
			default:
				return StringUtil.toJson(data);
		}
	}

	/**
	 * 加密行级数据
	 */
	private String encryptRowData(String data) throws Exception {
		String algorithm = dataEncryptNode.getRowEncryptAlgorithm();
		String secretKey = dataEncryptNode.getRowSecretKey();
		String salt = dataEncryptNode.getRowSalt();
		String iv = dataEncryptNode.getRowIV();

		switch (algorithm) {
			case "AES256":
			case "AES128":
				return encryptAES(data, secretKey, iv, algorithm);
			case "DES":
				return encryptDES(data, secretKey, iv);
			case "3DES":
				return encrypt3DES(data, secretKey, iv);
			case "RSA":
				return encryptRSA(data, secretKey);
			case "MD5":
				return hashMD5(data, salt);
			case "SHA1":
				return hashSHA1(data, salt);
			case "SHA256":
				return hashSHA256(data, salt);
			case "SHA512":
				return hashSHA512(data, salt);
			case "BASE64":
				return Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
			default:
				throw new RuntimeException("不支持的加密算法: " + algorithm);
		}
	}

	/**
	 * 执行自定义脚本
	 */
	private Map<String, Object> executeCustomScript(Map<String, Object> data, ScriptEngine scriptEngine) throws Exception {
		if ("JAVASCRIPT".equals(dataEncryptNode.getScriptType())) {
			return executeJavaScriptEncryption(data, scriptEngine);
		}
		// Python脚本执行暂不实现
		return data;
	}

	/**
	 * 执行JavaScript加密
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> executeJavaScriptEncryption(Map<String, Object> data, ScriptEngine scriptEngine) throws Exception {
		// 创建数据的副本，避免原数据被意外修改
		Map<String, Object> dataCopy = new HashMap<>(data);

		try {
			// 将数据转换为JSON字符串传递给JavaScript引擎（传对象js无法正确修改值）
			String jsonData = StringUtil.toJson(dataCopy);
			scriptEngine.put("recordJson", jsonData);

			// 构建包装后的JavaScript代码，自动处理JSON转换
			String wrappedJsCode = buildWrappedJavaScriptCode(dataEncryptNode.getJavascriptCode());

			// 执行包装后的JavaScript代码
			scriptEngine.eval(wrappedJsCode);

			// 调用包装后的函数并获取结果
			Object result = scriptEngine.eval("wrappedEncryptData(recordJson)");

			if (result == null) {
				return null;
			}

			// 如果返回的是JSON字符串，解析为Map
			if (result instanceof String) {
				String resultJson = (String) result;
				if ("null".equals(resultJson) || StringUtil.isBlank(resultJson)) {
					return null;
				}
				try {
					return StringUtil.fromJson(resultJson, Map.class);
				} catch (Exception e) {
					appendLog(String.format("节点【%s】解析JavaScript返回的JSON失败: %s",
							pipelineNode.getProperties().getName(), e.getMessage()));
					return dataCopy;
				}
			}

			// 如果返回的是Map类型，直接返回
			if (result instanceof Map) {
				return (Map<String, Object>) result;
			}

			// 如果都获取不到，返回原数据副本
			return dataCopy;

		} catch (Exception e) {
			// 记录JavaScript执行错误的详细信息
			appendLog(String.format("节点【%s】JavaScript加密执行错误: %s, 数据: %s",
					pipelineNode.getProperties().getName(), e.getMessage(), data.toString()));
			throw e;
		}
	}

	/**
	 * 构建包装后的JavaScript代码，自动处理JSON转换
	 */
	private String buildWrappedJavaScriptCode(String userJsCode) {
		StringBuilder wrappedCode = new StringBuilder();

		// 添加包装函数
		wrappedCode.append("function wrappedEncryptData(recordJson) {\n");
		wrappedCode.append("  // 自动解析JSON为JavaScript对象\n");
		wrappedCode.append("  var record = JSON.parse(recordJson);\n");
		wrappedCode.append("  \n");

		// 检查用户代码是否已经包含encrypt_data函数定义
		if (userJsCode.contains("function encrypt_data")) {
			// 用户代码包含完整的encrypt_data函数定义
			wrappedCode.append("  // 用户定义的encrypt_data函数\n");
			wrappedCode.append("  ").append(userJsCode).append("\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用用户的encrypt_data函数\n");
			wrappedCode.append("  var result = encrypt_data(record);\n");
		} else {
			// 用户代码是直接的处理逻辑，包装成encrypt_data函数
			wrappedCode.append("  // 包装用户的处理逻辑\n");
			wrappedCode.append("  function encrypt_data(record) {\n");
			wrappedCode.append("    ").append(userJsCode.replace("\n", "\n    ")).append("\n");
			wrappedCode.append("    return record;\n");
			wrappedCode.append("  }\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用包装后的encrypt_data函数\n");
			wrappedCode.append("  var result = encrypt_data(record);\n");
		}

		wrappedCode.append("  \n");
		wrappedCode.append("  // 自动转换结果为JSON字符串返回\n");
		wrappedCode.append("  if (result === null || result === undefined) {\n");
		wrappedCode.append("    return null;\n");
		wrappedCode.append("  }\n");
		wrappedCode.append("  return JSON.stringify(result);\n");
		wrappedCode.append("}\n");

		return wrappedCode.toString();
	}

	/**
	 * 处理错误
	 */
	private void handleError(String message, Exception e, Map<String, Object> data) {
		errorCount.incrementAndGet();
		processedCount.incrementAndGet();

		String errorMsg = String.format("%s: %s, 数据: %s", message, e.getMessage(), JSONUtil.toJsonString(data));

		switch (dataEncryptNode.getErrorHandling()) {
			case "SKIP":
				break;
			case "STOP":
				throw new RuntimeException(errorMsg, e);
			case "LOG":
				appendLog(String.format("节点【%s】记录错误，继续处理: %s", pipelineNode.getProperties().getName(), errorMsg));
				// 推送原始数据
				pushDataToDownstream(data);
				break;
		}
	}
}
