package com.bjgy.etl.engine.memory;

import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Iterator;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * ETL内存管理器 - 替代Redis的纯内存方案
 * 基于Kettle模式的内存数据流转，增强内存控制机制
 */
@Slf4j
public class EtlMemoryManager {

    private static final EtlMemoryManager INSTANCE = new EtlMemoryManager();

    // 节点数据队列 - 用于节点间数据流转
    private final Map<String, BlockingQueue<Map<String, Object>>> nodeDataQueues = new ConcurrentHashMap<>();

    // 节点状态映射
    private final Map<String, Integer> nodeStatusMap = new ConcurrentHashMap<>();

    // 流程运行状态
    private final Map<Long, Boolean> pipelineRunningMap = new ConcurrentHashMap<>();

    // 内存使用统计
    private final AtomicLong estimatedMemoryUsage = new AtomicLong(0);

    // 配置参数 - 可通过系统属性或配置文件设置
    private static final int DEFAULT_QUEUE_CAPACITY = getIntProperty("etl.memory.queue.capacity", 5000);
    private static final int MAX_PIPELINES = getIntProperty("etl.memory.max.pipelines", 50);
    private static final long MAX_MEMORY_BYTES = getLongProperty("etl.memory.max.bytes", 512 * 1024 * 1024L); // 默认512MB
    private static final double MEMORY_WARNING_THRESHOLD = getDoubleProperty("etl.memory.warning.threshold", 0.8); // 80%
    private static final double MEMORY_CRITICAL_THRESHOLD = getDoubleProperty("etl.memory.critical.threshold", 0.9); // 90%
    private static final boolean WAIT_FOR_MEMORY = getBooleanProperty("etl.memory.wait.for.memory", true); // 默认等待内存释放
    private static final int MEMORY_WAIT_TIMEOUT_SECONDS = getIntProperty("etl.memory.wait.timeout.seconds", 300); // 默认等待5分钟

    // JVM内存监控
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

    private EtlMemoryManager() {
        // 启动内存监控线程
        startMemoryMonitorThread();
    }

    public static EtlMemoryManager getInstance() {
        return INSTANCE;
    }

    /**
     * 启动流程
     */
    public synchronized void startPipeline(Long recordId) {
        // 检查流程数量限制
        if (pipelineRunningMap.size() >= MAX_PIPELINES) {
            throw new RuntimeException(String.format("超过最大流程数量限制: %d，请等待其他流程完成后再试", MAX_PIPELINES));
        }

        // 检查内存使用情况
        checkMemoryUsage("启动流程");

        pipelineRunningMap.put(recordId, true);
        log.info("Pipeline {} started, current active pipelines: {}", recordId, pipelineRunningMap.size());
    }

    /**
     * 停止流程
     */
    public void stopPipeline(Long recordId) {
        if (pipelineRunningMap.remove(recordId) != null) {
            log.info("流程已停止: {}", recordId);
        }
    }

    /**
     * 检查流程是否已停止
     */
    public boolean isPipelineStopped(Long recordId) {
        return !pipelineRunningMap.getOrDefault(recordId, false);
    }

    /**
     * 设置节点状态
     */
    public void setNodeStatus(String nodeKey, Integer status) {
        nodeStatusMap.put(nodeKey, status);
        log.debug("Node {} status set to {}", nodeKey, status);
    }

    /**
     * 获取节点状态
     */
    public Integer getNodeStatus(String nodeKey) {
        return nodeStatusMap.get(nodeKey);
    }

    /**
     * 获取所有匹配前缀的节点状态键
     */
    public Set<String> getNodeStatusKeys(String prefix) {
        return nodeStatusMap.keySet().stream()
                .filter(key -> key.startsWith(prefix))
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 初始化节点数据队列
     */
    public void initNodeDataQueue(String nodeKey) {
        nodeDataQueues.computeIfAbsent(nodeKey, k -> new LinkedBlockingQueue<>(DEFAULT_QUEUE_CAPACITY));
        log.debug("Node data queue initialized for {}", nodeKey);
    }

    /**
     * 向节点队列左端推送数据（生产者）
     * 当内存不足时，根据配置选择等待内存释放或抛异常
     */
    public boolean leftPush(String nodeKey, Map<String, Object> data) {
        BlockingQueue<Map<String, Object>> queue = nodeDataQueues.get(nodeKey);
        if (queue == null) {
            initNodeDataQueue(nodeKey);
            queue = nodeDataQueues.get(nodeKey);
        }

        // 内存检查和等待逻辑
        long startTime = System.currentTimeMillis();
        long timeoutMillis = MEMORY_WAIT_TIMEOUT_SECONDS * 1000L;

        while (!checkMemoryBeforePush()) {
            if (WAIT_FOR_MEMORY) {
                // 等待策略：循环等待内存释放
                long elapsedTime = System.currentTimeMillis() - startTime;
                if (elapsedTime > timeoutMillis) {
                    throw new RuntimeException(String.format(
                        "等待内存释放超时（%d秒），无法推送数据到队列: %s",
                        MEMORY_WAIT_TIMEOUT_SECONDS, nodeKey));
                }

                log.warn("内存使用过高，等待内存释放后再推送数据到队列: {}, 已等待: {}ms",
                        nodeKey, elapsedTime);

                // 主动触发清理
                cleanupCompletedQueues();

                try {
                    Thread.sleep(1000); // 等待1秒后重试
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待内存释放时被中断", e);
                }
            } else {
                // 异常策略：直接抛异常
                MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
                double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();
                long estimatedUsage = estimatedMemoryUsage.get();

                throw new RuntimeException(String.format(
                    "内存使用过高，拒绝推送数据到队列: %s。堆内存使用率: %.2f%%, 估算ETL内存使用: %d bytes",
                    nodeKey, heapUsageRatio * 100, estimatedUsage));
            }
        }

        try {
            // 使用offer方法，避免阻塞
            boolean result = queue.offer(data, 100, TimeUnit.MILLISECONDS);
            if (result) {
                // 计算实际数据大小并更新内存使用估算
                long actualSize = calculateDataSize(data);
                estimatedMemoryUsage.addAndGet(actualSize);
                log.debug("成功推送数据到队列: {}, 数据大小: {} bytes, 当前估算内存使用: {} bytes",
                        nodeKey, actualSize, estimatedMemoryUsage.get());
            } else {
                log.warn("队列已满，无法推送数据到队列: {}", nodeKey);
            }
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("推送数据到队列时被中断: {}", nodeKey, e);
            return false;
        }
    }

    /**
     * 从节点队列右端弹出数据（消费者）
     */
    public Map<String, Object> rightPop(String nodeKey) {
        BlockingQueue<Map<String, Object>> queue = nodeDataQueues.get(nodeKey);
        if (queue == null) {
            return null;
        }

        try {
            // 使用poll方法，避免长时间阻塞
            Map<String, Object> data = queue.poll(50, TimeUnit.MILLISECONDS);
            if (data != null) {
                // 计算实际数据大小并更新内存使用估算
                long actualSize = calculateDataSize(data);
                estimatedMemoryUsage.addAndGet(-actualSize);
                log.debug("成功从队列弹出数据: {}, 数据大小: {} bytes, 当前估算内存使用: {} bytes",
                        nodeKey, actualSize, estimatedMemoryUsage.get());
            }
            return data;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while popping data from queue {}", nodeKey, e);
            return null;
        }
    }

    /**
     * 获取队列大小
     */
    public long getQueueSize(String nodeKey) {
        BlockingQueue<Map<String, Object>> queue = nodeDataQueues.get(nodeKey);
        return queue == null ? 0 : queue.size();
    }

    /**
     * 检查队列是否存在
     */
    public boolean queueExists(String nodeKey) {
        return nodeDataQueues.containsKey(nodeKey);
    }

    /**
     * 清理指定记录的所有节点数据
     */
    public void cleanupNodeData(Long recordId) {
        String prefix = recordId.toString();

        // 计算要清理的数据总大小
        long dataToClean = 0;
        for (Map.Entry<String, BlockingQueue<Map<String, Object>>> entry : nodeDataQueues.entrySet()) {
            if (entry.getKey().contains(":" + prefix + ":")) {
                // 计算队列中所有数据的实际大小
                for (Map<String, Object> data : entry.getValue()) {
                    dataToClean += calculateDataSize(data);
                }
            }
        }

        // 清理节点状态
        nodeStatusMap.entrySet().removeIf(entry -> entry.getKey().contains(":" + prefix + ":"));

        // 清理节点数据队列
        nodeDataQueues.entrySet().removeIf(entry -> entry.getKey().contains(":" + prefix + ":"));

        // 清理流程运行状态
        pipelineRunningMap.remove(recordId);

        // 更新内存使用估算
        estimatedMemoryUsage.addAndGet(-dataToClean);

        log.info("Cleaned up all data for record {}, freed estimated {} bytes", recordId, dataToClean);
    }

    /**
     * 检查推送前的内存状态
     */
    private boolean checkMemoryBeforePush() {
        // 检查估算内存使用
        if (estimatedMemoryUsage.get() > MAX_MEMORY_BYTES * MEMORY_CRITICAL_THRESHOLD) {
            return false;
        }

        // 检查JVM堆内存使用
        MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
        double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();

        return heapUsageRatio < MEMORY_CRITICAL_THRESHOLD;
    }

    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage(String operation) {
        MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
        double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();
        long estimatedUsage = estimatedMemoryUsage.get();

        if (heapUsageRatio > MEMORY_CRITICAL_THRESHOLD || estimatedUsage > MAX_MEMORY_BYTES * MEMORY_CRITICAL_THRESHOLD) {
            throw new RuntimeException(String.format("内存使用过高，无法执行操作: %s。堆内存使用率: %.2f%%, 估算ETL内存使用: %d bytes",
                    operation, heapUsageRatio * 100, estimatedUsage));
        } else if (heapUsageRatio > MEMORY_WARNING_THRESHOLD || estimatedUsage > MAX_MEMORY_BYTES * MEMORY_WARNING_THRESHOLD) {
            log.warn("内存使用率较高 - 操作: {}, 堆内存使用率: {}%, 估算ETL内存使用: {} bytes",
                    operation, String.format("%.2f", heapUsageRatio * 100), estimatedUsage);
        }
    }

    /**
     * 启动内存监控线程
     */
    private void startMemoryMonitorThread() {
        Thread monitorThread = new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(30000); // 每30秒检查一次

                    MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
                    double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();
                    long estimatedUsage = estimatedMemoryUsage.get();

                    if (heapUsageRatio > MEMORY_WARNING_THRESHOLD || estimatedUsage > MAX_MEMORY_BYTES * MEMORY_WARNING_THRESHOLD) {
                        log.warn("内存监控告警 - 堆内存使用率: {}%, 估算ETL内存使用: {} bytes, 活跃流程数: {}, 队列数: {}",
                                String.format("%.2f", heapUsageRatio * 100), estimatedUsage, pipelineRunningMap.size(), nodeDataQueues.size());

                        // 如果内存使用过高，尝试清理一些已完成的队列
                        if (heapUsageRatio > MEMORY_CRITICAL_THRESHOLD) {
                            cleanupCompletedQueues();
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("内存监控线程异常", e);
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.setName("ETL-Memory-Monitor");
        monitorThread.start();
    }

    /**
     * 清理已完成的队列
     */
    private void cleanupCompletedQueues() {
        log.info("开始清理已完成的队列以释放内存");
        int cleanedQueues = 0;
        long freedMemory = 0;

        // 查找空队列并清理
        Iterator<Map.Entry<String, BlockingQueue<Map<String, Object>>>> iterator = nodeDataQueues.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BlockingQueue<Map<String, Object>>> entry = iterator.next();
            if (entry.getValue().isEmpty()) {
                iterator.remove();
                cleanedQueues++;
            } else {
                // 计算非空队列中数据的实际大小（仅用于统计，不进行清理）
                for (Map<String, Object> data : entry.getValue()) {
                    freedMemory += calculateDataSize(data);
                }
            }
        }

        if (cleanedQueues > 0) {
            log.info("清理了 {} 个空队列", cleanedQueues);
        }
    }

    /**
     * 获取内存使用统计信息
     */
    public MemoryStats getMemoryStats() {
        int totalQueues = nodeDataQueues.size();
        long totalQueueSize = nodeDataQueues.values().stream()
                .mapToLong(BlockingQueue::size)
                .sum();
        int totalNodeStatus = nodeStatusMap.size();
        int activePipelines = pipelineRunningMap.size();
        long estimatedMemory = estimatedMemoryUsage.get();

        MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
        double heapUsageRatio = (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax();

        return new MemoryStats(totalQueues, totalQueueSize, totalNodeStatus, activePipelines,
                estimatedMemory, heapUsageRatio, heapMemoryUsage.getUsed(), heapMemoryUsage.getMax());
    }

    // 辅助方法：获取系统属性配置
    private static int getIntProperty(String key, int defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private static long getLongProperty(String key, long defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Long.parseLong(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private static double getDoubleProperty(String key, double defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Double.parseDouble(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private static boolean getBooleanProperty(String key, boolean defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Boolean.parseBoolean(value) : defaultValue;
        } catch (IllegalArgumentException e) {
            return defaultValue;
        }
    }

    /**
     * 内存统计信息
     */
    public static class MemoryStats {
        private final int totalQueues;
        private final long totalQueueSize;
        private final int totalNodeStatus;
        private final int activePipelines;
        private final long estimatedMemoryUsage;
        private final double heapUsageRatio;
        private final long heapUsed;
        private final long heapMax;

        public MemoryStats(int totalQueues, long totalQueueSize, int totalNodeStatus, int activePipelines,
                          long estimatedMemoryUsage, double heapUsageRatio, long heapUsed, long heapMax) {
            this.totalQueues = totalQueues;
            this.totalQueueSize = totalQueueSize;
            this.totalNodeStatus = totalNodeStatus;
            this.activePipelines = activePipelines;
            this.estimatedMemoryUsage = estimatedMemoryUsage;
            this.heapUsageRatio = heapUsageRatio;
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
        }

        public int getTotalQueues() { return totalQueues; }
        public long getTotalQueueSize() { return totalQueueSize; }
        public int getTotalNodeStatus() { return totalNodeStatus; }
        public int getActivePipelines() { return activePipelines; }
        public long getEstimatedMemoryUsage() { return estimatedMemoryUsage; }
        public double getHeapUsageRatio() { return heapUsageRatio; }
        public long getHeapUsed() { return heapUsed; }
        public long getHeapMax() { return heapMax; }

        @Override
        public String toString() {
            return String.format("MemoryStats{queues=%d, queueSize=%d, nodeStatus=%d, pipelines=%d, " +
                    "estimatedMemory=%d bytes, heapUsage=%.2f%% (%d/%d bytes)}",
                    totalQueues, totalQueueSize, totalNodeStatus, activePipelines,
                    estimatedMemoryUsage, heapUsageRatio * 100, heapUsed, heapMax);
        }
    }

    /**
     * 计算Map数据的近似内存大小（字节）
     * 这是一个简化的计算方法，提供相对准确的内存使用估算
     */
    private long calculateDataSize(Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return 32; // 空Map的基本开销
        }

        long totalSize = 48; // Map基本对象开销（HashMap）

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            // 计算key的大小
            String key = entry.getKey();
            if (key != null) {
                totalSize += 40 + (key.length() * 2); // String对象开销 + 字符数据
            } else {
                totalSize += 8; // null引用
            }

            // 计算value的大小
            Object value = entry.getValue();
            totalSize += calculateObjectSize(value);

            // Map.Entry开销
            totalSize += 24;
        }

        return totalSize;
    }

    /**
     * 计算单个对象的近似内存大小
     */
    private long calculateObjectSize(Object obj) {
        if (obj == null) {
            return 8; // null引用
        }

        // 基本类型包装类
        if (obj instanceof Integer) {
            return 16;
        } else if (obj instanceof Long) {
            return 24;
        } else if (obj instanceof Double) {
            return 24;
        } else if (obj instanceof Float) {
            return 16;
        } else if (obj instanceof Boolean) {
            return 16;
        } else if (obj instanceof Byte) {
            return 16;
        } else if (obj instanceof Short) {
            return 16;
        } else if (obj instanceof Character) {
            return 16;
        }
        // String类型
        else if (obj instanceof String) {
            String str = (String) obj;
            return 40 + (str.length() * 2); // String对象开销 + 字符数据
        }
        // BigDecimal
        else if (obj instanceof BigDecimal) {
            BigDecimal bd = (BigDecimal) obj;
            return 72 + calculateObjectSize(bd.toString()); // BigDecimal开销 + 字符串表示
        }
        // BigInteger
        else if (obj instanceof BigInteger) {
            BigInteger bi = (BigInteger) obj;
            return 48 + (bi.bitLength() / 8) + 8; // BigInteger开销 + 位数据
        }
        // 日期类型
        else if (obj instanceof java.util.Date) {
            return 32;
        } else if (obj instanceof java.sql.Date) {
            return 32;
        } else if (obj instanceof java.sql.Timestamp) {
            return 40;
        }
        // 字节数组
        else if (obj instanceof byte[]) {
            byte[] bytes = (byte[]) obj;
            return 24 + bytes.length; // 数组对象开销 + 数据
        }
        // 其他数组类型
        else if (obj.getClass().isArray()) {
            return 24 + (java.lang.reflect.Array.getLength(obj) * 8); // 估算数组大小
        }
        // 集合类型（简化处理）
        else if (obj instanceof java.util.Collection) {
            java.util.Collection<?> collection = (java.util.Collection<?>) obj;
            long size = 48; // 集合基本开销
            for (Object item : collection) {
                size += calculateObjectSize(item);
            }
            return size;
        }
        // 其他对象类型（估算）
        else {
            return 64; // 一般对象的估算大小
        }
    }
}
