package com.bjgy.etl.node.load;

import lombok.Data;

import java.util.List;

/**
 * @ClassName 写入数据库表
 */
@Data
public class InsertDbNode {
	private String dependNodeNo;
	private Integer dbType;
	private Long databaseId;
	private String tableName;
	//1-仅插入 2-更新及插入（需设置主键） 3-仅更新（需设置主键）
	private Integer dataSyncType;
	//是否忽略错误 0-否 1-是
	private Integer ignoreEx;
	//批量映射（适用于所有表的映射规则一样）
	private List<String> sourcePrimaryKeys;
	private Boolean targetTruncate = Boolean.TRUE;
	private List<String> updateColumns;
	//先不考虑增量，只做主键
	/*private String increaseColumnName;
	private ColumnDescription increaseColumn;*/
	/*private List<PatternMapper> regexColumnMapper;*/
}
