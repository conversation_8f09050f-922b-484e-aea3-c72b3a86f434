 /*
  * Copyright (c) 2024 天津数睿通科技有限公司
  * All rights reserved.
  */
 package com.bjgy.etl.node.transform;

 import lombok.Data;

 import java.util.List;

 /**
  * 数据加密节点配置类
  *
  * @ClassName DataEncryptNode
  */
 @Data
 public class DataEncryptNode {

	 // 基本配置
	 private String dependNodeNo;                    // 数据源节点编号
	 private String encryptMode;                     // 加密模式：FIELD_LEVEL/ROW_LEVEL/CUSTOM

	 // 字段级加密配置
	 private List<EncryptionField> encryptFields;    // 加密字段配置列表

	 // 行级加密配置
	 private String rowEncryptAlgorithm;             // 行级加密算法
	 private String rowSecretKey;                    // 行级加密密钥
	 private String rowSalt;                         // 行级加密盐值
	 private String rowIV;                           // 行级加密初始化向量
	 private String serializationFormat;             // 数据序列化格式：JSON/XML/CSV/DELIMITED
	 private String delimiter;                       // 分隔符（用于DELIMITED格式）
	 private String rowOutputFieldName;              // 行级加密输出字段名
	 private Boolean keepOriginalFields;             // 保留原始字段
	 private String fieldMode;                       // 字段模式：ALL/INCLUDE/EXCLUDE
	 private String specifiedFields;                 // 指定字段列表

	 // 自定义加密配置
	 private String scriptType;                      // 脚本类型：JAVASCRIPT/PYTHON
	 private String javascriptCode;                  // JavaScript代码
	 private String pythonCode;                      // Python代码

	 // 性能优化配置
	 private Integer batchSize;                      // 批处理大小
	 private Boolean enableParallelProcessing;       // 启用并行处理
	 private Boolean enableMemoryOptimization;       // 启用内存优化

	 // 安全配置
	 private String encryptionStrength;              // 加密强度：STANDARD/HIGH/ULTRA
	 private Boolean enableIntegrityCheck;           // 启用数据完整性校验

	 // 错误处理
	 private String errorHandling;                   // 错误处理策略：SKIP/STOP/LOG

	 // 输出配置
	 private Boolean outputStatistics;               // 输出加密统计信息
	 private Boolean keepMetadata;                   // 保留元数据信息

	 /**
	  * 加密字段配置
	  */
	 @Data
	 public static class EncryptionField {
		 private String fieldName;                   // 字段名
		 private String algorithm;                   // 加密算法：AES256/AES128/DES/3DES/RSA/MD5/SHA1/SHA256/SHA512/BASE64
		 private String secretKey;                   // 密钥/公钥
		 private String salt;                        // 盐值
		 private String iv;                          // 初始化向量
		 private String outputFieldName;             // 输出字段名
		 private Boolean keepOriginal;               // 保留原值
	 }
 }
