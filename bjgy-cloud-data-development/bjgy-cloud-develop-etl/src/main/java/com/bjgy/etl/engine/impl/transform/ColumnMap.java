package com.bjgy.etl.engine.impl.transform;

import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.transform.ColumnMapNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import bjgy.cloud.framework.dbswitch.common.entity.PatternMapper;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ReadDb
 */
public class ColumnMap extends EtlEngine {

	public ColumnMap(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	private String sourceDataKey;
	private String sourceDataStatusKey;
	private long totalCount = 0;
	private Map<String, String> columnMap = new HashMap<>();
	private final String column_deleted = "<column_deleted>";


	@Override
	public void run() {
		//初始化缓存
		initCache();
		long startTime = System.currentTimeMillis();
		try {
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			ColumnMapNode columnMapNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, ColumnMapNode.class);
			List<PatternMapper> regexColumnMapper = columnMapNode.getRegexColumnMapper();
			//获取数据节点
			String sourceNodeNo = columnMapNode.getDependNodeNo();
			waitForDependNode(sourceNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));
			sourceDataKey = getSourceDataKey(sourceNodeNo);
			sourceDataStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + sourceNodeNo;

			for (PatternMapper patternMapper : regexColumnMapper) {
				columnMap.put(patternMapper.getFromPattern(), StringUtil.isBlank(patternMapper.getToValue()) ? column_deleted : patternMapper.getToValue());
			}
			while (true) {
				dependFailedCheck(sourceNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}
				try {
					long currentTime = System.currentTimeMillis();
					if (checkOverTime(startTime, currentTime, overTimes)) {
						appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
						appendLog(String.format("节点 【%s】 运行超时，已映射数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
						//更新节点日志
						updateLog();
						break;
					}
					mapAndpushData();
					//判断节点状态
					Integer sourceDataStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
					//如果源节点执行完毕，退出
					if (CommonRunStatus.isSuccess(sourceDataStatus)) {
						//获取剩余个数
						long dealSize = memoryManager.getQueueSize(sourceDataKey);
						for (int i = 0; i < dealSize; i++) {
							mapAndpushData();
						}
						break;
					}
					ThreadUtil.sleep(20);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}
			appendLog(String.format("节点 【%s】 运行成功结束，已映射数据总数：%s", pipelineNode.getProperties().getName(), totalCount));
			//成功结束
			successEnd();
		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，已映射数据总数：%s，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			//失败结束
			failEnd();
		}
	}

	private void mapAndpushData() {
		Map<String, Object> dataMap = memoryManager.rightPop(sourceDataKey);
		if (dataMap != null) {
			Map<String, Object> targetData = buildTargetData(dataMap);
			// 推送数据到下游队列（使用数据复制机制）
			pushDataToDownstream(targetData);
			totalCount += 1;

			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}
		}
	}


	private Map<String, Object> buildTargetData(Map<String, Object> dataMap) {
		Map<String, Object> targetData = new HashMap<>();
		for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
			String key = entry.getKey();
			Object value = entry.getValue();
			if (!column_deleted.equals(columnMap.get(key))) {
				targetData.put(columnMap.getOrDefault(key, key), value);
			}
		}
		return targetData;
	}
}
