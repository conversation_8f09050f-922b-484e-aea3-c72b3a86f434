/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.node.transform;

import lombok.Data;

import java.util.List;

/**
 * 数据脱敏节点配置
 */
@Data
public class DataDesensitizeNode {

    /**
     * 依赖节点编号
     */
    private String dependNodeNo;

    /**
     * 脱敏模式：FIELD_LEVEL-字段级脱敏，ROW_LEVEL-行级脱敏，CUSTOM-自定义脱敏
     */
    private String desensitizeMode;

    /**
     * 字段级脱敏配置
     */
    private List<DesensitizeField> desensitizeFields;

    /**
     * 行级脱敏策略
     */
    private String rowDesensitizeStrategy;

    /**
     * 关键字段列表（逗号分隔）
     */
    private String keyFields;

    /**
     * 行级脱敏遮盖字符
     */
    private String rowMaskChar;

    /**
     * 泛化级别：LOW-低级别，MEDIUM-中级别，HIGH-高级别
     */
    private String generalizationLevel;

    /**
     * 自定义脱敏脚本
     */
    private String customScript;

    /**
     * 是否启用性能优化
     */
    private Boolean enablePerformanceOptimization;

    /**
     * 批处理大小
     */
    private Integer batchSize;

    /**
     * 是否启用并行处理
     */
    private Boolean parallelProcessing;

    /**
     * 线程数量
     */
    private Integer threadCount;

    /**
     * 错误处理策略：SKIP-跳过错误记录，STOP-遇错停止处理，LOG-记录错误继续
     */
    private String errorHandlingStrategy;

    /**
     * 脱敏字段配置类
     */
    @Data
    public static class DesensitizeField {
        /**
         * 字段名
         */
        private String fieldName;

        /**
         * 脱敏策略
         */
        private String strategy;

        /**
         * 遮盖字符
         */
        private String maskChar;

        /**
         * 保留开头字符数
         */
        private String keepStart;

        /**
         * 保留结尾字符数
         */
        private String keepEnd;

        /**
         * 替换值
         */
        private String replaceValue;

        /**
         * 截断长度
         */
        private String truncateLength;

        /**
         * 截断位置：START-保留开头，END-保留结尾
         */
        private String truncatePosition;

        /**
         * 随机类型：NUMBER-随机数字，LETTER-随机字母，CHAR-随机字符，DATE-随机日期
         */
        private String randomType;

        /**
         * 金额脱敏类型：RANGE-区间模糊，PARTIAL_MASK-部分遮盖，SCALE-比例缩放
         */
        private String amountType;

        /**
         * 输出字段名
         */
        private String outputFieldName;

        /**
         * 是否保留原值
         */
        private Boolean keepOriginal;
    }
}
