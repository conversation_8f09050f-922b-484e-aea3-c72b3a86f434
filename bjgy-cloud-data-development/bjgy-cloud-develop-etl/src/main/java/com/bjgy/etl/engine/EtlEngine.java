package com.bjgy.etl.engine;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.governance.constant.DbType;
import com.bjgy.api.module.data.integrate.DataDatabaseApi;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.dao.DataProdPipelineDao;
import com.bjgy.dao.DataProdPipelineNodeDao;
import com.bjgy.dao.DataProdPipelineNodeRecordDao;
import com.bjgy.dao.DataProdPipelineRecordDao;
import com.bjgy.dto.Pipeline;
import com.bjgy.dto.PipelineEdge;
import com.bjgy.dto.PipelineNode;
import com.bjgy.dto.PipelineNodeProperties;
import com.bjgy.entity.DataProdPipelineEntity;
import com.bjgy.entity.DataProdPipelineNodeEntity;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.etl.engine.memory.EtlMemoryManager;
import com.bjgy.flink.common.context.SpringContextUtils;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.flink.common.utils.ThreadUtil;
import com.bjgy.framework.common.cache.bean.DataProjectCacheBean;
import com.bjgy.framework.common.utils.DateUtils;
import com.bjgy.framework.security.cache.TokenStoreCache;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.DbswitchStrUtils;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;
import bjgy.cloud.framework.dbswitch.common.util.TypeConvertUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName EtlEngine
 */
@Slf4j
public abstract class EtlEngine {

	protected TokenStoreCache storeCache;
	protected PipelineNode pipelineNode;
	protected String nodeJson;

	// 内存管理器实例
	protected final EtlMemoryManager memoryManager = EtlMemoryManager.getInstance();

	protected final static String NODE_STATUS_KEY_PREFIX = "etl:node:status:";
	protected final static String NODE_DATA_KEY_PREFIX = "etl:node:data:";

	//节点状态key
	protected String NODE_STATUS_KEY = "etl:node:status:";
	//节点数据key
	protected String NODE_DATA_KEY = "etl:node:data:";

	protected DataProdPipelineDao pipelineDao;
	protected DataProdPipelineNodeDao pipelineNodeDao;
	protected DataProdPipelineNodeRecordDao nodeRecordDao;
	protected DataProdPipelineRecordDao recordDao;
	protected DataDatabaseApi databaseApi;

	protected List<PipelineNode> nodeList;
	protected DataProdPipelineEntity pipelineEntity;
	protected DataProdPipelineNodeEntity pipelineNodeEntity;
	protected DataProdPipelineNodeRecordEntity nodeRecordEntity;
	protected DataProdPipelineRecordEntity recordEntity;
	protected final static Map<Long, StringBuffer> recordLogMap = new ConcurrentHashMap<>();
	// 为每个记录ID创建独立的锁对象，确保日志操作的线程安全
	private final static Map<Long, Object> recordLogLocks = new ConcurrentHashMap<>();
	protected final StringBuilder nodeRecordLog = new StringBuilder();

	// 数据复制机制相关属性
	protected List<String> downstreamNodeIds = new ArrayList<>();
	protected Map<String, String> downstreamDataKeys = new HashMap<>();

	public EtlEngine(PipelineNode pipelineNode) {
		this.pipelineNode = pipelineNode;
		nodeJson = StringUtil.toJson(pipelineNode.getProperties().getNodeJson());
		storeCache = SpringContextUtils.getBeanByClass(TokenStoreCache.class);
		pipelineDao = SpringContextUtils.getBeanByClass(DataProdPipelineDao.class);
		pipelineNodeDao = SpringContextUtils.getBeanByClass(DataProdPipelineNodeDao.class);
		nodeRecordDao = SpringContextUtils.getBeanByClass(DataProdPipelineNodeRecordDao.class);
		recordDao = SpringContextUtils.getBeanByClass(DataProdPipelineRecordDao.class);
		databaseApi = SpringContextUtils.getBeanByClass(DataDatabaseApi.class);
		nodeRecordEntity = nodeRecordDao.selectById(pipelineNode.getProperties().getNodeRecordId());
		recordEntity = recordDao.selectById(pipelineNode.getProperties().getRecordId());
		recordLogMap.putIfAbsent(recordEntity.getId(), new StringBuffer());
		pipelineEntity = pipelineDao.selectById(nodeRecordEntity.getPipelineId());
		pipelineNodeEntity = pipelineNodeDao.selectById(nodeRecordEntity.getPipelineNodeId());
	}

	public static EtlEngine builld() {
		return new EtlEngine() {
			@Override
			public void run() {

			}
		};
	}

	private EtlEngine() {
		// 移除Redis初始化
	}


	public void start(Long recordId) {
		memoryManager.startPipeline(recordId);
	}

	public void stop(Long recordId) {
		memoryManager.stopPipeline(recordId);
	}

	public boolean isStop() {
		return memoryManager.isPipelineStopped(recordEntity.getId());
	}

	public void setPipelineList(List<PipelineNode> nodeList) {
		this.nodeList = nodeList;
	}


	public void removeKey(Long id) {
		memoryManager.cleanupNodeData(id);
	}


	protected void dependFailedCheck(String dependNodeNo) {
		if (StringUtil.isBlank(dependNodeNo)) {
			return;
		}
		String dependNodeStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dependNodeNo;
		Integer dependNodeStatus = memoryManager.getNodeStatus(dependNodeStatusKey);
		if (CommonRunStatus.isFailed(dependNodeStatus)) {
			throw new RuntimeException(String.format("节点【%s】的依赖节点【%s】运行失败，停止运行当前节点", pipelineNode.getProperties().getName(), dependNodeNo));
		}
	}

	/**
	 * 等待依赖节点
	 * 根据依赖节点类型决定等待策略：
	 * - extract(1)和transform(2)类型：等待开始运行即可
	 * - load(3)类型：等待执行完毕
	 */
	protected void waitForDependNode(String dependNodeNo) {
		if (StringUtil.isBlank(dependNodeNo)) {
			return;
		}

		// 获取依赖节点信息
		DataProdPipelineNodeEntity dependNode = pipelineNodeDao.selectOne(
				Wrappers.lambdaQuery(DataProdPipelineNodeEntity.class)
						.eq(DataProdPipelineNodeEntity::getNo, dependNodeNo)
		);

		if (dependNode == null) {
			throw new RuntimeException(String.format("节点【%s】的依赖节点不存在: %s", pipelineNode.getProperties().getName(), dependNodeNo));
		}

		String dependNodeStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dependNodeNo;
		Integer dependNodeToolType = dependNode.getToolType();

		// 根据依赖节点类型决定等待策略
		if (dependNodeToolType == 1 || dependNodeToolType == 2) {
			// extract(1)和transform(2)类型：等待开始运行即可
			appendLog(String.format("节点【%s】等待依赖节点【%s】开始运行 (toolType: %d)", pipelineNode.getProperties().getName(), dependNode.getName(), dependNodeToolType));
			while (true) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点【%s】被停止运行", pipelineNode.getProperties().getName()));
				}
				Integer dependNodeStatus = memoryManager.getNodeStatus(dependNodeStatusKey);
				if (CommonRunStatus.isRunning(dependNodeStatus) || CommonRunStatus.isSuccess(dependNodeStatus)) {
					appendLog(String.format("节点【%s】的依赖节点【%s】已开始运行，当前节点开始执行", pipelineNode.getProperties().getName(), dependNode.getName()));
					break;
				}
				ThreadUtil.sleep(200);
			}
		} else if (dependNodeToolType == 3) {
			// load(3)类型：等待执行完毕
			appendLog(String.format("节点【%s】等待依赖节点【%s】执行完毕 (toolType: %d)", pipelineNode.getProperties().getName(), dependNode.getName(), dependNodeToolType));
			while (true) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点【%s】被停止运行", pipelineNode.getProperties().getName()));
				}
				Integer dependNodeStatus = memoryManager.getNodeStatus(dependNodeStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					appendLog(String.format("节点【%s】的依赖节点【%s】已执行完毕 (状态: %d)，当前节点开始执行", pipelineNode.getProperties().getName(), dependNode.getName(), dependNodeStatus));
					break;
				}
				ThreadUtil.sleep(200);
			}
		} else {
			// 其他类型或未知类型：默认等待开始运行
			appendLog(String.format("节点【%s】的依赖节点【%s】类型未知 (toolType: %d)，采用默认等待策略", pipelineNode.getProperties().getName(), dependNode.getName(), dependNodeToolType));
			while (true) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点【%s】被停止运行", pipelineNode.getProperties().getName()));
				}
				Integer dependNodeStatus = memoryManager.getNodeStatus(dependNodeStatusKey);
				if (CommonRunStatus.isRunning(dependNodeStatus) || CommonRunStatus.isSuccess(dependNodeStatus)) {
					appendLog(String.format("节点【%s】的依赖节点【%s】已开始运行，当前节点开始执行", pipelineNode.getProperties().getName(), dependNode.getName()));
					break;
				}
				ThreadUtil.sleep(200);
			}
		}
	}

	/**
	 * 获取消费者节点应该使用的数据队列key
	 * 支持数据复制机制：如果上游节点启用了数据复制，则使用专属队列；否则使用原有队列
	 *
	 * @param sourceNodeNo 源节点编号
	 * @return 数据队列key
	 */
	protected String getSourceDataKey(String sourceNodeNo) {
		// 检查上游节点是否启用了数据复制机制
		String currentNodeId = pipelineNode.getId();
		Long recordId = pipelineNode.getProperties().getRecordId();

		// 尝试使用数据复制机制的专属队列
		String replicationDataKey = getConsumerDataKey(sourceNodeNo, currentNodeId, recordId);

		// 检查专属队列是否存在
		if (memoryManager.queueExists(replicationDataKey)) {
			return replicationDataKey;
		} else {
			// 使用原有的单一队列
			return NODE_DATA_KEY_PREFIX + recordId + ":" + sourceNodeNo;
		}
	}

	/**
	 * 跑节点
	 */
	public abstract void run();


	public void initCache() {
		NODE_STATUS_KEY = NODE_STATUS_KEY + pipelineNode.getProperties().getRecordId() + ":" + pipelineNode.getId();
		NODE_DATA_KEY = NODE_DATA_KEY + pipelineNode.getProperties().getRecordId() + ":" + pipelineNode.getId();
		// 初始化数据复制机制
		initDataReplicationMechanism();
		memoryManager.setNodeStatus(NODE_STATUS_KEY, CommonRunStatus.RUNNING.getCode());
	}

	/**
	 * 初始化数据复制机制
	 * 分析当前节点的下游节点，为每个下游节点创建独立的数据队列
	 */
	private void initDataReplicationMechanism() {
		// 获取下游节点ID列表
		downstreamNodeIds = getDownstreamNodeIds();

		if (downstreamNodeIds.isEmpty()) {
			// 如果没有下游节点，使用原有的单一队列
			memoryManager.initNodeDataQueue(NODE_DATA_KEY);
		} else {
			// 为每个下游节点创建独立的数据队列
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = NODE_DATA_KEY + ":" + downstreamNodeId;
				downstreamDataKeys.put(downstreamNodeId, dataKey);
				memoryManager.initNodeDataQueue(dataKey);
			}
		}
	}

	/**
	 * 获取当前节点的下游节点ID列表
	 *
	 * @return 下游节点ID列表
	 */
	private List<String> getDownstreamNodeIds() {
		List<String> downstreamIds = new ArrayList<>();

		try {
			LambdaQueryWrapper<DataProdPipelineNodeEntity> wrapper = Wrappers.lambdaQuery();
			wrapper.like(DataProdPipelineNodeEntity::getDependNo, pipelineNode.getId());
			List<DataProdPipelineNodeEntity> nodeEntities = pipelineNodeDao.selectList(wrapper);
			downstreamIds = nodeEntities.stream().map(DataProdPipelineNodeEntity::getNo).collect(Collectors.toList());
			// 解析edges JSON字符串(不合理，依赖节点是用户指定的)
			/*String edgesJson = pipelineEntity.getEdges();
			if (StringUtil.isNotBlank(edgesJson)) {
				List<PipelineEdge> edges = JSONUtil.parseObject(edgesJson, new TypeReference<List<PipelineEdge>>() {});
				// 查找以当前节点为源节点的边
				String currentNodeId = pipelineNode.getId();
				assert edges != null;
				for (PipelineEdge edge : edges) {
					if (currentNodeId.equals(edge.getSourceNodeId())) {
						downstreamIds.add(edge.getTargetNodeId());
					}
				}
			}*/
		} catch (Exception e) {
			log.warn("获取pipeline下游节点失败，使用默认数据队列机制: {}", e.getMessage());
		}

		return downstreamIds;
	}

	/**
	 * 推送数据到下游节点队列（数据复制机制）
	 * 如果有多个下游节点，会将数据复制到每个下游节点的专属队列中
	 * 如果没有下游节点，使用原有的单一队列
	 *
	 * @param data 要推送的数据
	 */
	protected void pushDataToDownstream(Map<String, Object> data) {
		if (downstreamNodeIds.isEmpty()) {
			// 没有下游节点，使用原有的单一队列
			memoryManager.leftPush(NODE_DATA_KEY, data);
		} else {
			// 有下游节点，复制数据到每个下游节点的专属队列
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = downstreamDataKeys.get(downstreamNodeId);
				if (dataKey != null) {
					// 复制数据对象，避免并发修改问题
					Map<String, Object> dataCopy = new HashMap<>(data);
					memoryManager.leftPush(dataKey, dataCopy);
				}
			}
		}
	}

	/**
	 * 批量推送数据到下游节点队列（数据复制机制）
	 *
	 * @param dataList 要推送的数据列表
	 */
	protected void pushDataListToDownstream(List<Map<String, Object>> dataList) {
		if (dataList == null || dataList.isEmpty()) {
			return;
		}

		if (downstreamNodeIds.isEmpty()) {
			// 没有下游节点，使用原有的单一队列
			for (Map<String, Object> data : dataList) {
				memoryManager.leftPush(NODE_DATA_KEY, data);
			}
		} else {
			// 有下游节点，复制数据到每个下游节点的专属队列
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = downstreamDataKeys.get(downstreamNodeId);
				if (dataKey != null) {
					for (Map<String, Object> data : dataList) {
						// 复制数据对象，避免并发修改问题
						Map<String, Object> dataCopy = new HashMap<>(data);
						memoryManager.leftPush(dataKey, dataCopy);
					}
				}
			}
		}
	}

	/**
	 * 获取当前节点对应的数据队列key
	 * 用于下游节点消费数据时确定正确的队列
	 *
	 * @param sourceNodeId  源节点ID
	 * @param currentNodeId 当前节点ID（消费者节点ID）
	 * @param recordId      记录ID
	 * @return 数据队列key
	 */
	protected String getConsumerDataKey(String sourceNodeId, String currentNodeId, Long recordId) {
		// 构造消费者应该使用的数据队列key
		return NODE_DATA_KEY_PREFIX + recordId + ":" + sourceNodeId + ":" + currentNodeId;
	}

	/**
	 * 检查是否启用了数据复制机制
	 *
	 * @return 如果有多个下游节点则返回true，否则返回false
	 */
	protected boolean isDataReplicationEnabled() {
		return !downstreamNodeIds.isEmpty();
	}

	/**
	 * 获取下游节点数量
	 *
	 * @return 下游节点数量
	 */
	protected int getDownstreamNodeCount() {
		return downstreamNodeIds.size();
	}

	protected void successEnd() {
		pipelineNode.getProperties().setRunStatus(CommonRunStatus.SUCCESS.getCode());
		pipelineNode.getProperties().setStyle(PipelineNodeProperties.SUCCESS_STYLE);
		nodeRecordEntity.setRunStatus(CommonRunStatus.SUCCESS.getCode());
		nodeRecordEntity.setEndTime(new Date());
		memoryManager.setNodeStatus(NODE_STATUS_KEY, CommonRunStatus.SUCCESS.getCode());
		updateLog();
		allEnd();
	}

	protected void failEnd() {
		pipelineNode.getProperties().setRunStatus(CommonRunStatus.FAILED.getCode());
		pipelineNode.getProperties().setStyle(PipelineNodeProperties.FALIE_STYLE);
		nodeRecordEntity.setRunStatus(CommonRunStatus.FAILED.getCode());
		nodeRecordEntity.setEndTime(new Date());
		memoryManager.setNodeStatus(NODE_STATUS_KEY, CommonRunStatus.FAILED.getCode());
		updateLog();
		allEnd();
	}

	protected void allEnd() {
		if (pipelineNode.getProperties().isIfEnd()) {
			while (true) {
				//判断是否都已结束
				Set<String> keys = memoryManager.getNodeStatusKeys(NODE_STATUS_KEY_PREFIX + recordEntity.getId());
				boolean end = true;
				boolean hasFailed = false;
				for (String key : keys) {
					Integer code = memoryManager.getNodeStatus(key);
					if (CommonRunStatus.FAILED.getCode().equals(code)) {
						hasFailed = true;
					}
					if (!CommonRunStatus.SUCCESS.getCode().equals(code) && !CommonRunStatus.FAILED.getCode().equals(code)) {
						end = false;
					}
				}
				if (end) {
					Long recordId = recordEntity.getId();
					// 使用独立锁确保日志读取操作的原子性
					String finalLog;
					synchronized (recordLogLocks.computeIfAbsent(recordId, k -> new Object())) {
						finalLog = recordLogMap.get(recordId).toString();
					}
					recordEntity.setLog(finalLog);
					recordEntity.setEndTime(new Date());
					recordEntity.setRunStatus(hasFailed ? CommonRunStatus.FAILED.getCode() : CommonRunStatus.SUCCESS.getCode());
					Pipeline pipeline = new Pipeline();
					pipeline.setId(pipelineEntity.getId());
					pipeline.setTaskType(pipelineEntity.getTaskType());
					pipeline.setRecordId(recordEntity.getId());
					pipeline.setName(pipelineEntity.getName());
					pipeline.setDescription(pipelineEntity.getDescription());
					pipeline.setCron(pipelineEntity.getCron());
					pipeline.setNodes(nodeList);
					pipeline.setEdges(JSONUtil.parseObject(pipelineEntity.getEdges(), new TypeReference<List<PipelineEdge>>() {
					}));
					recordEntity.setConfigJson(JSONUtil.toJsonString(pipeline));
					recordDao.updateById(recordEntity);
					removeKey(recordEntity.getId());
					recordLogMap.remove(recordId);
					// 清理对应的锁对象，避免内存泄漏
					recordLogLocks.remove(recordId);
					stop(recordEntity.getId());
					break;
				}
			}

		}
	}

	protected void appendLog(String logStr) {
		Long recordId = recordEntity.getId();
		// 使用独立锁确保日志操作的原子性
		synchronized (recordLogLocks.computeIfAbsent(recordId, k -> new Object())) {
			recordLogMap.get(recordId).append(DateUtils.formatDateTime(new Date())).append(" ").append(logStr).append("\r\n");
		}
		nodeRecordLog.append(DateUtils.formatDateTime(new Date())).append(" ").append(logStr).append("\r\n");
	}

	protected void updateLog() {
		Long recordId = recordEntity.getId();
		nodeRecordEntity.setLog(nodeRecordLog.toString());
		// 使用独立锁确保日志读取操作的原子性
		String recordLog;
		synchronized (recordLogLocks.computeIfAbsent(recordId, k -> new Object())) {
			recordLog = recordLogMap.get(recordId).toString();
		}
		recordEntity.setLog(recordLog);
		recordDao.updateById(recordEntity);
		nodeRecordDao.updateById(nodeRecordEntity);
	}

	protected DataDatabaseDto buildDatabase(Integer dbType, Long databaseId) {
		DataDatabaseDto dataDatabaseDto = new DataDatabaseDto();
		if (DbType.MIDDLE_DB.getValue().equals(dbType)) {
			DataProjectCacheBean project = storeCache.getProject(recordEntity.getProjectId());
			dataDatabaseDto.setDatabaseType(project.getDbType());
			dataDatabaseDto.setJdbcUrl(project.getDbUrl());
			dataDatabaseDto.setDatabasePort(project.getDbPort());
			dataDatabaseDto.setDatabaseIp(project.getDbIp());
			dataDatabaseDto.setDatabaseSchema(project.getDbSchema());
			dataDatabaseDto.setDatabaseName(project.getDbName());
			dataDatabaseDto.setUserName(project.getDbUsername());
			dataDatabaseDto.setPassword(project.getDbPassword());
		} else {
			dataDatabaseDto = databaseApi.getById(databaseId).getData();
		}
		return dataDatabaseDto;
	}

	protected HikariDataSource createDataSource(DataDatabaseDto databaseDto) {
		ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(databaseDto.getDatabaseType());
		HikariDataSource ds = new HikariDataSource();
		ds.setJdbcUrl(databaseDto.getJdbcUrl());
		ds.setDriverClassName(productTypeEnum.getDriveClassName());
		ds.setUsername(databaseDto.getUserName());
		ds.setPassword(databaseDto.getPassword());
		if (ds.getDriverClassName().contains("oracle")) {
			ds.setConnectionTestQuery("SELECT 'Hello' from DUAL");
			System.getProperties().setProperty("oracle.jdbc.J2EE13Compliant", "true");
		} else if (ds.getDriverClassName().contains("db2")) {
			ds.setConnectionTestQuery("SELECT 1 FROM SYSIBM.SYSDUMMY1");
		} else {
			ds.setConnectionTestQuery("SELECT 1");
		}
		ds.setMaximumPoolSize(8);
		ds.setMinimumIdle(5);
		ds.setMaxLifetime(TimeUnit.MINUTES.toMillis(60));
		ds.setConnectionTimeout(TimeUnit.MINUTES.toMillis(60));
		ds.setIdleTimeout(60000);

		return ds;
	}


	protected Map<String, Object> buildRowMap(List<String> columns, ResultSet rs) throws SQLException {
		Map<String, Object> map = new HashMap<>();
		//转换
		for (int i = 1; i <= columns.size(); i++) {
			Object value = rs.getObject(i);
			String key = columns.get(i - 1);
			if (value instanceof byte[]) {
				map.put(key, DbswitchStrUtils.toHexString((byte[]) value));
			} else if (value instanceof java.sql.Clob) {
				map.put(key, TypeConvertUtils.castToString(value));
			} else if (value instanceof java.sql.Blob) {
				map.put(key, DbswitchStrUtils.toHexString(TypeConvertUtils.castToByteArray(value)));
			} else {
				map.put(key, value);
			}
		}
		return map;
	}

	protected boolean checkOverTime(Long startTime, Long currentTime, Integer overTimes) {
		if (overTimes <= 0) {
			return false;
		}
		return currentTime - startTime >= overTimes * 60 * 1000L;
	}

}
