package com.bjgy.etl.node.load;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName 输出Excel文件
 */
@Data
public class WriteExcelNode {
    
    /**
     * 依赖节点编号
     */
    private String dependNodeNo;
    
    /**
     * 输出文件路径
     */
    private String filePath;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 工作表名
     */
    private String sheetName = "Sheet1";
    
    /**
     * 写入模式：overwrite-覆盖写入, append-追加写入
     */
    private String writeMode = "overwrite";
    
    /**
     * 是否包含标题行
     */
    private Boolean includeHeader = true;
    
    /**
     * 起始行（从1开始）
     */
    private Integer startRow = 1;
    
    /**
     * 起始列（如A、B、C等）
     */
    private String startColumn = "A";
    
    /**
     * 批处理大小
     */
    private Integer batchSize = 1000;
    
    /**
     * 列映射配置
     */
    private List<ColumnMapping> columnMappings = new ArrayList<>();
    
    /**
     * 日期格式
     */
    private String dateFormat = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 数字格式
     */
    private String numberFormat = "#,##0.00";
    
    /**
     * 空值处理方式：keep-保留空值, empty_string-替换为空字符串, default_value-替换为默认值
     */
    private String nullValueHandling = "keep";
    
    /**
     * 默认值（当nullValueHandling为default_value时使用）
     */
    private String defaultValue = "";
    
    /**
     * 是否自动调整列宽
     */
    private Boolean autoSizeColumns = true;
    
    /**
     * 是否冻结窗格
     */
    private Boolean freezePane = false;
    
    /**
     * 冻结行数
     */
    private Integer freezeRows = 1;
    
    /**
     * 冻结列数
     */
    private Integer freezeCols = 0;
    
    /**
     * 列映射内部类
     */
    @Data
    public static class ColumnMapping {
        /**
         * 源列名
         */
        private String sourceColumn;
        
        /**
         * 目标列名（Excel中的列名）
         */
        private String targetColumn;
        
        /**
         * 数据类型：STRING-文本, NUMERIC-数字, DATE-日期, BOOLEAN-布尔
         */
        private String columnType = "STRING";
        
        /**
         * 格式化规则
         */
        private String format;
    }

    /**
     * 获取列映射配置，确保不返回null
     */
    public List<ColumnMapping> getColumnMappings() {
        if (columnMappings == null) {
            columnMappings = new ArrayList<>();
        }
        return columnMappings;
    }
} 