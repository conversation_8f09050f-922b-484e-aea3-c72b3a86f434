package com.bjgy.etl.engine.impl.extract;

import com.zaxxer.hikari.HikariDataSource;
import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.extract.ReadDbNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;
import bjgy.cloud.framework.dbswitch.dbcommon.database.DatabaseOperatorFactory;
import bjgy.cloud.framework.dbswitch.dbcommon.database.IDatabaseOperator;
import bjgy.cloud.framework.dbswitch.dbcommon.domain.StatementResultSet;

import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName ReadDb
 */
public class ReadDb extends EtlEngine {

	public ReadDb(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	@SneakyThrows
	@Override
	public void run() {
		initCache();
		Integer overTimes = pipelineNodeEntity.getOverTimes();
		long startTime = System.currentTimeMillis();
		long totalCount = 0;
		try {
			ReadDbNode readDbNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, ReadDbNode.class);
			Integer fetchSize = readDbNode.getFetchSize();
			String dependNodeNo = readDbNode.getDependNodeNo();

			// 等待依赖节点
			waitForDependNode(dependNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

			//执行逻辑
			DataDatabaseDto database = buildDatabase(readDbNode.getDbType(), readDbNode.getDatabaseId());
			try (HikariDataSource dataSource = createDataSource(database)) {
				ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(database.getDatabaseType());
				IDatabaseOperator sourceOperator = DatabaseOperatorFactory
						.createDatabaseOperator(dataSource, productTypeEnum);
				IMetaDataByJdbcService service = new MetaDataByJdbcServiceImpl(productTypeEnum);
				List<ColumnDescription> columnDescriptions = readDbNode.getIfSql() == 0 ?
						service.queryTableColumnMeta(database.getJdbcUrl(), database.getUserName(), database.getPassword(), database.getDatabaseSchema(), readDbNode.getTableName()) :
						service.querySqlColumnMeta(database.getJdbcUrl(), database.getUserName(), database.getPassword(), readDbNode.getSql());
				List<String> columns = columnDescriptions.stream().map(ColumnDescription::getFieldName).collect(Collectors.toList());
				appendLog(String.format("节点 【%s】 开始获取数据", pipelineNode.getProperties().getName()));
				//更新节点日志
				updateLog();
				try (StatementResultSet srs = readDbNode.getIfSql() == 0 ?
						sourceOperator.queryTableData(database.getDatabaseSchema(), readDbNode.getTableName(), columns) :
						sourceOperator.queryTableData(readDbNode.getSql(), columns); ResultSet rs = srs.getResultset()) {
					//long cacheBytes = 0;
					while (rs.next()) {
						dependFailedCheck(dependNodeNo);
						if (isStop()) {
							throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
						}
						long currentTime = System.currentTimeMillis();
						if (checkOverTime(startTime, currentTime, overTimes)) {
							appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
							appendLog(String.format("节点 【%s】 运行超时，已获取数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
							//更新节点日志
							updateLog();
							break;
						}
						Map<String, Object> map = buildRowMap(columns, rs);
						//向内存队列中推送数据（使用数据复制机制）
						pushDataToDownstream(map);
						//long bytes = SizeOf.newInstance().sizeOf(map);
						//cacheBytes += bytes;
						totalCount += 1;

						// 每处理10000条数据打印一次日志
						if (totalCount % 10000 == 0) {
							appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
							updateLog();
						}

						//判断是否被消费
						while (true) {
							dependFailedCheck(dependNodeNo);
							if (isStop()) {
								throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
							}
							currentTime = System.currentTimeMillis();
							if (checkOverTime(startTime, currentTime, overTimes)) {
								appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
								appendLog(String.format("节点 【%s】 运行超时，已获取数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
								//更新节点日志
								updateLog();
								break;
							}
							// 检查队列大小时需要考虑数据复制机制
							long currentQueueSize = getCurrentQueueSize();
							if (currentQueueSize < fetchSize) {
								break;
							}
							ThreadUtil.sleep(10);
						}
					}
				}
			}
			appendLog(String.format("节点 【%s】 运行成功结束，获取数据总数：%s", pipelineNode.getProperties().getName(), totalCount));
			//成功结束
			successEnd();
		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，获取数据总数：%s，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			//失败结束
			failEnd();
		}

	}

	/**
	 * 获取当前队列大小
	 * 如果启用了数据复制机制，返回所有下游队列的最大大小
	 * 否则返回原有单一队列的大小
	 */
	private long getCurrentQueueSize() {
		if (isDataReplicationEnabled()) {
			// 数据复制机制：返回所有下游队列中的最大大小
			long maxQueueSize = 0;
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = downstreamDataKeys.get(downstreamNodeId);
				if (dataKey != null) {
					long queueSize = memoryManager.getQueueSize(dataKey);
					maxQueueSize = Math.max(maxQueueSize, queueSize);
				}
			}
			return maxQueueSize;
		} else {
			// 原有机制：返回单一队列大小
			return memoryManager.getQueueSize(NODE_DATA_KEY);
		}
	}
}
