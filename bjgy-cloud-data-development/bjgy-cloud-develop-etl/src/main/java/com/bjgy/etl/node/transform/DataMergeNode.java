/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.node.transform;

import lombok.Data;

import java.util.List;

/**
 * 数据合并节点配置类
 *
 * @ClassName DataMergeNode
 */
@Data
public class DataMergeNode {

    // 基本配置
    private List<String> sourceNodes;        // 选中的数据源节点ID数组
    private String mergeStrategy;            // 合并策略：UNION/JOIN/APPEND
    private String syncStrategy;             // 同步策略 REAL_TIME/BATCH

    // JOIN配置
    private String joinType;                 // 关联类型：INNER/LEFT/RIGHT/FULL
    private List<JoinCondition> joinConditions; // 关联条件数组

    // 字段映射配置
    private Boolean enableFieldMapping;      // 是否启用字段映射
    private List<FieldMapping> fieldMappings; // 字段映射数组

    // 数据处理选项
    private Boolean removeDuplicates;        // 是否去重
    private Boolean addSourceTag;            // 是否添加数据源标识
    private String sourceTagField;           // 数据源标识字段名

    // 批量配置
    private Integer batchSize;               // 批量大小

    /**
     * JOIN关联条件
     */
    @Data
    public static class JoinCondition {
        private String leftSource;           // 左数据源
        private String leftField;            // 左字段
        private String rightSource;          // 右数据源
        private String rightField;           // 右字段
    }

    /**
     * 字段映射配置
     */
    @Data
    public static class FieldMapping {
        private String sourceNode;           // 数据源节点
        private String sourceField;          // 源字段名
        private String targetField;          // 目标字段名
        private String dataType;             // 数据类型
    }
}
