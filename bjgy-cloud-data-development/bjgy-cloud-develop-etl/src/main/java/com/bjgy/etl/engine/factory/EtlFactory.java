// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.bjgy.etl.engine.factory;

import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.engine.constant.ToolCategoryEnum;
import com.bjgy.etl.engine.impl.extract.ReadApi;
import com.bjgy.etl.engine.impl.extract.ReadDb;
import com.bjgy.etl.engine.impl.extract.ReadExcel;
import com.bjgy.etl.engine.impl.extract.ReadKafka;
import com.bjgy.etl.engine.impl.load.InsertDb;
import com.bjgy.etl.engine.impl.load.WriteExcel;
import com.bjgy.etl.engine.impl.load.WriteKafka;
import com.bjgy.etl.engine.impl.transform.ColumnMap;
import com.bjgy.etl.engine.impl.transform.DataClean;
import com.bjgy.etl.engine.impl.transform.DataDecrypt;
import com.bjgy.etl.engine.impl.transform.DataDesensitize;
import com.bjgy.etl.engine.impl.transform.DataEncrypt;
import com.bjgy.etl.engine.impl.transform.DataFilter;
import com.bjgy.etl.engine.impl.transform.DataMerge;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 数据库实例构建工厂类
 */
public final class EtlFactory {

	public static final Map<ToolCategoryEnum, Function<PipelineNode, EtlEngine>> TOOL_MAP
			= new HashMap<ToolCategoryEnum, Function<PipelineNode, EtlEngine>>() {

		private static final long serialVersionUID = 9202705534880971997L;

		{
			put(ToolCategoryEnum.READ_DB, ReadDb::new);
			put(ToolCategoryEnum.READ_EXCEL, ReadExcel::new);
			put(ToolCategoryEnum.REST_API, ReadApi::new);
			put(ToolCategoryEnum.READ_KAFKA, ReadKafka::new);
			put(ToolCategoryEnum.INERT_DB, InsertDb::new);
			put(ToolCategoryEnum.COLUMN_MAP, ColumnMap::new);
			put(ToolCategoryEnum.DATA_MERGE, DataMerge::new);
			put(ToolCategoryEnum.WRITE_EXCEL, WriteExcel::new);
			put(ToolCategoryEnum.WRITE_KAFKA, WriteKafka::new);
			put(ToolCategoryEnum.DATA_FILTER, DataFilter::new);
			put(ToolCategoryEnum.DATA_CLEAN, DataClean::new);
			put(ToolCategoryEnum.DATA_ENCRYPT, DataEncrypt::new);
			put(ToolCategoryEnum.DATA_DESENSITIZE, DataDesensitize::new);
			put(ToolCategoryEnum.DATA_DECRYPT, DataDecrypt::new);
		}
	};

	public static EtlEngine getEtlEngine(ToolCategoryEnum categoryEnum, PipelineNode pipelineNode) {
		if (!TOOL_MAP.containsKey(categoryEnum)) {
			throw new RuntimeException("不支持的组件类型！");
		}
		return TOOL_MAP.get(categoryEnum).apply(pipelineNode);
	}

	private EtlFactory() {
		throw new IllegalStateException();
	}

}
