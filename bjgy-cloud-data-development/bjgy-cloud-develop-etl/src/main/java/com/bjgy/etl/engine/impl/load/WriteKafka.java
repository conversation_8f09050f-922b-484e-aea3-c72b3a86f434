package com.bjgy.etl.engine.impl.load;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.load.WriteKafkaNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName WriteKafka
 */
public class WriteKafka extends EtlEngine {

	private static final Logger logger = LoggerFactory.getLogger(WriteKafka.class);

	public WriteKafka(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	private String sourceDataKey;
	private String sourceDataStatusKey;
	private WriteKafkaNode writeKafkaNode;
	private KafkaProducer<String, String> producer;
	private long totalCount = 0;
	private long successCount = 0;
	private long failCount = 0;
	private List<Map<String, Object>> dataList = new ArrayList<>();
	private ObjectMapper objectMapper = new ObjectMapper();
	private List<WriteKafkaNode.FieldMapping> effectiveFieldMappings = new ArrayList<>();

	@SneakyThrows
	@Override
	public void run() {
		initCache();
		Integer overTimes = pipelineNodeEntity.getOverTimes();
		long startTime = System.currentTimeMillis();

		try {
			writeKafkaNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, WriteKafkaNode.class);
			String dependNodeNo = writeKafkaNode.getDependNodeNo();

			// 等待依赖节点
			waitForDependNode(dependNodeNo);

			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

			// 获取数据节点
			String sourceNodeNo = writeKafkaNode.getDependNodeNo();
			sourceDataKey = getSourceDataKey(sourceNodeNo);
			sourceDataStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + sourceNodeNo;

			// 初始化Kafka生产者
			initializeKafkaProducer();

			// 初始化字段映射
			initializeFieldMappings();

			appendLog(String.format("节点 【%s】 开始发送消息到Kafka主题：%s", pipelineNode.getProperties().getName(), writeKafkaNode.getTopic()));

			// 验证连接状态
			try {
				appendLog(String.format("节点 【%s】 验证Kafka连接状态", pipelineNode.getProperties().getName()));
				producer.partitionsFor(writeKafkaNode.getTopic());
				appendLog(String.format("节点 【%s】 Kafka连接状态正常", pipelineNode.getProperties().getName()));
			} catch (Exception e) {
				throw new RuntimeException(String.format("节点 【%s】 Kafka连接状态异常，无法开始发送数据：%s", 
						pipelineNode.getProperties().getName(), e.getMessage()), e);
			}

			// 处理数据并发送到Kafka
			while (true) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}

				long currentTime = System.currentTimeMillis();
				if (checkOverTime(startTime, currentTime, overTimes)) {
					appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
					appendLog(String.format("节点 【%s】 运行超时，已处理数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
					updateLog();
					break;
				}

				// 获取数据
				Map<String, Object> dataMap = memoryManager.rightPop(sourceDataKey);
				if (dataMap != null) {
					totalCount++;
					dataList.add(dataMap);
				}

				// 批量发送数据
				if (dataList.size() >= writeKafkaNode.getBatchSize()) {
					sendDataBatch();
					dataList.clear();
				}

				// 判断节点状态
				Integer sourceDataStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
				if (CommonRunStatus.isSuccess(sourceDataStatus)) {
					// 获取剩余数据
					long remainSize = memoryManager.getQueueSize(sourceDataKey);
					for (int i = 0; i < remainSize; i++) {
						Map<String, Object> remainData = memoryManager.rightPop(sourceDataKey);
						if (remainData != null) {
							totalCount++;
							dataList.add(remainData);
						}
					}
					break;
				}
				ThreadUtil.sleep(20);
			}

			// 发送剩余数据
			if (!dataList.isEmpty()) {
				sendDataBatch();
			}

			// 等待所有消息发送完成
			if (producer != null) {
				producer.flush();
			}

			appendLog(String.format("节点 【%s】 运行成功结束，已处理数据总数：%d，成功发送：%d，发送失败：%d",
					pipelineNode.getProperties().getName(), totalCount, successCount, failCount));
			successEnd();

		} catch (Exception e) {
			logger.error("节点 【{}】 运行出现异常", pipelineNode.getProperties().getName(), e);
			appendLog(String.format("节点 【%s】 运行出现异常：%s", pipelineNode.getProperties().getName(), e.getMessage()));
			failEnd();
		} finally {
			// 关闭Kafka生产者
			if (producer != null) {
				try {
					producer.close();
					appendLog(String.format("节点 【%s】 已关闭Kafka生产者连接", pipelineNode.getProperties().getName()));
				} catch (Exception e) {
					logger.error("关闭Kafka生产者时出现异常", e);
				}
			}
			logger.info("节点 【{}】 完成Kafka写入操作", pipelineNode.getProperties().getName());
		}
	}

	/**
	 * 初始化Kafka生产者
	 */
	private void initializeKafkaProducer() {
		Properties props = new Properties();

		// 基本配置
		props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, writeKafkaNode.getBootstrapServers());
		props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
		props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

		// 性能配置
		props.put(ProducerConfig.BATCH_SIZE_CONFIG, writeKafkaNode.getBatchSize() * 1024); // 转换为字节
		props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, writeKafkaNode.getRequestTimeoutMs());
		props.put(ProducerConfig.RETRIES_CONFIG, writeKafkaNode.getRetries());
		props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, writeKafkaNode.getBufferMemory() * 1024 * 1024L); // 转换为字节

		// 连接超时配置
		props.put(ProducerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 30000); // 30秒空闲连接超时
		props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30000); // 30秒阻塞超时

		// 确认机制
		props.put(ProducerConfig.ACKS_CONFIG, writeKafkaNode.getAcks());

		// 压缩配置
		if (!"none".equals(writeKafkaNode.getCompressionType())) {
			props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, writeKafkaNode.getCompressionType());
		}

		// 幂等性配置
		if (writeKafkaNode.getEnableIdempotence()) {
			props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
		}

		// 事务配置
		if (writeKafkaNode.getEnableTransaction() && StringUtil.isNotBlank(writeKafkaNode.getTransactionalId())) {
			props.put(ProducerConfig.TRANSACTIONAL_ID_CONFIG, writeKafkaNode.getTransactionalId());
		}

		// 安全配置
		if (!"PLAINTEXT".equals(writeKafkaNode.getSecurityProtocol())) {
			props.put("security.protocol", writeKafkaNode.getSecurityProtocol());

			if (writeKafkaNode.getSecurityProtocol().contains("SASL")) {
				props.put("sasl.mechanism", writeKafkaNode.getSaslMechanism());
				if (StringUtil.isNotBlank(writeKafkaNode.getSaslUsername()) &&
						StringUtil.isNotBlank(writeKafkaNode.getSaslPassword())) {
					String jaasConfig = String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
							writeKafkaNode.getSaslUsername(), writeKafkaNode.getSaslPassword());
					props.put("sasl.jaas.config", jaasConfig);
				}
			}
		}

		try {
			producer = new KafkaProducer<>(props);

			// 验证连接 - 尝试获取主题元数据
			appendLog(String.format("节点 【%s】 正在验证Kafka连接：%s",
					pipelineNode.getProperties().getName(), writeKafkaNode.getBootstrapServers()));

			// 尝试获取主题分区信息来验证连接
			producer.partitionsFor(writeKafkaNode.getTopic());

			// 如果启用事务，初始化事务
			if (writeKafkaNode.getEnableTransaction() && StringUtil.isNotBlank(writeKafkaNode.getTransactionalId())) {
				producer.initTransactions();
			}

			appendLog(String.format("节点 【%s】 Kafka生产者初始化成功，连接到：%s",
					pipelineNode.getProperties().getName(), writeKafkaNode.getBootstrapServers()));
		} catch (Exception e) {
			if (producer != null) {
				try {
					producer.close();
				} catch (Exception closeException) {
					logger.error("关闭Kafka生产者时出现异常", closeException);
				}
			}
			throw new RuntimeException(String.format("节点 【%s】 Kafka生产者初始化失败，无法连接到：%s，错误信息：%s",
					pipelineNode.getProperties().getName(), writeKafkaNode.getBootstrapServers(), e.getMessage()), e);
		}
	}

	/**
	 * 初始化字段映射
	 */
	private void initializeFieldMappings() {
		List<WriteKafkaNode.FieldMapping> configuredMappings = writeKafkaNode.getFieldMappings();

		if (configuredMappings != null && !configuredMappings.isEmpty()) {
			// 使用配置的字段映射
			effectiveFieldMappings = new ArrayList<>(configuredMappings);
			appendLog(String.format("节点 【%s】 使用配置的字段映射，共 %d 个字段",
					pipelineNode.getProperties().getName(), effectiveFieldMappings.size()));
		} else {
			// 如果没有配置字段映射，后续从第一条数据中自动生成
			appendLog(String.format("节点 【%s】 未配置字段映射，将从数据中自动生成", pipelineNode.getProperties().getName()));
		}
	}

	/**
	 * 从第一条数据初始化字段映射
	 */
	private void initializeFieldMappingsFromFirstData() {
		//数据为空，直接return
		if (CollectionUtils.isEmpty(dataList)) {
			return;
		}

		// 尝试获取第一条数据
		Map<String, Object> firstData = dataList.get(0);

		List<String> filedMappings = effectiveFieldMappings.stream().map(WriteKafkaNode.FieldMapping::getSourceField).collect(Collectors.toList());

		if (!effectiveFieldMappings.isEmpty() && filedMappings.containsAll(firstData.keySet())) {
			return; // 已经有映射配置
		}

		if (firstData != null) {
			// 从第一条数据生成字段映射
			if (effectiveFieldMappings.isEmpty()) {
				generateFieldMappingsFromData(firstData);
				appendLog(String.format("节点 【%s】 从数据中自动生成字段映射，共 %d 个字段",
						pipelineNode.getProperties().getName(), effectiveFieldMappings.size()));
			} else {
				// 如果有部分配置，则补充缺失的映射
				supplementColumnMappings(firstData);
			}
		} else {
			appendLog(String.format("节点 【%s】 无法获取数据来生成字段映射，生成字段映射失败", pipelineNode.getProperties().getName()));
		}
	}

	private void supplementColumnMappings(Map<String, Object> dataMap) {
		// 获取已配置的源字段列表
		List<String> configuredSourceColumns = new ArrayList<>();
		for (WriteKafkaNode.FieldMapping mapping : effectiveFieldMappings) {
			configuredSourceColumns.add(mapping.getSourceField());
		}
		// 查找数据中未映射的字段
		List<String> missingColumns = new ArrayList<>();
		for (String key : dataMap.keySet()) {
			if (!configuredSourceColumns.contains(key)) {
				missingColumns.add(key);
			}
		}
		if (!missingColumns.isEmpty()) {
			appendLog(String.format("节点 【%s】 发现 %d 个未映射字段，添加到列映射中：%s",
					pipelineNode.getProperties().getName(), missingColumns.size(), String.join(", ", missingColumns)));
			// 为缺失的字段添加默认映射
			for (String missingColumn : missingColumns) {
				WriteKafkaNode.FieldMapping mapping = new WriteKafkaNode.FieldMapping();
				mapping.setSourceField(missingColumn);
				mapping.setTargetField(missingColumn); // 默认使用源字段名作为目标列名
				effectiveFieldMappings.add(mapping);
			}
			appendLog(String.format("节点 【%s】 补充后的列映射总数：%d",
					pipelineNode.getProperties().getName(), effectiveFieldMappings.size()));
		}
	}

	/**
	 * 从数据生成字段映射
	 */
	private void generateFieldMappingsFromData(Map<String, Object> dataMap) {
		if (dataMap == null || dataMap.isEmpty()) {
			return;
		}

		for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
			WriteKafkaNode.FieldMapping mapping = new WriteKafkaNode.FieldMapping();
			mapping.setSourceField(entry.getKey());
			mapping.setTargetField(entry.getKey()); // 默认目标字段名与源字段名相同

			// 根据值类型设置数据类型
			Object value = entry.getValue();
			if (value instanceof String) {
				mapping.setDataType("STRING");
			} else if (value instanceof Integer) {
				mapping.setDataType("INTEGER");
			} else if (value instanceof Long) {
				mapping.setDataType("LONG");
			} else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
				mapping.setDataType("DOUBLE");
			} else if (value instanceof Boolean) {
				mapping.setDataType("BOOLEAN");
			} else if (value instanceof Date) {
				mapping.setDataType("TIMESTAMP");
			} else {
				mapping.setDataType("STRING"); // 默认为字符串
			}

			effectiveFieldMappings.add(mapping);
		}
	}

	/**
	 * 批量发送数据
	 */
	private void sendDataBatch() {
		if (dataList == null || dataList.isEmpty()) {
			return;
		}

		// 检查生产者连接状态
		try {
			producer.partitionsFor(writeKafkaNode.getTopic());
		} catch (Exception e) {
			throw new RuntimeException(String.format("节点 【%s】 Kafka连接已断开，无法发送数据：%s", 
					pipelineNode.getProperties().getName(), e.getMessage()), e);
		}

		// 如果字段映射为空，从第一条数据生成
		if (effectiveFieldMappings.isEmpty()) {
			initializeFieldMappingsFromFirstData();
		}

		List<Future<RecordMetadata>> futures = new ArrayList<>();
		int batchFailCount = 0;

		try {
			// 如果启用事务，开始事务
			if (writeKafkaNode.getEnableTransaction() && StringUtil.isNotBlank(writeKafkaNode.getTransactionalId())) {
				producer.beginTransaction();
			}

			for (Map<String, Object> dataMap : dataList) {
				if (shouldSkipRecord(dataMap)) {
					continue;
				}

				String key = generateMessageKey(dataMap);
				String value = formatMessage(dataMap);
				Integer partition = generatePartition(dataMap);

				ProducerRecord<String, String> record;
				if (partition != null) {
					record = new ProducerRecord<>(writeKafkaNode.getTopic(), partition, key, value);
				} else {
					record = new ProducerRecord<>(writeKafkaNode.getTopic(), key, value);
				}

				if (writeKafkaNode.getAsyncSend()) {
					// 异步发送
					Future<RecordMetadata> future = producer.send(record, (metadata, exception) -> {
						if (exception == null) {
							successCount++;
						} else {
							failCount++;
							logger.error("发送消息失败", exception);
						}
					});
					futures.add(future);
				} else {
					// 同步发送
					try {
						producer.send(record).get();
						successCount++;
					} catch (Exception e) {
						failCount++;
						batchFailCount++;
						logger.error("同步发送消息失败", e);
						
						// 如果同步发送失败率过高，抛出异常
						if (batchFailCount > dataList.size() * 0.5) {
							throw new RuntimeException(String.format("节点 【%s】 批量发送失败率过高，可能Kafka连接异常", 
									pipelineNode.getProperties().getName()), e);
						}
					}
				}
			}

			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}

			// 如果是异步发送，等待部分结果并检查连接状态
			if (writeKafkaNode.getAsyncSend() && !futures.isEmpty()) {
				// 等待前几个消息的结果以检查连接状态
				int checkCount = Math.min(5, futures.size());
				int asyncFailCount = 0;
				
				for (int i = 0; i < checkCount; i++) {
					try {
						futures.get(i).get();
					} catch (Exception e) {
						asyncFailCount++;
						logger.error("检查异步发送结果时出现异常", e);
					}
				}
				
				// 如果异步发送检查失败率过高，可能连接有问题
				if (asyncFailCount == checkCount && checkCount > 0) {
					throw new RuntimeException(String.format("节点 【%s】 异步发送检查全部失败，可能Kafka连接异常", 
							pipelineNode.getProperties().getName()));
				}
			}

			// 如果启用事务，提交事务
			if (writeKafkaNode.getEnableTransaction() && StringUtil.isNotBlank(writeKafkaNode.getTransactionalId())) {
				producer.commitTransaction();
			}

		} catch (Exception e) {
			// 如果启用事务，回滚事务
			if (writeKafkaNode.getEnableTransaction() && StringUtil.isNotBlank(writeKafkaNode.getTransactionalId())) {
				try {
					producer.abortTransaction();
				} catch (Exception abortException) {
					logger.error("回滚事务时出现异常", abortException);
				}
			}
			throw new RuntimeException("批量发送数据失败", e);
		}
	}

	/**
	 * 判断是否应该跳过记录
	 */
	private boolean shouldSkipRecord(Map<String, Object> dataMap) {
		if ("skip".equals(writeKafkaNode.getNullValueHandling())) {
			// 检查是否所有字段都为空
			boolean allNull = true;
			for (WriteKafkaNode.FieldMapping mapping : effectiveFieldMappings) {
				Object value = dataMap.get(mapping.getSourceField());
				if (value != null && !value.toString().trim().isEmpty()) {
					allNull = false;
					break;
				}
			}
			return allNull;
		}
		return false;
	}

	/**
	 * 生成消息键
	 */
	private String generateMessageKey(Map<String, Object> dataMap) {
		switch (writeKafkaNode.getKeyStrategy()) {
			case "field":
				if (StringUtil.isNotBlank(writeKafkaNode.getKeyField())) {
					Object keyValue = dataMap.get(writeKafkaNode.getKeyField());
					return keyValue != null ? keyValue.toString() : null;
				}
				break;
			case "uuid":
				return UUID.randomUUID().toString();
			case "hash":
				return String.valueOf(dataMap.hashCode());
			case "none":
			default:
				return null;
		}
		return null;
	}

	/**
	 * 生成分区
	 */
	private Integer generatePartition(Map<String, Object> dataMap) {
		if ("custom".equals(writeKafkaNode.getPartitionStrategy()) &&
				StringUtil.isNotBlank(writeKafkaNode.getPartitionField())) {
			Object partitionValue = dataMap.get(writeKafkaNode.getPartitionField());
			if (partitionValue != null) {
				return Math.abs(partitionValue.hashCode()) % 10; // 简单的分区计算，实际应该根据主题分区数计算
			}
		}
		return null; // 使用默认分区策略
	}

	/**
	 * 格式化消息
	 */
	private String formatMessage(Map<String, Object> dataMap) throws Exception {
		Map<String, Object> messageData = new HashMap<>();

		// 根据字段映射构建消息数据
		for (WriteKafkaNode.FieldMapping mapping : effectiveFieldMappings) {
			Object sourceValue = dataMap.get(mapping.getSourceField());
			Object targetValue = formatFieldValue(sourceValue, mapping);

			// 处理空值
			if (targetValue == null) {
				targetValue = handleNullValue();
			}

			messageData.put(mapping.getTargetField(), targetValue);
		}

		// 根据消息格式生成最终消息
		switch (writeKafkaNode.getMessageFormat()) {
			case "JSON":
				return objectMapper.writeValueAsString(messageData);
			case "CSV":
				return formatAsCSV(messageData);
			case "STRING":
				return messageData.toString();
			case "AVRO":
				// TODO: 实现AVRO序列化
				return objectMapper.writeValueAsString(messageData);
			default:
				return objectMapper.writeValueAsString(messageData);
		}
	}

	/**
	 * 格式化字段值
	 */
	private Object formatFieldValue(Object value, WriteKafkaNode.FieldMapping mapping) {
		if (value == null) {
			return null;
		}

		try {
			switch (mapping.getDataType()) {
				case "STRING":
					return value.toString();
				case "INTEGER":
					if (value instanceof Number) {
						return ((Number) value).intValue();
					}
					return Integer.parseInt(value.toString());
				case "LONG":
					if (value instanceof Number) {
						return ((Number) value).longValue();
					}
					return Long.parseLong(value.toString());
				case "DOUBLE":
					if (value instanceof Number) {
						return ((Number) value).doubleValue();
					}
					return Double.parseDouble(value.toString());
				case "BOOLEAN":
					if (value instanceof Boolean) {
						return value;
					}
					return Boolean.parseBoolean(value.toString());
				case "DATE":
				case "TIMESTAMP":
					if (value instanceof Date) {
						if (StringUtil.isNotBlank(mapping.getFormat())) {
							SimpleDateFormat sdf = new SimpleDateFormat(mapping.getFormat());
							return sdf.format((Date) value);
						}
						return value.toString();
					}
					return value.toString();
				default:
					return value.toString();
			}
		} catch (Exception e) {
			logger.warn("格式化字段值失败，使用原始值：{}", e.getMessage());
			return value.toString();
		}
	}

	/**
	 * 处理空值
	 */
	private Object handleNullValue() {
		switch (writeKafkaNode.getNullValueHandling()) {
			case "send_null":
				return null;
			case "default_value":
				return StringUtil.isNotBlank(writeKafkaNode.getDefaultValue()) ? writeKafkaNode.getDefaultValue() : "";
			case "skip":
			default:
				return "";
		}
	}

	/**
	 * 格式化为CSV
	 */
	private String formatAsCSV(Map<String, Object> messageData) {
		StringBuilder csvBuilder = new StringBuilder();

		// 如果需要包含标题行（仅第一次）
		if (writeKafkaNode.getIncludeHeader() && totalCount == 1) {
			List<String> headers = new ArrayList<>();
			for (WriteKafkaNode.FieldMapping mapping : effectiveFieldMappings) {
				headers.add(mapping.getTargetField());
			}
			csvBuilder.append(String.join(writeKafkaNode.getCsvDelimiter(), headers)).append("\n");
		}

		// 添加数据行
		List<String> values = new ArrayList<>();
		for (WriteKafkaNode.FieldMapping mapping : effectiveFieldMappings) {
			Object value = messageData.get(mapping.getTargetField());
			values.add(value != null ? value.toString() : "");
		}
		csvBuilder.append(String.join(writeKafkaNode.getCsvDelimiter(), values));

		return csvBuilder.toString();
	}
}
