/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.engine.impl.transform;

import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.transform.DataDesensitizeNode;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.flink.common.utils.ThreadUtil;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 数据脱敏引擎
 */
@Slf4j
public class DataDesensitize extends EtlEngine {

	private DataDesensitizeNode dataDesensitizeNode;
	private ExecutorService executorService;
	private final SecureRandom random = new SecureRandom();
	private final long startTime = System.currentTimeMillis();

	public DataDesensitize(PipelineNode pipelineNode) {
		super(pipelineNode);
		this.dataDesensitizeNode = JSONUtil.parseObject(nodeJson, DataDesensitizeNode.class);
	}

	@Override
	public void run() {
		try {
			//节点初始化
			initCache();
			// 等待依赖节点
			waitForDependNode(dataDesensitizeNode.getDependNodeNo());

			appendLog(String.format("节点【%s】开始执行数据脱敏处理", pipelineNode.getProperties().getName()));
			// 初始化线程池
			initThreadPool();

			// 根据脱敏模式执行相应逻辑
			switch (dataDesensitizeNode.getDesensitizeMode()) {
				case "FIELD_LEVEL":
					executeFieldLevelDesensitize();
					break;
				case "ROW_LEVEL":
					executeRowLevelDesensitize();
					break;
				case "CUSTOM":
					executeCustomDesensitize();
					break;
				default:
					throw new RuntimeException("不支持的脱敏模式: " + dataDesensitizeNode.getDesensitizeMode());
			}

			appendLog(String.format("节点【%s】数据脱敏完成", pipelineNode.getProperties().getName()));
			successEnd();

		} catch (Exception e) {
			appendLog(String.format("节点【%s】数据脱敏失败：%s", pipelineNode.getProperties().getName(), e.getMessage()));
			failEnd();
		} finally {
			if (executorService != null && !executorService.isShutdown()) {
				executorService.shutdown();
			}
		}
	}

	/**
	 * 初始化线程池
	 */
	private void initThreadPool() {
		if (Boolean.TRUE.equals(dataDesensitizeNode.getParallelProcessing())) {
			int threadCount = dataDesensitizeNode.getThreadCount() != null ? dataDesensitizeNode.getThreadCount() : 4;
			executorService = Executors.newFixedThreadPool(threadCount);
			appendLog(String.format("节点【%s】启用并行处理，线程数量: %s", pipelineNode.getProperties().getName(), threadCount));
		}
	}

	/**
	 * 执行字段级脱敏
	 */
	private void executeFieldLevelDesensitize() {
		appendLog(String.format("节点【%s】开始执行字段级脱敏", pipelineNode.getProperties().getName()));

		String sourceDataKey = getSourceDataKey(dataDesensitizeNode.getDependNodeNo());
		List<Map<String, Object>> batch = new ArrayList<>();
		int batchSize = dataDesensitizeNode.getBatchSize() != null ? dataDesensitizeNode.getBatchSize() : 1000;

		long processedCount = 0;
		long desensitizedCount = 0;
		long errorCount = 0;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}
			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataDesensitizeNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDesensitizeNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batch.add(data);
						// 如果缓冲区满了，先处理
						if (batch.size() >= batchSize) {
							long[] counts = processBatch(batch);
							processedCount += counts[0];
							desensitizedCount += counts[1];
							errorCount += counts[2];
							batch.clear();
						}
					}
					// 处理剩余数据
					if (!batch.isEmpty()) {
						processBatch(batch);
						batch.clear();
					}
					break;
				} else {
					// 依赖节点未完成，等待20ms继续循环
					ThreadUtil.sleep(20);
					continue;
				}
			}


			batch.add(data);
			if (batch.size() >= batchSize) {
				long[] counts = processBatch(batch);
				processedCount += counts[0];
				desensitizedCount += counts[1];
				errorCount += counts[2];
				batch.clear();
			}
		}

		appendLog(String.format("节点【%s】字段级脱敏完成 - 处理记录数: %d, 脱敏记录数: %d, 错误记录数: %d", pipelineNode.getProperties().getName(),
				processedCount, desensitizedCount, errorCount));
	}

	/**
	 * 执行行级脱敏
	 */
	private void executeRowLevelDesensitize() {
		appendLog(String.format("节点【%s】开始执行行级脱敏", pipelineNode.getProperties().getName()));

		String sourceDataKey = getSourceDataKey(dataDesensitizeNode.getDependNodeNo());
		List<Map<String, Object>> batch = new ArrayList<>();
		int batchSize = dataDesensitizeNode.getBatchSize() != null ? dataDesensitizeNode.getBatchSize() : 1000;

		long processedCount = 0;
		long desensitizedCount = 0;
		long errorCount = 0;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataDesensitizeNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDesensitizeNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batch.add(data);
						// 如果缓冲区满了，先处理
						if (batch.size() >= batchSize) {
							long[] counts = processRowLevelBatch(batch);
							processedCount += counts[0];
							desensitizedCount += counts[1];
							errorCount += counts[2];
							batch.clear();
						}
					}
					// 处理剩余数据
					if (!batch.isEmpty()) {
						processRowLevelBatch(batch);
						batch.clear();
					}
					break;
				} else {
					// 依赖节点未完成，等待20ms继续循环
					ThreadUtil.sleep(20);
					continue;
				}
			}

			batch.add(data);
			if (batch.size() >= batchSize) {
				long[] counts = processRowLevelBatch(batch);
				processedCount += counts[0];
				desensitizedCount += counts[1];
				errorCount += counts[2];
				batch.clear();
			}
		}

		appendLog(String.format("节点【%s】行级脱敏完成 - 处理记录数: %d, 脱敏记录数: %d, 错误记录数: %d", pipelineNode.getProperties().getName(),
				processedCount, desensitizedCount, errorCount));
	}

	/**
	 * 执行自定义脱敏
	 */
	private void executeCustomDesensitize() {
		appendLog(String.format("节点【%s】开始执行自定义脱敏", pipelineNode.getProperties().getName()));

		String sourceDataKey = getSourceDataKey(dataDesensitizeNode.getDependNodeNo());
		ScriptEngine engine = new ScriptEngineManager().getEngineByName("javascript");

		try {
			// 预编译脚本
			engine.eval(dataDesensitizeNode.getCustomScript());

			long processedCount = 0;
			long desensitizedCount = 0;
			long errorCount = 0;

			while (true) {

				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}

				// 检查超时
				long currentTime = System.currentTimeMillis();
				Integer overTimes = pipelineNodeEntity.getOverTimes();
				if (checkOverTime(startTime, currentTime, overTimes)) {
					appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
					break;
				}

				// 检查依赖节点失败
				dependFailedCheck(dataDesensitizeNode.getDependNodeNo());

				Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
				if (data == null) {
					// 检查依赖节点状态
					String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDesensitizeNode.getDependNodeNo();
					Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
					if (CommonRunStatus.isSuccess(dependNodeStatus)) {
						// 依赖节点已完成，继续获取队列中剩余数据
						while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
							// 处理这条数据的脱敏逻辑（复制原有的处理代码）
							try {
								Map<String, Object> desensitizedData = executeJavaScriptDesensitize(data, engine);
								if (desensitizedData != null) {
									pushDataToDownstream(desensitizedData);
									desensitizedCount++;
								}
								processedCount++;
							} catch (Exception e) {
								errorCount++;
								handleError("自定义脚本执行失败: " + e.getMessage(), data, errorCount);
							}
						}
						break;
					} else {
						// 依赖节点未完成，等待20ms继续循环
						ThreadUtil.sleep(20);
						continue;
					}
				}

				try {
					Map<String, Object> desensitizedData = executeJavaScriptDesensitize(data, engine);
					if (desensitizedData != null) {
						pushDataToDownstream(desensitizedData);
						desensitizedCount++;
					}
					processedCount++;
				} catch (Exception e) {
					errorCount++;
					handleError("自定义脚本执行失败: " + e.getMessage(), data, errorCount);
				}
			}

			appendLog(String.format("节点【%s】自定义脱敏完成 - 处理记录数: %d, 脱敏记录数: %d, 错误记录数: %d", pipelineNode.getProperties().getName(),
					processedCount, desensitizedCount, errorCount));

		} catch (Exception e) {
			throw new RuntimeException("自定义脚本初始化失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 处理批量数据
	 */
	private long[] processBatch(List<Map<String, Object>> batch) {
		long batchProcessed = 0;
		long batchDesensitized = 0;
		long batchError = 0;

		if (Boolean.TRUE.equals(dataDesensitizeNode.getParallelProcessing()) && executorService != null) {
			// 并行处理
			List<Future<long[]>> futures = new ArrayList<>();
			int chunkSize = Math.max(1, batch.size() / dataDesensitizeNode.getThreadCount());

			for (int i = 0; i < batch.size(); i += chunkSize) {
				int end = Math.min(i + chunkSize, batch.size());
				List<Map<String, Object>> chunk = batch.subList(i, end);

				futures.add(executorService.submit(() -> processChunk(chunk)));
			}

			// 收集结果
			for (Future<long[]> future : futures) {
				try {
					long[] counts = future.get();
					batchProcessed += counts[0];
					batchDesensitized += counts[1];
					batchError += counts[2];
				} catch (Exception e) {
					log.error("并行处理任务执行失败", e);
					batchError += chunkSize;
				}
			}
		} else {
			// 串行处理
			long[] counts = processChunk(batch);
			batchProcessed = counts[0];
			batchDesensitized = counts[1];
			batchError = counts[2];
		}

		return new long[]{batchProcessed, batchDesensitized, batchError};
	}

	/**
	 * 处理数据块
	 */
	private long[] processChunk(List<Map<String, Object>> chunk) {
		long processed = 0;
		long desensitized = 0;
		long error = 0;

		for (Map<String, Object> data : chunk) {
			try {
				Map<String, Object> result = processFieldLevelRecord(data);
				pushDataToDownstream(result);
				desensitized++;
				processed++;
			} catch (Exception e) {
				error++;
				handleError("字段级脱敏处理失败: " + e.getMessage(), data, error);
			}
		}

		return new long[]{processed, desensitized, error};
	}

	/**
	 * 处理单条字段级脱敏记录
	 */
	private Map<String, Object> processFieldLevelRecord(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>(data);

		for (DataDesensitizeNode.DesensitizeField field : dataDesensitizeNode.getDesensitizeFields()) {
			String fieldName = field.getFieldName();
			Object fieldValue = data.get(fieldName);

			if (fieldValue != null) {
				String desensitizedValue = desensitizeFieldValue(fieldValue.toString(), field);

				// 设置输出字段
				String outputFieldName = StringUtil.isNotBlank(field.getOutputFieldName()) ?
						field.getOutputFieldName() : fieldName;
				result.put(outputFieldName, desensitizedValue);

				// 是否保留原值
				if (!Boolean.TRUE.equals(field.getKeepOriginal()) && outputFieldName.equals(fieldName)) {
					// 如果输出字段名与原字段名相同且不保留原值，则已经被替换
				} else if (!Boolean.TRUE.equals(field.getKeepOriginal())) {
					// 如果输出字段名不同且不保留原值，则删除原字段
					result.remove(fieldName);
				}
			}
		}

		return result;
	}

	/**
	 * 脱敏字段值
	 */
	private String desensitizeFieldValue(String value, DataDesensitizeNode.DesensitizeField field) {
		if (StringUtil.isBlank(value)) {
			return value;
		}

		switch (field.getStrategy()) {
			case "ID_CARD":
				return desensitizeIdCard(value);
			case "PHONE":
				return desensitizePhone(value);
			case "EMAIL":
				return desensitizeEmail(value);
			case "NAME":
				return desensitizeName(value);
			case "BANK_CARD":
				return desensitizeBankCard(value);
			case "ADDRESS":
				return desensitizeAddress(value);
			case "IP_ADDRESS":
				return desensitizeIpAddress(value);
			case "MASK":
				return maskValue(value, field);
			case "REPLACE":
				return field.getReplaceValue() != null ? field.getReplaceValue() : "";
			case "TRUNCATE":
				return truncateValue(value, field);
			case "RANDOMIZE":
				return randomizeValue(value, field);
			case "NULL_REPLACE":
				return null;
			case "AMOUNT":
				return desensitizeAmount(value, field);
			default:
				return value;
		}
	}

	/**
	 * 身份证号脱敏
	 */
	private String desensitizeIdCard(String idCard) {
		if (idCard.length() < 8) {
			return idCard;
		}
		return idCard.substring(0, 6) + "********" + idCard.substring(idCard.length() - 4);
	}

	/**
	 * 手机号脱敏
	 */
	private String desensitizePhone(String phone) {
		if (phone.length() < 7) {
			return phone;
		}
		return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
	}

	/**
	 * 邮箱脱敏
	 */
	private String desensitizeEmail(String email) {
		int atIndex = email.indexOf("@");
		if (atIndex <= 2) {
			return email;
		}
		return email.substring(0, 2) + "***" + email.substring(atIndex);
	}

	/**
	 * 姓名脱敏
	 */
	private String desensitizeName(String name) {
		if (name.length() <= 1) {
			return name;
		} else if (name.length() == 2) {
			return name.substring(0, 1) + "*";
		} else {
			StringBuilder stars = new StringBuilder();
			for (int i = 0; i < name.length() - 2; i++) {
				stars.append("*");
			}
			return name.substring(0, 1) + stars.toString() + name.substring(name.length() - 1);
		}
	}

	/**
	 * 银行卡号脱敏
	 */
	private String desensitizeBankCard(String bankCard) {
		if (bankCard.length() < 8) {
			return bankCard;
		}
		return bankCard.substring(0, 4) + "********" + bankCard.substring(bankCard.length() - 4);
	}

	/**
	 * 地址脱敏
	 */
	private String desensitizeAddress(String address) {
		if (address.length() <= 6) {
			int maskLength = address.length() - address.length() / 2;
			StringBuilder stars = new StringBuilder();
			for (int i = 0; i < maskLength; i++) {
				stars.append("*");
			}
			return address.substring(0, address.length() / 2) + stars.toString();
		}
		return address.substring(0, 6) + "***";
	}

	/**
	 * IP地址脱敏
	 */
	private String desensitizeIpAddress(String ip) {
		String[] parts = ip.split("\\.");
		if (parts.length == 4) {
			return parts[0] + "." + parts[1] + ".***." + parts[3];
		}
		return ip;
	}

	/**
	 * 字符遮盖
	 */
	private String maskValue(String value, DataDesensitizeNode.DesensitizeField field) {
		String maskChar = StringUtil.isNotBlank(field.getMaskChar()) ? field.getMaskChar() : "*";
		int keepStart = Integer.parseInt(StringUtil.isNotBlank(field.getKeepStart()) ? field.getKeepStart() : "0");
		int keepEnd = Integer.parseInt(StringUtil.isNotBlank(field.getKeepEnd()) ? field.getKeepEnd() : "0");

		if (keepStart + keepEnd >= value.length()) {
			return value;
		}

		String start = value.substring(0, keepStart);
		String end = value.substring(value.length() - keepEnd);
		StringBuilder middle = new StringBuilder();
		for (int i = 0; i < value.length() - keepStart - keepEnd; i++) {
			middle.append(maskChar);
		}

		return start + middle.toString() + end;
	}

	/**
	 * 数据截断
	 */
	private String truncateValue(String value, DataDesensitizeNode.DesensitizeField field) {
		int length = Integer.parseInt(StringUtil.isNotBlank(field.getTruncateLength()) ? field.getTruncateLength() : "0");
		String position = StringUtil.isNotBlank(field.getTruncatePosition()) ? field.getTruncatePosition() : "START";

		if (length >= value.length()) {
			return value;
		}

		if ("END".equals(position)) {
			return value.substring(value.length() - length);
		} else {
			return value.substring(0, length);
		}
	}

	/**
	 * 随机化
	 */
	private String randomizeValue(String value, DataDesensitizeNode.DesensitizeField field) {
		String randomType = StringUtil.isNotBlank(field.getRandomType()) ? field.getRandomType() : "NUMBER";
		int length = value.length();

		switch (randomType) {
			case "NUMBER":
				StringBuilder numbers = new StringBuilder();
				for (int i = 0; i < length; i++) {
					numbers.append(random.nextInt(10));
				}
				return numbers.toString();
			case "LETTER":
				StringBuilder letters = new StringBuilder();
				for (int i = 0; i < length; i++) {
					letters.append((char) ('A' + random.nextInt(26)));
				}
				return letters.toString();
			case "CHAR":
				StringBuilder chars = new StringBuilder();
				String charSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
				for (int i = 0; i < length; i++) {
					chars.append(charSet.charAt(random.nextInt(charSet.length())));
				}
				return chars.toString();
			case "DATE":
				return "2023-01-01"; // 简化实现
			default:
				return value;
		}
	}

	/**
	 * 金额脱敏
	 */
	private String desensitizeAmount(String amount, DataDesensitizeNode.DesensitizeField field) {
		String amountType = StringUtil.isNotBlank(field.getAmountType()) ? field.getAmountType() : "RANGE";

		try {
			double value = Double.parseDouble(amount);
			switch (amountType) {
				case "RANGE":
					if (value < 1000) return "0-1000";
					else if (value < 10000) return "1000-10000";
					else if (value < 100000) return "10000-100000";
					else return "100000+";
				case "PARTIAL_MASK":
					String str = amount;
					if (str.length() > 4) {
						return str.substring(0, 2) + "***" + str.substring(str.length() - 2);
					}
					return str;
				case "SCALE":
					return String.valueOf(Math.round(value * 0.1)); // 缩放到10%
				default:
					return amount;
			}
		} catch (NumberFormatException e) {
			return amount;
		}
	}

	/**
	 * 处理行级脱敏批量数据
	 */
	private long[] processRowLevelBatch(List<Map<String, Object>> batch) {
		long processed = 0;
		long desensitized = 0;
		long error = 0;

		for (Map<String, Object> data : batch) {
			try {
				Map<String, Object> result = processRowLevelRecord(data);
				if (result != null) {
					pushDataToDownstream(result);
					desensitized++;
				}
				processed++;
			} catch (Exception e) {
				error++;
				handleError("行级脱敏处理失败: " + e.getMessage(), data, error);
			}
		}

		return new long[]{processed, desensitized, error};
	}

	/**
	 * 处理单条行级脱敏记录
	 */
	private Map<String, Object> processRowLevelRecord(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>(data);

		switch (dataDesensitizeNode.getRowDesensitizeStrategy()) {
			case "ALL_MASK":
				return processAllMask(result);
			case "KEY_FIELDS":
				return processKeyFields(result);
			case "RANDOM_DATA":
				return processRandomData(result);
			case "DATA_SHUFFLE":
				return processDataShuffle(result);
			case "GENERALIZATION":
				return processGeneralization(result);
			default:
				return result;
		}
	}

	/**
	 * 全字段遮盖处理
	 */
	private Map<String, Object> processAllMask(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>();
		String maskChar = StringUtil.isNotBlank(dataDesensitizeNode.getRowMaskChar()) ?
				dataDesensitizeNode.getRowMaskChar() : "*";

		for (Map.Entry<String, Object> entry : data.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof String) {
				String str = (String) value;
				StringBuilder mask = new StringBuilder();
				for (int i = 0; i < Math.max(1, str.length()); i++) {
					mask.append(maskChar);
				}
				result.put(entry.getKey(), mask.toString());
			} else if (value instanceof Number) {
				result.put(entry.getKey(), 0);
			} else {
				result.put(entry.getKey(), maskChar);
			}
		}

		return result;
	}

	/**
	 * 关键字段脱敏处理
	 */
	private Map<String, Object> processKeyFields(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>(data);
		String keyFields = dataDesensitizeNode.getKeyFields();

		if (StringUtil.isNotBlank(keyFields)) {
			String[] fields = keyFields.split(",");
			String maskChar = StringUtil.isNotBlank(dataDesensitizeNode.getRowMaskChar()) ?
					dataDesensitizeNode.getRowMaskChar() : "*";

			for (String field : fields) {
				field = field.trim();
				Object value = result.get(field);
				if (value instanceof String) {
					String str = (String) value;
					StringBuilder mask = new StringBuilder();
					for (int i = 0; i < Math.max(1, str.length()); i++) {
						mask.append(maskChar);
					}
					result.put(field, mask.toString());
				} else if (value instanceof Number) {
					result.put(field, 0);
				} else if (value != null) {
					result.put(field, maskChar);
				}
			}
		}

		return result;
	}

	/**
	 * 随机数据生成处理
	 */
	private Map<String, Object> processRandomData(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>();

		for (Map.Entry<String, Object> entry : data.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof String) {
				result.put(entry.getKey(), generateRandomString(((String) value).length()));
			} else if (value instanceof Integer) {
				result.put(entry.getKey(), random.nextInt(1000));
			} else if (value instanceof Long) {
				result.put(entry.getKey(), random.nextLong());
			} else if (value instanceof Double) {
				result.put(entry.getKey(), random.nextDouble() * 1000);
			} else {
				result.put(entry.getKey(), value);
			}
		}

		return result;
	}

	/**
	 * 数据置换处理（简化实现）
	 */
	private Map<String, Object> processDataShuffle(Map<String, Object> data) {
		// 简化实现：随机打乱字符串字段的字符顺序
		Map<String, Object> result = new HashMap<>();

		for (Map.Entry<String, Object> entry : data.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof String) {
				String str = (String) value;
				List<Character> chars = new ArrayList<>();
				for (char c : str.toCharArray()) {
					chars.add(c);
				}
				Collections.shuffle(chars, random);
				StringBuilder shuffled = new StringBuilder();
				for (char c : chars) {
					shuffled.append(c);
				}
				result.put(entry.getKey(), shuffled.toString());
			} else {
				result.put(entry.getKey(), value);
			}
		}

		return result;
	}

	/**
	 * 数据泛化处理
	 */
	private Map<String, Object> processGeneralization(Map<String, Object> data) {
		Map<String, Object> result = new HashMap<>();
		String level = dataDesensitizeNode.getGeneralizationLevel();

		for (Map.Entry<String, Object> entry : data.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof String) {
				result.put(entry.getKey(), generalizeString((String) value, level));
			} else if (value instanceof Number) {
				result.put(entry.getKey(), generalizeNumber((Number) value, level));
			} else {
				result.put(entry.getKey(), value);
			}
		}

		return result;
	}

	/**
	 * 字符串泛化
	 */
	private String generalizeString(String str, String level) {
		switch (level) {
			case "LOW":
				return str.length() > 2 ? str.substring(0, 2) + "***" : str;
			case "MEDIUM":
				return str.length() > 1 ? str.substring(0, 1) + "***" : str;
			case "HIGH":
				return "***";
			default:
				return str;
		}
	}

	/**
	 * 数字泛化
	 */
	private Object generalizeNumber(Number num, String level) {
		double value = num.doubleValue();
		switch (level) {
			case "LOW":
				return Math.round(value / 10) * 10; // 精确到十位
			case "MEDIUM":
				return Math.round(value / 100) * 100; // 精确到百位
			case "HIGH":
				return Math.round(value / 1000) * 1000; // 精确到千位
			default:
				return num;
		}
	}

	/**
	 * 生成随机字符串
	 */
	private String generateRandomString(int length) {
		String charSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		StringBuilder result = new StringBuilder();
		for (int i = 0; i < length; i++) {
			result.append(charSet.charAt(random.nextInt(charSet.length())));
		}
		return result.toString();
	}

	/**
	 * 处理错误
	 */
	private void handleError(String errorMsg, Map<String, Object> data, long errorCount) {
		switch (dataDesensitizeNode.getErrorHandlingStrategy()) {
			case "SKIP":
				log.debug("跳过错误记录: {}", errorMsg);
				break;
			case "STOP":
				throw new RuntimeException(errorMsg);
			case "LOG":
				appendLog(String.format("节点【%s】错误记录 #", pipelineNode.getProperties().getName()) + errorCount + ": " + errorMsg);
				// 继续处理，但记录错误
				pushDataToDownstream(data);
				break;
			default:
				log.warn("未知的错误处理策略: {}", dataDesensitizeNode.getErrorHandlingStrategy());
				break;
		}
	}

	/**
	 * 执行JavaScript脱敏
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> executeJavaScriptDesensitize(Map<String, Object> data, ScriptEngine scriptEngine) throws Exception {
		// 创建数据的副本，避免原数据被意外修改
		Map<String, Object> dataCopy = new HashMap<>(data);

		try {
			// 将数据转换为JSON字符串传递给JavaScript引擎（传对象js无法正确修改值）
			String jsonData = StringUtil.toJson(dataCopy);
			scriptEngine.put("recordJson", jsonData);

			// 构建包装后的JavaScript代码，自动处理JSON转换
			String wrappedJsCode = buildWrappedJavaScriptCode(dataDesensitizeNode.getCustomScript());

			// 执行包装后的JavaScript代码
			scriptEngine.eval(wrappedJsCode);

			// 调用包装后的函数并获取结果
			Object result = scriptEngine.eval("wrappedDesensitizeData(recordJson)");

			if (result == null) {
				return null;
			}

			// 如果返回的是JSON字符串，解析为Map
			if (result instanceof String) {
				String resultJson = (String) result;
				if ("null".equals(resultJson) || StringUtil.isBlank(resultJson)) {
					return null;
				}
				try {
					return StringUtil.fromJson(resultJson, Map.class);
				} catch (Exception e) {
					appendLog(String.format("节点【%s】解析JavaScript返回的JSON失败: %s",
							pipelineNode.getProperties().getName(), e.getMessage()));
					return dataCopy;
				}
			}

			// 如果返回的是Map类型，直接返回
			if (result instanceof Map) {
				return (Map<String, Object>) result;
			}

			// 如果都获取不到，返回原数据副本
			return dataCopy;

		} catch (Exception e) {
			// 记录JavaScript执行错误的详细信息
			appendLog(String.format("节点【%s】JavaScript脱敏执行错误: %s, 数据: %s",
					pipelineNode.getProperties().getName(), e.getMessage(), data.toString()));
			throw e;
		}
	}

	/**
	 * 构建包装后的JavaScript代码，自动处理JSON转换
	 */
	private String buildWrappedJavaScriptCode(String userJsCode) {
		StringBuilder wrappedCode = new StringBuilder();

		// 添加包装函数
		wrappedCode.append("function wrappedDesensitizeData(recordJson) {\n");
		wrappedCode.append("  // 自动解析JSON为JavaScript对象\n");
		wrappedCode.append("  var record = JSON.parse(recordJson);\n");
		wrappedCode.append("  \n");

		// 检查用户代码是否已经包含desensitize函数定义
		if (userJsCode.contains("function desensitize")) {
			// 用户代码包含完整的desensitize函数定义
			wrappedCode.append("  // 用户定义的desensitize函数\n");
			wrappedCode.append("  ").append(userJsCode).append("\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用用户的desensitize函数\n");
			wrappedCode.append("  var result = desensitize(record);\n");
		} else {
			// 用户代码是直接的处理逻辑，包装成desensitize函数
			wrappedCode.append("  // 包装用户的处理逻辑\n");
			wrappedCode.append("  function desensitize(record) {\n");
			wrappedCode.append("    ").append(userJsCode.replace("\n", "\n    ")).append("\n");
			wrappedCode.append("    return record;\n");
			wrappedCode.append("  }\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用包装后的desensitize函数\n");
			wrappedCode.append("  var result = desensitize(record);\n");
		}

		wrappedCode.append("  \n");
		wrappedCode.append("  // 自动转换结果为JSON字符串返回\n");
		wrappedCode.append("  if (result === null || result === undefined) {\n");
		wrappedCode.append("    return null;\n");
		wrappedCode.append("  }\n");
		wrappedCode.append("  return JSON.stringify(result);\n");
		wrappedCode.append("}\n");

		return wrappedCode.toString();
	}
}
