/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.engine.impl.transform;

import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.transform.DataMergeNode;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据合并组件处理逻辑
 * 支持多种合并策略：UNION、JOIN、APPEND
 *
 * @ClassName DataMerge
 */
public class DataMerge extends EtlEngine {

	public DataMerge(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	private DataMergeNode dataMergeNode;
	private Map<String, String> sourceDataKeys = new HashMap<>();
	private Map<String, String> sourceStatusKeys = new HashMap<>();
	private Map<String, List<Map<String, Object>>> sourceDataBuffers = new ConcurrentHashMap<>();
	private long totalCount = 0;
	private int batchCount = 0;

	@Override
	public void run() {
		// 初始化缓存
		initCache();
		long startTime = System.currentTimeMillis();

		try {
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			dataMergeNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, DataMergeNode.class);

			// 验证配置
			validateConfiguration();

			// 初始化数据源
			initializeDataSources();

			appendLog(String.format("节点 【%s】 开始运行，合并策略：%s，同步策略：%s",
					pipelineNode.getProperties().getName(),
					dataMergeNode.getMergeStrategy(),
					dataMergeNode.getSyncStrategy()));

			// 根据同步策略执行不同的处理逻辑
			switch (dataMergeNode.getSyncStrategy()) {
				case "REAL_TIME":
					processRealTimeStrategy(startTime, overTimes);
					break;
				case "BATCH":
					processBatchStrategy(startTime, overTimes);
					break;
				default:
					throw new RuntimeException("不支持的同步策略: " + dataMergeNode.getSyncStrategy());
			}

			appendLog(String.format("节点 【%s】 运行成功结束，已合并数据总数：%d",
					pipelineNode.getProperties().getName(), totalCount));
			successEnd();

		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，已合并数据总数：%d，错误信息：%s",
					pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			failEnd();
		}
	}

	/**
	 * 验证配置
	 */
	private void validateConfiguration() {
		if (dataMergeNode.getSourceNodes() == null || dataMergeNode.getSourceNodes().size() < 2) {
			throw new RuntimeException("数据合并节点至少需要配置2个数据源");
		}

		if ("JOIN".equals(dataMergeNode.getMergeStrategy())) {
			if (dataMergeNode.getJoinConditions() == null || dataMergeNode.getJoinConditions().isEmpty()) {
				throw new RuntimeException("JOIN策略必须配置关联条件");
			}
		}

		if ("BATCH".equals(dataMergeNode.getSyncStrategy())) {
			if (dataMergeNode.getBatchSize() == null || dataMergeNode.getBatchSize() <= 0) {
				throw new RuntimeException("批量模式必须设置有效的批量大小");
			}
		}

		// 验证字段映射配置
		if (Boolean.TRUE.equals(dataMergeNode.getEnableFieldMapping())) {
			if (dataMergeNode.getFieldMappings() == null || dataMergeNode.getFieldMappings().isEmpty()) {
				throw new RuntimeException("启用字段映射时必须配置字段映射规则");
			}

			// 验证字段映射配置的完整性
			for (DataMergeNode.FieldMapping mapping : dataMergeNode.getFieldMappings()) {
				if (StringUtil.isBlank(mapping.getSourceField())) {
					throw new RuntimeException("字段映射配置中源字段名不能为空");
				}
				if (StringUtil.isBlank(mapping.getTargetField())) {
					throw new RuntimeException("字段映射配置中目标字段名不能为空");
				}
				// 验证数据源节点是否存在（如果指定了的话）
				if (StringUtil.isNotBlank(mapping.getSourceNode()) &&
						!dataMergeNode.getSourceNodes().contains(mapping.getSourceNode())) {
					throw new RuntimeException(String.format("字段映射配置中指定的数据源节点 '%s' 不存在", mapping.getSourceNode()));
				}
			}
		}

		// 验证数据源标识配置
		if (Boolean.TRUE.equals(dataMergeNode.getAddSourceTag())) {
			if (StringUtil.isBlank(dataMergeNode.getSourceTagField())) {
				throw new RuntimeException("启用数据源标识时必须指定数据源标识字段名");
			}
		}
	}

	/**
	 * 初始化数据源
	 */
	private void initializeDataSources() {
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			// 等待依赖节点
			waitForDependNode(sourceNodeId);

			// 构建数据源key
			String sourceDataKey = getSourceDataKey(sourceNodeId);
			String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + sourceNodeId;

			sourceDataKeys.put(sourceNodeId, sourceDataKey);
			sourceStatusKeys.put(sourceNodeId, sourceStatusKey);
			sourceDataBuffers.put(sourceNodeId, new ArrayList<>());
		}
	}

	/**
	 * 实时合并策略
	 */
	private void processRealTimeStrategy(long startTime, Integer overTimes) {
		long lastStatusCheckTime = System.currentTimeMillis();
		int cycleCount = 0;

		while (true) {
			cycleCount++;

			// 检查是否需要停止
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			checkAllSourcesFailed();

			// 尝试从各个数据源获取数据
			boolean hasData = collectRealTimeData();

			if (hasData) {
				// 执行实时合并
				performRealTimeMerge();
			}

			// 定期检查和记录状态（每30秒）
			if (currentTime - lastStatusCheckTime > 30000) {
				Map<String, Object> syncStatus = checkDataSourceSyncStatus();
				appendLog(String.format("节点 【%s】 状态检查 - 循环次数：%s，缓冲区状态：%s，已完成数据源：%s/%s",
						pipelineNode.getProperties().getName(), cycleCount,
						syncStatus.get("bufferSizes"),
						syncStatus.get("completedSources"),
						syncStatus.get("totalSources")));
				lastStatusCheckTime = currentTime;
				updateLog();
			}

			// 检查是否所有数据源都已完成
			if (areAllSourcesCompleted()) {
				appendLog(String.format("节点 【%s】 所有数据源已完成，开始处理剩余数据", pipelineNode.getProperties().getName()));
				// 处理剩余数据
				processRemainingData();
				break;
			}

			// 短暂休眠
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				break;
			}
		}
	}

	/**
	 * 批量合并策略
	 */
	private void processBatchStrategy(long startTime, Integer overTimes) {
		long lastStatusCheckTime = System.currentTimeMillis();
		int cycleCount = 0;

		while (true) {
			cycleCount++;

			// 检查是否需要停止
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			checkAllSourcesFailed();

			// 收集批量数据
			boolean hasEnoughData = collectBatchData();

			if (hasEnoughData) {
				// 执行批量合并
				performBatchMerge();
				batchCount++;

				if (batchCount % 10 == 0) {
					appendLog(String.format("节点 【%s】 已处理 %d 个批次", pipelineNode.getProperties().getName(), batchCount));
					updateLog();
				}
			}

			// 定期检查和记录状态（每30秒）
			if (currentTime - lastStatusCheckTime > 30000) {
				Map<String, Object> syncStatus = checkDataSourceSyncStatus();
				appendLog(String.format("节点 【%s】 状态检查 - 循环次数：%d，批次数：%s，缓冲区状态：%s，已完成数据源：%s/%s",
						pipelineNode.getProperties().getName(), cycleCount, batchCount,
						syncStatus.get("bufferSizes"),
						syncStatus.get("completedSources"),
						syncStatus.get("totalSources")));
				lastStatusCheckTime = currentTime;
				updateLog();
			}

			// 检查是否所有数据源都已完成
			if (areAllSourcesCompleted()) {
				appendLog(String.format("节点 【%s】 所有数据源已完成，开始处理剩余批量数据", pipelineNode.getProperties().getName()));
				// 处理剩余数据
				processRemainingBatchData();
				break;
			}

			// 短暂休眠
			try {
				Thread.sleep(50);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				break;
			}
		}
	}


	/**
	 * 执行数据合并
	 */
	private List<Map<String, Object>> performDataMerge(Map<String, List<Map<String, Object>>> allSourceData) {
		List<Map<String, Object>> mergedData;

		switch (dataMergeNode.getMergeStrategy()) {
			case "UNION":
				mergedData = performUnionMerge(allSourceData);
				break;
			case "JOIN":
				mergedData = performJoinMerge(allSourceData);
				break;
			case "APPEND":
				mergedData = performAppendMerge(allSourceData);
				break;
			default:
				throw new RuntimeException("不支持的合并策略: " + dataMergeNode.getMergeStrategy());
		}

		// 应用数据处理选项
		mergedData = applyDataProcessingOptions(mergedData);

		return mergedData;
	}

	/**
	 * UNION合并策略 - 列合并
	 * 将多个数据源的字段合并到同一行中
	 */
	private List<Map<String, Object>> performUnionMerge(Map<String, List<Map<String, Object>>> allSourceData) {
		List<Map<String, Object>> result = new ArrayList<>();

		// 构建字段映射关系，确定哪些字段需要添加前缀
		List<String> sourceOrder = new ArrayList<>(allSourceData.keySet());
		Map<String, String> fieldMapping = buildFieldMapping(allSourceData, sourceOrder);

		// 获取所有数据源的最大行数
		int maxRows = 0;
		for (List<Map<String, Object>> sourceData : allSourceData.values()) {
			maxRows = Math.max(maxRows, sourceData.size());
		}

		// 按行索引合并各数据源的字段
		for (int rowIndex = 0; rowIndex < maxRows; rowIndex++) {
			Map<String, Object> mergedRow = new HashMap<>();

			// 遍历每个数据源
			for (Map.Entry<String, List<Map<String, Object>>> entry : allSourceData.entrySet()) {
				String sourceId = entry.getKey();
				List<Map<String, Object>> sourceData = entry.getValue();

				// 如果当前数据源在该行索引有数据
				if (rowIndex < sourceData.size()) {
					Map<String, Object> sourceRow = sourceData.get(rowIndex);

					// 将该数据源的所有字段添加到合并行中
					for (Map.Entry<String, Object> fieldEntry : sourceRow.entrySet()) {
						String originalFieldName = fieldEntry.getKey();
						Object fieldValue = fieldEntry.getValue();

						// 根据字段映射确定最终字段名
						String mappingKey = sourceId + "." + originalFieldName;
						String finalFieldName;

						finalFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);

						mergedRow.put(finalFieldName, fieldValue);
					}
				}
			}

			// 只有当合并行不为空时才添加到结果中
			if (!mergedRow.isEmpty()) {
				result.add(mergedRow);
			}
		}

		return result;
	}

	/**
	 * JOIN合并策略 - 支持多个数据源关联
	 */
	private List<Map<String, Object>> performJoinMerge(Map<String, List<Map<String, Object>>> allSourceData) {
		if (dataMergeNode.getJoinConditions() == null || dataMergeNode.getJoinConditions().isEmpty()) {
			throw new RuntimeException("JOIN策略必须配置关联条件");
		}

		// 构建关联链
		List<String> joinChain = buildJoinChain(dataMergeNode.getJoinConditions());

		// 分析所有数据源的字段，确定哪些字段需要添加前缀
		Map<String, String> fieldMapping = buildFieldMapping(allSourceData, joinChain);

		// 从第一个数据源开始，逐步与其他数据源进行关联
		String firstSourceId = joinChain.get(0);
		List<Map<String, Object>> firstSourceData = allSourceData.get(firstSourceId);
		if (firstSourceData == null) {
			appendLog(String.format("节点 【%s】 警告：数据源 %s 没有数据，返回空结果",
					pipelineNode.getProperties().getName(), firstSourceId));
			return new ArrayList<>();
		}
		List<Map<String, Object>> result = new ArrayList<>(firstSourceData);

		// 为第一个数据源的字段重命名（如果需要）
		result = renameFieldsInRows(result, joinChain.get(0), fieldMapping);

		// 依次与其他数据源进行关联
		for (int i = 1; i < joinChain.size(); i++) {
			String currentSource = joinChain.get(i);
			List<Map<String, Object>> currentData = allSourceData.get(currentSource);

			// 找到与当前数据源相关的关联条件
			DataMergeNode.JoinCondition joinCondition = findJoinCondition(joinChain.get(i - 1), currentSource);

			if (joinCondition != null) {
				result = performTwoTableJoin(result, currentData, joinCondition, currentSource, fieldMapping);
			}
		}

		appendLog(String.format("节点 【%s】 JOIN合并完成：输入数据源数量=%d，输出数据量=%d，JOIN类型=%s",
				pipelineNode.getProperties().getName(),
				allSourceData.size(),
				result.size(),
				dataMergeNode.getJoinType() != null ? dataMergeNode.getJoinType() : "INNER"));

		return result;
	}

	/**
	 * 构建关联链
	 */
	private List<String> buildJoinChain(List<DataMergeNode.JoinCondition> joinConditions) {

		if (joinConditions == null || joinConditions.isEmpty()) {
			appendLog(String.format("节点 【%s】 错误：JOIN条件为空", pipelineNode.getProperties().getName()));
			return new ArrayList<>();
		}

		List<String> chain = new ArrayList<>();
		Set<String> visited = new HashSet<>();

		// 从第一个关联条件开始构建链
		DataMergeNode.JoinCondition firstCondition = joinConditions.get(0);
		chain.add(firstCondition.getLeftSource());
		chain.add(firstCondition.getRightSource());
		visited.add(firstCondition.getLeftSource());
		visited.add(firstCondition.getRightSource());

		// 继续查找后续的关联
		boolean foundNext = true;
		while (foundNext && chain.size() < dataMergeNode.getSourceNodes().size()) {
			foundNext = false;
			String lastSource = chain.get(chain.size() - 1);

			for (DataMergeNode.JoinCondition condition : joinConditions) {
				// 查找以当前最后一个数据源为左表的关联条件
				if (lastSource.equals(condition.getLeftSource()) && !visited.contains(condition.getRightSource())) {
					chain.add(condition.getRightSource());
					visited.add(condition.getRightSource());
					foundNext = true;
					break;
				}
				// 查找以当前最后一个数据源为右表的关联条件
				else if (lastSource.equals(condition.getRightSource()) && !visited.contains(condition.getLeftSource())) {
					chain.add(condition.getLeftSource());
					visited.add(condition.getLeftSource());
					foundNext = true;
					break;
				}
			}
		}

		return chain;
	}

	/**
	 * 查找两个数据源之间的关联条件
	 */
	private DataMergeNode.JoinCondition findJoinCondition(String sourceA, String sourceB) {
		for (DataMergeNode.JoinCondition condition : dataMergeNode.getJoinConditions()) {
			if ((sourceA.equals(condition.getLeftSource()) && sourceB.equals(condition.getRightSource())) ||
					(sourceA.equals(condition.getRightSource()) && sourceB.equals(condition.getLeftSource()))) {
				return condition;
			}
		}
		return null;
	}

	/**
	 * 构建字段映射关系，确定哪些字段需要添加前缀
	 * 对于重名字段，只给后面的数据源加前缀，保持第一个数据源的字段名不变
	 *
	 * @param allSourceData 所有数据源的数据
	 * @param sourceOrder   数据源顺序列表
	 * @return 字段映射关系 Map
	 */
	private Map<String, String> buildFieldMapping(Map<String, List<Map<String, Object>>> allSourceData, List<String> sourceOrder) {
		Map<String, String> fieldMapping = new HashMap<>();
		Map<String, List<String>> fieldSourceMap = new LinkedHashMap<>();

		// 收集所有数据源的字段信息，保持数据源的顺序
		for (String sourceId : sourceOrder) {
			List<Map<String, Object>> sourceData = allSourceData.get(sourceId);
			if (sourceData != null && !sourceData.isEmpty()) {
				Map<String, Object> firstRow = sourceData.get(0);
				for (String fieldName : firstRow.keySet()) {
					fieldSourceMap.computeIfAbsent(fieldName, k -> new ArrayList<>()).add(sourceId);
				}
			}
		}

		// 为重名字段生成带前缀的映射
		int duplicateFieldCount = 0;
		for (Map.Entry<String, List<String>> entry : fieldSourceMap.entrySet()) {
			String fieldName = entry.getKey();
			List<String> sources = entry.getValue();

			if (sources.size() > 1) {
				duplicateFieldCount++;
				// 字段重名，第一个数据源保持原名，后续数据源添加前缀
				for (int i = 0; i < sources.size(); i++) {
					String sourceId = sources.get(i);
					String originalKey = sourceId + "." + fieldName;

					if (i == 0) {
						// 第一个数据源保持原字段名
						fieldMapping.put(originalKey, fieldName);
					} else {
						// 后续数据源添加前缀
						String mappedKey = sourceId + "_" + fieldName;
						fieldMapping.put(originalKey, mappedKey);
					}
				}
			} else {
				// 字段不重名，保持原名
				String sourceId = sources.get(0);
				String originalKey = sourceId + "." + fieldName;
				fieldMapping.put(originalKey, fieldName);
			}
		}

		// 记录字段映射信息
		if (duplicateFieldCount > 0) {
			appendLog(String.format("节点 【%s】 检测到 %d 个重名字段，已为后续数据源添加前缀",
					pipelineNode.getProperties().getName(), duplicateFieldCount));
		}

		return fieldMapping;
	}

	/**
	 * 根据字段映射重命名数据行中的字段
	 */
	private List<Map<String, Object>> renameFieldsInRows(List<Map<String, Object>> data, String sourceId, Map<String, String> fieldMapping) {
		List<Map<String, Object>> result = new ArrayList<>();

		for (Map<String, Object> row : data) {
			Map<String, Object> renamedRow = new HashMap<>();
			for (Map.Entry<String, Object> entry : row.entrySet()) {
				String originalFieldName = entry.getKey();
				String mappingKey = sourceId + "." + originalFieldName;
				String newFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);
				renamedRow.put(newFieldName, entry.getValue());
			}
			result.add(renamedRow);
		}

		return result;
	}

	/**
	 * 执行两表关联
	 */
	private List<Map<String, Object>> performTwoTableJoin(List<Map<String, Object>> leftData,
														  List<Map<String, Object>> rightData,
														  DataMergeNode.JoinCondition joinCondition,
														  String rightSourceId,
														  Map<String, String> fieldMapping) {
		// 默认JOIN类型为INNER
		String joinType = dataMergeNode.getJoinType();
		if (joinType == null || joinType.trim().isEmpty()) {
			joinType = "INNER";
			appendLog(String.format("节点 【%s】 JOIN类型未配置，默认使用INNER JOIN",
					pipelineNode.getProperties().getName()));
		}

		// 参数空值检查
		if (leftData == null) {
			appendLog(String.format("节点 【%s】 错误：左表数据为空", pipelineNode.getProperties().getName()));
			return new ArrayList<>();
		}
		if (rightData == null) {
			appendLog(String.format("节点 【%s】 错误：右表数据为空", pipelineNode.getProperties().getName()));
			return new ArrayList<>();
		}
		if (joinCondition == null) {
			appendLog(String.format("节点 【%s】 错误：JOIN条件为空", pipelineNode.getProperties().getName()));
			return new ArrayList<>();
		}
		if (joinCondition.getLeftSource() == null || joinCondition.getLeftField() == null ||
				joinCondition.getRightSource() == null || joinCondition.getRightField() == null) {
			appendLog(String.format("节点 【%s】 错误：JOIN条件字段不完整，左表源：%s，左表字段：%s，右表源：%s，右表字段：%s",
					pipelineNode.getProperties().getName(),
					joinCondition.getLeftSource(), joinCondition.getLeftField(),
					joinCondition.getRightSource(), joinCondition.getRightField()));
			return new ArrayList<>();
		}
		if (fieldMapping == null) {
			appendLog(String.format("节点 【%s】 警告：字段映射为空，使用默认映射", pipelineNode.getProperties().getName()));
			fieldMapping = new HashMap<>();
		}

		appendLog(String.format("节点 【%s】 执行%s JOIN：左表%d条，右表%d条，关联字段：%s.%s = %s.%s",
				pipelineNode.getProperties().getName(), joinType,
				leftData.size(), rightData.size(),
				joinCondition.getLeftSource(), joinCondition.getLeftField(),
				joinCondition.getRightSource(), joinCondition.getRightField()));

		List<Map<String, Object>> result = new ArrayList<>();

		for (Map<String, Object> leftRow : leftData) {
			boolean hasMatch = false;

			for (Map<String, Object> rightRow : rightData) {
				if (isMultiSourceJoinMatch(leftRow, rightRow, joinCondition, fieldMapping)) {
					Map<String, Object> joinedRow = new HashMap<>(leftRow);

					// 添加右表数据（根据字段映射决定是否需要前缀）
					for (Map.Entry<String, Object> entry : rightRow.entrySet()) {
						String originalFieldName = entry.getKey();
						String mappingKey = rightSourceId + "." + originalFieldName;
						String mappedFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);
						joinedRow.put(mappedFieldName, entry.getValue());
					}

					result.add(joinedRow);
					hasMatch = true;

					// 根据JOIN类型决定是否继续匹配
					String currentJoinType = dataMergeNode.getJoinType();
					if (currentJoinType == null) currentJoinType = "INNER";

					// INNER JOIN和LEFT JOIN找到匹配后跳出，避免一对多重复匹配
					if ("INNER".equals(currentJoinType) || "LEFT".equals(currentJoinType)) {
						break;
					}
					// RIGHT JOIN和FULL JOIN可能需要处理一对多关系，这里也建议跳出避免重复
					// 如果业务需要一对多，需要单独处理
					break;
				}
			}

			// 处理LEFT JOIN的情况
			if (!hasMatch && ("LEFT".equals(joinType) || "FULL".equals(joinType))) {
				Map<String, Object> joinedRow = new HashMap<>(leftRow);
				// 为右表字段添加空值
				if (!rightData.isEmpty()) {
					for (Map.Entry<String, Object> entry : rightData.get(0).entrySet()) {
						String originalFieldName = entry.getKey();
						String mappingKey = rightSourceId + "." + originalFieldName;
						String mappedFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);
						joinedRow.put(mappedFieldName, null);
					}
				}
				result.add(joinedRow);
			}
		}

		// 处理RIGHT JOIN和FULL JOIN的情况
		// 处理RIGHT JOIN和FULL JOIN的情况
		if ("RIGHT".equals(joinType) || "FULL".equals(joinType)) {
			if ("RIGHT".equals(joinType)) {
				// RIGHT JOIN：清空之前的结果，重新构建
				result.clear();

				// 遍历右表的每一行
				for (Map<String, Object> rightRow : rightData) {
					boolean hasMatch = false;
					Map<String, Object> matchedLeftRow = null;

					// 查找与右表匹配的左表数据
					for (Map<String, Object> leftRow : leftData) {
						if (isMultiSourceJoinMatch(leftRow, rightRow, joinCondition, fieldMapping)) {
							hasMatch = true;
							matchedLeftRow = leftRow;
							break;
						}
					}

					Map<String, Object> joinedRow = new HashMap<>();

					if (hasMatch) {
						// 有匹配：合并左表和右表数据
						joinedRow.putAll(matchedLeftRow);
					} else {
						// 无匹配：为左表字段添加空值
						if (!leftData.isEmpty()) {
							for (String fieldName : leftData.get(0).keySet()) {
								joinedRow.put(fieldName, null);
							}
						}
					}

					// 添加右表数据
					for (Map.Entry<String, Object> entry : rightRow.entrySet()) {
						String originalFieldName = entry.getKey();
						String mappingKey = rightSourceId + "." + originalFieldName;
						String mappedFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);
						joinedRow.put(mappedFieldName, entry.getValue());
					}

					result.add(joinedRow);
				}
			} else {
				// FULL JOIN：只需要添加右表中没有匹配的数据
				// 因为匹配的数据在前面的LEFT JOIN逻辑中已经处理了

				for (Map<String, Object> rightRow : rightData) {
					boolean hasMatch = false;

					// 检查这个右表行是否与任何左表行匹配
					for (Map<String, Object> leftRow : leftData) {
						if (isMultiSourceJoinMatch(leftRow, rightRow, joinCondition, fieldMapping)) {
							hasMatch = true;
							break;
						}
					}

					// 只有当右表行没有匹配时，才添加到结果中
					if (!hasMatch) {
						Map<String, Object> joinedRow = new HashMap<>();

						// 为左表字段添加空值
						if (!leftData.isEmpty()) {
							for (String fieldName : leftData.get(0).keySet()) {
								joinedRow.put(fieldName, null);
							}
						}

						// 添加右表数据
						for (Map.Entry<String, Object> entry : rightRow.entrySet()) {
							String originalFieldName = entry.getKey();
							String mappingKey = rightSourceId + "." + originalFieldName;
							String mappedFieldName = fieldMapping.getOrDefault(mappingKey, originalFieldName);
							joinedRow.put(mappedFieldName, entry.getValue());
						}

						result.add(joinedRow);
					}
				}
			}
		}

		appendLog(String.format("节点 【%s】 %s JOIN操作完成：左表%d条，右表%d条，结果%d条",
				pipelineNode.getProperties().getName(), joinType,
				leftData.size(), rightData.size(), result.size()));

		// 根据JOIN类型验证结果数量
		if ("RIGHT".equals(joinType)) {
			if (result.size() != rightData.size()) {
				appendLog(String.format("节点 【%s】 警告：RIGHT JOIN结果数量不正确，期望%d条，实际%d条",
						pipelineNode.getProperties().getName(), rightData.size(), result.size()));
			}
		} else if ("FULL".equals(joinType)) {
			// FULL JOIN的结果数量应该 >= max(leftData.size(), rightData.size()) 且 <= leftData.size() + rightData.size()
			int expectedMin = Math.max(leftData.size(), rightData.size());
			int expectedMax = leftData.size() + rightData.size();
			if (result.size() < expectedMin || result.size() > expectedMax) {
				appendLog(String.format("节点 【%s】 警告：FULL JOIN结果数量异常，期望范围[%d-%d]条，实际%d条",
						pipelineNode.getProperties().getName(), expectedMin, expectedMax, result.size()));
			} else {
				appendLog(String.format("节点 【%s】 FULL JOIN结果数量正常，期望范围[%d-%d]条，实际%d条",
						pipelineNode.getProperties().getName(), expectedMin, expectedMax, result.size()));
			}
		}
		return result;
	}

	/**
	 * 判断多数据源JOIN匹配
	 */
	private boolean isMultiSourceJoinMatch(Map<String, Object> leftRow, Map<String, Object> rightRow,
										   DataMergeNode.JoinCondition joinCondition, Map<String, String> fieldMapping) {
		// 获取左表字段的映射名称
		String leftMappingKey = joinCondition.getLeftSource() + "." + joinCondition.getLeftField();
		String leftFieldName = fieldMapping.getOrDefault(leftMappingKey, joinCondition.getLeftField());
		Object leftValue = leftRow.get(leftFieldName);

		// 右表字段名（原始字段名）
		Object rightValue = rightRow.get(joinCondition.getRightField());

		if (leftValue == null && rightValue == null) {
			return true;
		}

		return leftValue != null && leftValue.equals(rightValue);
	}

	/**
	 * 在行中查找字段（考虑字段映射）
	 */
	private String findFieldInRow(Map<String, Object> row, String sourceId, String fieldName) {
		// 直接使用字段名查找
		if (row.containsKey(fieldName)) {
			return fieldName;
		}

		// 尝试带前缀的字段名
		String prefixedFieldName = sourceId + "_" + fieldName;
		if (row.containsKey(prefixedFieldName)) {
			return prefixedFieldName;
		}

		// 遍历所有字段查找匹配的后缀
		for (String key : row.keySet()) {
			if (key.endsWith("_" + fieldName)) {
				return key;
			}
		}

		return fieldName; // 默认返回原字段名
	}

	/**
	 * APPEND合并策略
	 */
	private List<Map<String, Object>> performAppendMerge(Map<String, List<Map<String, Object>>> allSourceData) {
		List<Map<String, Object>> result = new ArrayList<>();

		// 按照数据源顺序依次追加
		for (String sourceId : dataMergeNode.getSourceNodes()) {
			List<Map<String, Object>> sourceData = allSourceData.get(sourceId);
			if (sourceData != null) {
				for (Map<String, Object> data : sourceData) {
					Map<String, Object> processedData = new HashMap<>(data);

					// 添加数据源标识
					if (Boolean.TRUE.equals(dataMergeNode.getAddSourceTag())) {
						processedData.put(dataMergeNode.getSourceTagField(), sourceId);
					}

					result.add(processedData);
				}
			}
		}

		return result;
	}

	/**
	 * 应用数据处理选项
	 */
	private List<Map<String, Object>> applyDataProcessingOptions(List<Map<String, Object>> data) {
		List<Map<String, Object>> result = new ArrayList<>(data);

		// 应用字段映射
		if (Boolean.TRUE.equals(dataMergeNode.getEnableFieldMapping()) &&
				dataMergeNode.getFieldMappings() != null && !dataMergeNode.getFieldMappings().isEmpty()) {

			appendLog(String.format("节点 【%s】 开始应用字段映射，映射规则数：%d",
					pipelineNode.getProperties().getName(), dataMergeNode.getFieldMappings().size()));

			result = applyFieldMapping(result);

			appendLog(String.format("节点 【%s】 字段映射应用完成", pipelineNode.getProperties().getName()));
		}

		// 去重处理
		if (Boolean.TRUE.equals(dataMergeNode.getRemoveDuplicates())) {
			int beforeCount = result.size();
			result = removeDuplicates(result);
			int afterCount = result.size();

			appendLog(String.format("节点 【%s】 去重处理完成，去重前：%d 条，去重后：%d 条",
					pipelineNode.getProperties().getName(), beforeCount, afterCount));
		}

		return result;
	}

	/**
	 * 应用字段映射
	 */
	private List<Map<String, Object>> applyFieldMapping(List<Map<String, Object>> data) {
		List<Map<String, Object>> result = new ArrayList<>();

		for (Map<String, Object> row : data) {

			// 先将原始数据复制到映射后的行中
			Map<String, Object> mappedRow = new HashMap<>(row);

			// 应用字段映射配置
			for (DataMergeNode.FieldMapping mapping : dataMergeNode.getFieldMappings()) {
				// 检查是否指定了数据源节点，如果指定了则需要匹配
				if (mapping.getSourceNode() != null && !mapping.getSourceNode().isEmpty()) {
					// 对于已合并的数据，我们需要检查字段是否来自指定的数据源
					// 这里通过字段前缀来判断（如果字段名包含数据源前缀）
					String sourcePrefix = mapping.getSourceNode() + "_";
					String actualSourceField = null;

					// 查找实际的源字段名
					if (row.containsKey(mapping.getSourceField())) {
						actualSourceField = mapping.getSourceField();
					} else if (row.containsKey(sourcePrefix + mapping.getSourceField())) {
						actualSourceField = sourcePrefix + mapping.getSourceField();
					} else {
						// 查找其他可能的前缀形式
						for (String key : row.keySet()) {
							if (key.endsWith("_" + mapping.getSourceField()) &&
									key.startsWith(mapping.getSourceNode())) {
								actualSourceField = key;
								break;
							}
						}
					}

					if (actualSourceField != null) {
						Object value = row.get(actualSourceField);
						if (value != null) {
							// 应用数据类型转换
							Object convertedValue = convertDataType(value, mapping.getDataType());
							mappedRow.put(mapping.getTargetField(), convertedValue);

							// 如果目标字段名与源字段名不同，则移除原字段
							if (!mapping.getTargetField().equals(actualSourceField)) {
								mappedRow.remove(actualSourceField);
							}
						}
					}
				} else {
					// 没有指定数据源节点，直接按字段名映射
					Object value = row.get(mapping.getSourceField());
					if (value != null) {
						// 应用数据类型转换
						Object convertedValue = convertDataType(value, mapping.getDataType());
						mappedRow.put(mapping.getTargetField(), convertedValue);

						// 如果目标字段名与源字段名不同，则移除原字段
						if (!mapping.getTargetField().equals(mapping.getSourceField())) {
							mappedRow.remove(mapping.getSourceField());
						}
					}
				}
			}

			result.add(mappedRow);
		}

		return result;
	}

	/**
	 * 数据类型转换
	 */
	private Object convertDataType(Object value, String targetType) {
		if (value == null || targetType == null || targetType.trim().isEmpty()) {
			return value;
		}

		try {
			switch (targetType.toUpperCase()) {
				case "STRING":
				case "VARCHAR":
				case "TEXT":
					return value.toString();
				case "INTEGER":
				case "INT":
					if (value instanceof Number) {
						return ((Number) value).intValue();
					}
					return Integer.valueOf(value.toString().trim());
				case "LONG":
				case "BIGINT":
					if (value instanceof Number) {
						return ((Number) value).longValue();
					}
					return Long.valueOf(value.toString().trim());
				case "DOUBLE":
				case "DECIMAL":
				case "NUMERIC":
					if (value instanceof Number) {
						return ((Number) value).doubleValue();
					}
					return Double.valueOf(value.toString().trim());
				case "FLOAT":
					if (value instanceof Number) {
						return ((Number) value).floatValue();
					}
					return Float.valueOf(value.toString().trim());
				case "BOOLEAN":
				case "BOOL":
					if (value instanceof Boolean) {
						return value;
					}
					String strValue = value.toString().trim().toLowerCase();
					return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
				case "DATETIME":
				case "TIMESTAMP":
					// 如果已经是日期类型，直接返回
					if (value instanceof Date || value instanceof java.time.LocalDateTime || value instanceof java.time.LocalDate) {
						return value;
					}
					// 对于字符串类型的日期，保持原值，由具体的数据库驱动处理
					return value.toString();
				case "DATE":
					// 如果已经是日期类型，直接返回
					if (value instanceof Date || value instanceof java.time.LocalDate) {
						return value;
					}
					// 对于字符串类型的日期，保持原值，由具体的数据库驱动处理
					return value.toString();
				case "TIME":
					// 如果已经是时间类型，直接返回
					if (value instanceof java.time.LocalTime || value instanceof java.sql.Time) {
						return value;
					}
					// 对于字符串类型的时间，保持原值，由具体的数据库驱动处理
					return value.toString();
				default:
					return value;
			}
		} catch (Exception e) {
			// 转换失败时返回原值，并记录警告日志
			appendLog(String.format("节点 【%s】 字段类型转换失败，字段值：%s，目标类型：%s，错误：%s", pipelineNode.getProperties().getName(),
					value, targetType, e.getMessage()));
			return value;
		}
	}

	/**
	 * 去重处理
	 */
	private List<Map<String, Object>> removeDuplicates(List<Map<String, Object>> data) {
		Set<String> seen = new HashSet<>();
		List<Map<String, Object>> result = new ArrayList<>();

		for (Map<String, Object> row : data) {
			String rowHash = calculateRowHash(row);
			if (!seen.contains(rowHash)) {
				seen.add(rowHash);
				result.add(row);
			}
		}

		return result;
	}

	/**
	 * 计算行哈希值
	 */
	private String calculateRowHash(Map<String, Object> row) {
		StringBuilder sb = new StringBuilder();
		for (Map.Entry<String, Object> entry : row.entrySet()) {
			sb.append(entry.getKey()).append(":").append(entry.getValue()).append(";");
		}
		return sb.toString();
	}

	/**
	 * 推送合并后的数据
	 */
	private void pushMergedData(List<Map<String, Object>> mergedData) {
		for (Map<String, Object> data : mergedData) {
			pushDataToDownstream(data);
			totalCount++;

			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}
		}
	}

	/**
	 * 收集实时数据
	 */
	private boolean collectRealTimeData() {
		boolean hasData = false;
		Map<String, Integer> sourceDataCount = new HashMap<>();
		Map<String, Integer> currentBufferSizes = new HashMap<>();

		// 先统计当前各数据源的缓冲区状态
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			int currentSize = sourceDataBuffers.get(sourceNodeId).size();
			currentBufferSizes.put(sourceNodeId, currentSize);
		}

		// 计算当前缓冲区的最小和最大值
		int currentMinBuffer = currentBufferSizes.values().stream().mapToInt(Integer::intValue).min().orElse(0);
		int currentMaxBuffer = currentBufferSizes.values().stream().mapToInt(Integer::intValue).max().orElse(0);

		// 根据JOIN类型调整收集策略
		String joinType = dataMergeNode.getJoinType();
		if (joinType == null) joinType = "INNER";

		// 识别右表数据源（用于RIGHT JOIN）
		String rightSourceId = null;
		if ("RIGHT".equals(joinType) && dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
			rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
		}

		// 动态调整收集策略
		int maxCollectCount = 10; // 默认最多收集10条

		// 如果缓冲区不平衡，对数据少的数据源多收集一些
		if (currentMaxBuffer - currentMinBuffer > 5) {
			appendLog(String.format("节点 【%s】 检测到缓冲区不平衡，调整收集策略", pipelineNode.getProperties().getName()));
		}

		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			String dataKey = sourceDataKeys.get(sourceNodeId);
			int count = 0;
			int currentBufferSize = currentBufferSizes.get(sourceNodeId);

			// 根据JOIN类型和数据源类型调整收集数量
			int targetCollectCount = maxCollectCount;

			if ("RIGHT".equals(joinType) && sourceNodeId.equals(rightSourceId)) {
				// RIGHT JOIN的右表：优先保证数据收集
				targetCollectCount = Math.max(maxCollectCount, Math.min(25, maxCollectCount + 10));
			} else if ("FULL".equals(joinType)) {
				// FULL JOIN：每个数据源都尽量多收集
				targetCollectCount = Math.max(maxCollectCount, Math.min(20, maxCollectCount + 5));
			} else {
				// INNER JOIN和LEFT JOIN：使用原有的平衡策略
				if (currentBufferSize < currentMinBuffer + 2) {
					targetCollectCount = Math.max(maxCollectCount, Math.min(20, maxCollectCount + 5));
				} else if (currentBufferSize > currentMaxBuffer - 2) {
					targetCollectCount = Math.max(1, maxCollectCount - 3);
				}
			}

			// 尝试获取数据
			for (int i = 0; i < targetCollectCount; i++) {
				Map<String, Object> data = memoryManager.rightPop(dataKey);
				if (data != null) {
					sourceDataBuffers.get(sourceNodeId).add(data);
					hasData = true;
					count++;
				} else {
					break;
				}
			}

			sourceDataCount.put(sourceNodeId, count);
		}

		// 记录数据收集情况
		if (hasData) {
			StringBuilder logMsg = new StringBuilder();
			logMsg.append(String.format("节点 【%s】 实时数据收集：", pipelineNode.getProperties().getName()));

			for (Map.Entry<String, Integer> entry : sourceDataCount.entrySet()) {
				String sourceId = entry.getKey();
				int collectedCount = entry.getValue();
				int totalBufferSize = sourceDataBuffers.get(sourceId).size();

				if (collectedCount > 0) {
					logMsg.append(String.format(" %s:+%d条(总计%d)", sourceId, collectedCount, totalBufferSize));
				}
			}

			// 只有在收集了较多数据或缓冲区不平衡时才记录详细日志
			if (sourceDataCount.values().stream().mapToInt(Integer::intValue).sum() >= 5 ||
					currentMaxBuffer - currentMinBuffer > 3) {
				appendLog(logMsg.toString());
			}
		}

		return hasData;
	}

	/**
	 * 执行实时合并
	 */
	private void performRealTimeMerge() {

		Map<String, List<Map<String, Object>>> currentData = new HashMap<>();
		Map<String, Integer> bufferSizes = new HashMap<>();
		int minBufferSize = Integer.MAX_VALUE;
		int maxBufferSize = 0;
		boolean hasAnyData = false;

		// 统计各数据源的缓冲区数据量
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);
			int bufferSize = buffer.size();
			bufferSizes.put(sourceNodeId, bufferSize);

			if (bufferSize > 0) {
				hasAnyData = true;
				minBufferSize = Math.min(minBufferSize, bufferSize);
				maxBufferSize = Math.max(maxBufferSize, bufferSize);
			} else {
				// 检查该数据源是否已经完成
				String statusKey = sourceStatusKeys.get(sourceNodeId);
				Integer status = memoryManager.getNodeStatus(statusKey);
				if (CommonRunStatus.isEnd(status)) {
					// 已完成的数据源，缓冲区大小视为0，但不影响最小值计算
					minBufferSize = Math.min(minBufferSize, 0);
				}
			}
		}

		// 如果没有任何数据，直接返回
		if (!hasAnyData) {
			return;
		}

		// 检查是否应该进行实时合并
		boolean shouldMerge = false;

		// 策略1：如果所有活跃数据源都有数据，且数据量相对平衡
		if (minBufferSize > 0) {
			shouldMerge = true;
		} else {
			// 策略2：如果有数据源已完成，且其他数据源有数据
			for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
				String statusKey = sourceStatusKeys.get(sourceNodeId);
				Integer status = memoryManager.getNodeStatus(statusKey);

				if (!CommonRunStatus.isEnd(status)) {
					// 如果活跃数据源没有数据，不进行合并
					if (bufferSizes.get(sourceNodeId) == 0) {
						shouldMerge = false;
						break;
					} else {
						shouldMerge = true;
					}
				}
			}
		}

		if (shouldMerge) {
			// 根据JOIN类型决定合并策略
			int realTimeMinSize;
			String joinType = dataMergeNode.getJoinType();
			if (joinType == null) joinType = "INNER";

			if ("RIGHT".equals(joinType)) {
				// RIGHT JOIN：确保右表数据完整，识别右表数据源
				String rightSourceId = null;
				if (dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
					rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
				}

				if (rightSourceId != null) {
					int rightBufferSize = bufferSizes.get(rightSourceId);
					realTimeMinSize = Math.max(1, rightBufferSize);
					appendLog(String.format("节点 【%s】 RIGHT JOIN模式，以右表(%s)数据量为准：%d条",
							pipelineNode.getProperties().getName(), rightSourceId, realTimeMinSize));
				} else {
					// 无法识别右表，使用最大值
					realTimeMinSize = maxBufferSize;
					appendLog(String.format("节点 【%s】 RIGHT JOIN模式，无法识别右表，以最大数据量为准：%d条",
							pipelineNode.getProperties().getName(), realTimeMinSize));
				}
			} else if ("FULL".equals(joinType)) {
				// FULL JOIN：确保所有数据完整，以最大数据量为准
				realTimeMinSize = maxBufferSize;
				appendLog(String.format("节点 【%s】 FULL JOIN模式，以最大数据量为准：%d条",
						pipelineNode.getProperties().getName(), realTimeMinSize));
			} else {
				// INNER JOIN和LEFT JOIN：使用平衡策略
				realTimeMinSize = Math.max(1, minBufferSize);
			}

			// 记录数据不平衡情况
			if (minBufferSize != maxBufferSize && minBufferSize > 0) {
				appendLog(String.format("节点 【%s】 实时合并数据不平衡，最小：%d，最大：%d，将平衡取出：%d条",
						pipelineNode.getProperties().getName(), minBufferSize, maxBufferSize, realTimeMinSize));
			}

			// 根据JOIN类型决定数据取出策略
			// 根据JOIN类型决定数据取出策略
			for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
				List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);
				List<Map<String, Object>> portion = new ArrayList<>();

				int targetSize;
				if ("RIGHT".equals(joinType)) {
					// RIGHT JOIN：识别右表数据源
					String rightSourceId = null;
					if (dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
						rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
					}

					if (sourceNodeId.equals(rightSourceId)) {
						// 这是右表，取所有可用数据（不超过实时最大值）
						targetSize = Math.min(realTimeMinSize, buffer.size());
					} else {
						// 这是左表，取最小值
						targetSize = Math.min(minBufferSize, buffer.size());
					}
				} else if ("FULL".equals(joinType)) {
					// FULL JOIN：每个数据源都取实际可用数据，但不超过最大值
					targetSize = Math.min(realTimeMinSize, buffer.size());
				} else {
					// INNER JOIN和LEFT JOIN：平衡策略
					targetSize = Math.min(realTimeMinSize, buffer.size());
				}

				// 取出指定数量的数据
				for (int i = 0; i < targetSize && !buffer.isEmpty(); i++) {
					portion.add(buffer.remove(0));
				}

				// 只有当有数据时才加入合并
				if (!portion.isEmpty()) {
					currentData.put(sourceNodeId, portion);
				}
			}

			if (!currentData.isEmpty()) {
				List<Map<String, Object>> mergedData = performDataMerge(currentData);
				pushMergedData(mergedData);

				appendLog(String.format("节点 【%s】 实时合并完成，参与合并的数据源数：%d，每个数据源数据量：%d，合并数据行数：%d",
						pipelineNode.getProperties().getName(), currentData.size(), realTimeMinSize, mergedData.size()));
			}
		}
	}

	/**
	 * 收集批量数据
	 */
	private boolean collectBatchData() {
		Map<String, Integer> sourceBufferSizes = new HashMap<>();
		int minBufferSize = Integer.MAX_VALUE;
		int maxBufferSize = 0;

		// 根据JOIN类型确定收集策略
		String joinType = dataMergeNode.getJoinType();
		if (joinType == null) joinType = "INNER";

		// 识别右表数据源
		String rightSourceId = null;
		if ("RIGHT".equals(joinType) && dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
			rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
		}

		// 收集各数据源的数据到缓冲区
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			String dataKey = sourceDataKeys.get(sourceNodeId);
			List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);

			// 根据JOIN类型确定目标收集大小
			int targetBatchSize = dataMergeNode.getBatchSize();

			if ("RIGHT".equals(joinType) && sourceNodeId.equals(rightSourceId)) {
				// RIGHT JOIN的右表：确保收集足够的数据
				targetBatchSize = Math.max(dataMergeNode.getBatchSize(), dataMergeNode.getBatchSize() + 5);
			} else if ("FULL".equals(joinType)) {
				// FULL JOIN：每个数据源都尽量收集更多数据
				targetBatchSize = Math.max(dataMergeNode.getBatchSize(), dataMergeNode.getBatchSize() + 3);
			}

			// 从队列中获取数据到缓冲区
			while (buffer.size() < targetBatchSize) {
				Map<String, Object> data = memoryManager.rightPop(dataKey);
				if (data == null) {
					break;
				}
				buffer.add(data);
			}

			int bufferSize = buffer.size();
			sourceBufferSizes.put(sourceNodeId, bufferSize);
			minBufferSize = Math.min(minBufferSize, bufferSize);
			maxBufferSize = Math.max(maxBufferSize, bufferSize);
		}

		// 检查是否所有数据源都有足够的数据进行批量合并
		// 检查是否所有数据源都有足够的数据进行批量合并
		boolean allSourcesReady = true;
		if (joinType == null) joinType = "INNER";

		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			String statusKey = sourceStatusKeys.get(sourceNodeId);
			Integer status = memoryManager.getNodeStatus(statusKey);
			int bufferSize = sourceBufferSizes.get(sourceNodeId);

			// 根据JOIN类型调整就绪条件
			boolean sourceReady = false;

			if (CommonRunStatus.isEnd(status)) {
				// 数据源已完成，认为就绪
				sourceReady = true;
			} else {
				// 数据源未完成，检查缓冲区数据
				if ("RIGHT".equals(joinType)) {
					// RIGHT JOIN：右表需要有数据，左表可以较少
					if (sourceNodeId.equals(rightSourceId)) {
						// 右表：需要有足够的数据
						sourceReady = bufferSize >= dataMergeNode.getBatchSize();
					} else {
						// 左表：有数据即可
						sourceReady = bufferSize > 0;
					}
				} else if ("FULL".equals(joinType)) {
					// FULL JOIN：每个数据源都需要有数据
					sourceReady = bufferSize > 0;
				} else {
					// INNER JOIN和LEFT JOIN：使用原有逻辑
					sourceReady = bufferSize >= dataMergeNode.getBatchSize();
				}
			}

			if (!sourceReady) {
				allSourcesReady = false;
				break;
			}
		}

		// 记录缓冲区状态
		if (minBufferSize != maxBufferSize) {
			appendLog(String.format("节点 【%s】 数据源缓冲区不平衡，最小：%d，最大：%d",
					pipelineNode.getProperties().getName(), minBufferSize, maxBufferSize));
		}

		return allSourcesReady && minBufferSize > 0;
	}

	/**
	 * 执行批量合并
	 */
	private void performBatchMerge() {
		Map<String, List<Map<String, Object>>> batchData = new HashMap<>();
		int minBufferSize = Integer.MAX_VALUE;
		int maxBufferSize = 0;

		// 统计各数据源的缓冲区大小
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);
			int bufferSize = buffer.size();
			minBufferSize = Math.min(minBufferSize, bufferSize);
			maxBufferSize = Math.max(maxBufferSize, bufferSize);
		}

		// 根据JOIN类型决定批量合并策略
		int batchSize;
		String joinType = dataMergeNode.getJoinType();
		if (joinType == null) joinType = "INNER";

		if ("RIGHT".equals(joinType)) {
			// RIGHT JOIN：确保右表数据完整，以右表数据量为准
			batchSize = Math.min(dataMergeNode.getBatchSize(), maxBufferSize);
			appendLog(String.format("节点 【%s】 批量RIGHT JOIN模式，以右表数据量为准：%d条",
					pipelineNode.getProperties().getName(), batchSize));
		} else if ("FULL".equals(joinType)) {
			// FULL JOIN：确保所有数据完整，以最大数据量为准
			batchSize = Math.min(dataMergeNode.getBatchSize(), maxBufferSize);
			appendLog(String.format("节点 【%s】 批量FULL JOIN模式，以最大数据量为准：%d条",
					pipelineNode.getProperties().getName(), batchSize));
		} else {
			// INNER JOIN和LEFT JOIN：使用平衡策略
			batchSize = Math.min(dataMergeNode.getBatchSize(), minBufferSize);
		}

		// 记录数据不平衡情况
		if (minBufferSize != maxBufferSize && minBufferSize > 0) {
			appendLog(String.format("节点 【%s】 批量合并数据不平衡，最小：%d，最大：%d，将取出：%d条",
					pipelineNode.getProperties().getName(), minBufferSize, maxBufferSize, batchSize));
		}

		// 根据JOIN类型决定数据取出策略
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);
			List<Map<String, Object>> batchPortion = new ArrayList<>();

			int targetSize;
			if ("RIGHT".equals(joinType)) {
				// RIGHT JOIN：识别右表数据源
				String rightSourceId = null;
				if (dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
					rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
				}

				if (rightSourceId != null && sourceNodeId.equals(rightSourceId)) {
					// 这是右表，取所有可用数据（不超过批量大小）
					targetSize = Math.min(batchSize, buffer.size());
				} else {
					// 这是左表，取最小值
					targetSize = Math.min(minBufferSize, buffer.size());
				}
			} else if ("FULL".equals(joinType)) {
				// FULL JOIN：每个数据源都取实际可用数据，但不超过批量大小
				targetSize = Math.min(batchSize, buffer.size());
			} else {
				// INNER JOIN和LEFT JOIN：平衡策略
				targetSize = Math.min(batchSize, buffer.size());
			}

			// 取出指定数量的数据
			for (int i = 0; i < targetSize && !buffer.isEmpty(); i++) {
				batchPortion.add(buffer.remove(0));
			}

			if (!batchPortion.isEmpty()) {
				batchData.put(sourceNodeId, batchPortion);
			}
		}

		if (!batchData.isEmpty()) {
			List<Map<String, Object>> mergedData = performDataMerge(batchData);
			pushMergedData(mergedData);

			// 统计实际取出的数据量
			StringBuilder sizeInfo = new StringBuilder();
			for (Map.Entry<String, List<Map<String, Object>>> entry : batchData.entrySet()) {
				if (sizeInfo.length() > 0) sizeInfo.append(", ");
				sizeInfo.append(entry.getKey()).append(":").append(entry.getValue().size());
			}

			appendLog(String.format("节点 【%s】 批量合并完成，参与合并的数据源数：%d，各数据源数据量：[%s]，合并数据行数：%d",
					pipelineNode.getProperties().getName(), batchData.size(), sizeInfo.toString(), mergedData.size()));
		}
	}

	/**
	 * 检查所有数据源是否完成
	 */
	private boolean areAllSourcesCompleted() {
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			String statusKey = sourceStatusKeys.get(sourceNodeId);
			Integer status = memoryManager.getNodeStatus(statusKey);
			if (!CommonRunStatus.isSuccess(status)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 检查数据源同步状态
	 *
	 * @return 返回同步状态信息
	 */
	private Map<String, Object> checkDataSourceSyncStatus() {
		Map<String, Object> syncStatus = new HashMap<>();
		Map<String, Integer> bufferSizes = new HashMap<>();
		Map<String, Integer> nodeStatuses = new HashMap<>();
		Map<String, Long> queueSizes = new HashMap<>();

		int totalBufferSize = 0;
		int completedSources = 0;

		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			// 缓冲区大小
			int bufferSize = sourceDataBuffers.get(sourceNodeId).size();
			bufferSizes.put(sourceNodeId, bufferSize);
			totalBufferSize += bufferSize;

			// 节点状态
			String statusKey = sourceStatusKeys.get(sourceNodeId);
			Integer status = memoryManager.getNodeStatus(statusKey);
			nodeStatuses.put(sourceNodeId, status);

			if (CommonRunStatus.isEnd(status)) {
				completedSources++;
			}

			// 队列大小
			String dataKey = sourceDataKeys.get(sourceNodeId);
			long queueSize = memoryManager.getQueueSize(dataKey);
			queueSizes.put(sourceNodeId, queueSize);
		}

		syncStatus.put("bufferSizes", bufferSizes);
		syncStatus.put("nodeStatuses", nodeStatuses);
		syncStatus.put("queueSizes", queueSizes);
		syncStatus.put("totalBufferSize", totalBufferSize);
		syncStatus.put("completedSources", completedSources);
		syncStatus.put("totalSources", dataMergeNode.getSourceNodes().size());

		return syncStatus;
	}

	/**
	 * 处理剩余数据
	 */
	private void processRemainingData() {
		appendLog(String.format("节点 【%s】 开始处理剩余数据", pipelineNode.getProperties().getName()));

		// 根据JOIN类型调整剩余数据处理策略
		String joinType = dataMergeNode.getJoinType();
		if (joinType == null) joinType = "INNER";

		// 识别右表数据源
		String rightSourceId = null;
		if ("RIGHT".equals(joinType) && dataMergeNode.getJoinConditions() != null && !dataMergeNode.getJoinConditions().isEmpty()) {
			rightSourceId = dataMergeNode.getJoinConditions().get(0).getRightSource();
		}

		Map<String, List<Map<String, Object>>> remainingData = new HashMap<>();
		Map<String, Integer> remainingCounts = new HashMap<>();

		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			String dataKey = sourceDataKeys.get(sourceNodeId);
			List<Map<String, Object>> buffer = sourceDataBuffers.get(sourceNodeId);
			List<Map<String, Object>> remaining = new ArrayList<>(buffer);

			// 获取队列中剩余的数据
			long queueSize = memoryManager.getQueueSize(dataKey);

			// 对于RIGHT JOIN的右表，确保获取所有剩余数据
			if ("RIGHT".equals(joinType) && sourceNodeId.equals(rightSourceId)) {
				appendLog(String.format("节点 【%s】 RIGHT JOIN右表剩余数据处理，队列大小：%d",
						pipelineNode.getProperties().getName(), queueSize));
			}

			for (int i = 0; i < queueSize; i++) {
				Map<String, Object> data = memoryManager.rightPop(dataKey);
				if (data != null) {
					remaining.add(data);
				}
			}

			if (!remaining.isEmpty()) {
				remainingData.put(sourceNodeId, remaining);
				remainingCounts.put(sourceNodeId, remaining.size());
			}
		}

		if (!remainingData.isEmpty()) {
			// 记录剩余数据情况
			StringBuilder logMsg = new StringBuilder();
			logMsg.append(String.format("节点 【%s】 剩余数据处理：", pipelineNode.getProperties().getName()));
			for (Map.Entry<String, Integer> entry : remainingCounts.entrySet()) {
				logMsg.append(String.format(" %s:%d条", entry.getKey(), entry.getValue()));
			}
			appendLog(logMsg.toString());

			List<Map<String, Object>> mergedData = performDataMerge(remainingData);
			pushMergedData(mergedData);

			appendLog(String.format("节点 【%s】 剩余数据处理完成，合并数据行数：%d",
					pipelineNode.getProperties().getName(), mergedData.size()));
		} else {
			appendLog(String.format("节点 【%s】 无剩余数据需要处理", pipelineNode.getProperties().getName()));
		}
	}

	/**
	 * 处理剩余批量数据
	 */
	private void processRemainingBatchData() {
		processRemainingData();
	}

	/**
	 * 检查所有数据源失败
	 */
	private void checkAllSourcesFailed() {
		for (String sourceNodeId : dataMergeNode.getSourceNodes()) {
			dependFailedCheck(sourceNodeId);
		}
	}
}
