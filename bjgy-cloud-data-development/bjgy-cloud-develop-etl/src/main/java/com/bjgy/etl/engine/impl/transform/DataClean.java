 /*
  * Copyright (c) 2024 天津数睿通科技有限公司
  * All rights reserved.
  */
 package com.bjgy.etl.engine.impl.transform;

 import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
 import com.bjgy.dto.PipelineNode;
 import com.bjgy.etl.engine.EtlEngine;
 import com.bjgy.etl.node.transform.DataCleanNode;
 import com.bjgy.flink.common.utils.ThreadUtil;
 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;
 import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
 import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

 import javax.script.ScriptEngine;
 import javax.script.ScriptEngineManager;
 import javax.script.ScriptException;
 import java.math.BigDecimal;
 import java.sql.Connection;
 import java.sql.DriverManager;
 import java.sql.PreparedStatement;
 import java.sql.ResultSet;
 import java.sql.ResultSetMetaData;
 import java.sql.SQLException;
 import java.sql.Statement;
 import java.text.DecimalFormat;
 import java.text.ParseException;
 import java.text.SimpleDateFormat;
 import java.util.ArrayList;
 import java.util.Arrays;
 import java.util.Date;
 import java.util.HashMap;
 import java.util.HashSet;
 import java.util.List;
 import java.util.Map;
 import java.util.Set;
 import java.util.concurrent.ConcurrentHashMap;
 import java.util.concurrent.ExecutorService;
 import java.util.concurrent.Executors;
 import java.util.concurrent.Future;
 import java.util.regex.Pattern;

 /**
  * 数据清洗组件处理逻辑
  * 支持标准清洗和自定义清洗两种模式
  */
 public class DataClean extends EtlEngine {

	 private static final Logger logger = LoggerFactory.getLogger(DataClean.class);

	 public DataClean(PipelineNode pipelineNode) {
		 super(pipelineNode);
	 }

	 private DataCleanNode dataCleanNode;
	 private String sourceDataKey;
	 private String sourceStatusKey;
	 private long totalCount = 0;
	 private long cleanedCount = 0;
	 private long removedCount = 0;
	 private long errorCount = 0;
	 private List<Map<String, Object>> dataBuffer = new ArrayList<>();
	 private ExecutorService executorService;
	 private ScriptEngine jsEngine;

	 // SQL清洗相关
	 private Connection h2Connection;
	 private String tempTableName;

	 // 统计信息
	 private final Map<String, Long> cleaningStatistics = new ConcurrentHashMap<>();

	 // 去重相关
	 private final Set<String> deduplicationKeys = new HashSet<>();
	 private final Map<String, Map<String, Object>> latestRecords = new HashMap<>();

	 // 格式化器
	 private final List<SimpleDateFormat> dateFormatters = Arrays.asList(
			 new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
			 new SimpleDateFormat("yyyy-MM-dd"),
			 new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"),
			 new SimpleDateFormat("yyyy/MM/dd"),
			 new SimpleDateFormat("MM/dd/yyyy"),
			 new SimpleDateFormat("dd/MM/yyyy")
	 );

	 @Override
	 public void run() {
		 // 初始化缓存
		 initCache();
		 long startTime = System.currentTimeMillis();

		 try {
			 Integer overTimes = pipelineNodeEntity.getOverTimes();
			 dataCleanNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, DataCleanNode.class);

			 // 验证配置
			 validateConfiguration();

			 // 初始化数据源
			 initializeDataSource();

			 // 初始化清洗引擎
			 initializeCleaningEngine();

			 appendLog(String.format("节点 【%s】 开始运行，清洗模式：%s，批处理大小：%d",
					 pipelineNode.getProperties().getName(),
					 dataCleanNode.getCleanMode(),
					 dataCleanNode.getBatchSize()));

			 // 开始数据清洗处理
			 processDataCleaning(startTime, overTimes);

			 // 输出统计信息
			 if (Boolean.TRUE.equals(dataCleanNode.getOutputStatistics())) {
				 outputCleaningStatistics();
			 }

			 appendLog(String.format("节点 【%s】 运行成功结束，总处理数据：%d 条，清洗通过：%d 条，移除：%d 条，错误：%d 条",
					 pipelineNode.getProperties().getName(), totalCount, cleanedCount, removedCount, errorCount));
			 successEnd();

		 } catch (Exception e) {
			 appendLog(String.format("节点 【%s】 运行失败结束，总处理数据：%d 条，错误信息：%s",
					 pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			 failEnd();
		 } finally {
			 // 关闭线程池
			 if (executorService != null && !executorService.isShutdown()) {
				 executorService.shutdown();
			 }
			 // 清理SQL资源
			 cleanupSqlResources();
			 // 清理去重相关的内存
			 clearDeduplicationMemory();
		 }
	 }

	 /**
	  * 验证配置
	  */
	 private void validateConfiguration() {
		 if (StringUtil.isBlank(dataCleanNode.getDependNodeNo())) {
			 throw new RuntimeException("数据清洗器必须配置数据源节点");
		 }

		 if (StringUtil.isBlank(dataCleanNode.getCleanMode())) {
			 throw new RuntimeException("必须选择清洗模式");
		 }

		 if ("STANDARD".equals(dataCleanNode.getCleanMode())) {
			 // 验证标准清洗配置
			 validateStandardCleaningConfig();
		 } else if ("CUSTOM".equals(dataCleanNode.getCleanMode())) {
			 // 验证自定义清洗配置
			 validateCustomCleaningConfig();
		 }

		 // 设置默认值
		 if (dataCleanNode.getBatchSize() == null || dataCleanNode.getBatchSize() <= 0) {
			 dataCleanNode.setBatchSize(1000);
		 }

		 if (StringUtil.isBlank(dataCleanNode.getErrorHandling())) {
			 dataCleanNode.setErrorHandling("SKIP");
		 }
	 }

	 /**
	  * 验证标准清洗配置
	  */
	 private void validateStandardCleaningConfig() {
		 // 验证去除字段配置
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableFieldRemoval()) && StringUtil.isBlank(dataCleanNode.getFieldsToRemove())) {
			 throw new RuntimeException("启用去除字段功能时必须指定要去除的字段");
		 }

		 // 验证去重配置
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableDeduplication())) {
			 if ("KEY_FIELDS".equals(dataCleanNode.getDeduplicationStrategy()) && StringUtil.isBlank(dataCleanNode.getKeyFields())) {
				 throw new RuntimeException("关键字段去重模式下必须指定关键字段");
			 }
			 if ("LATEST".equals(dataCleanNode.getKeepStrategy()) && StringUtil.isBlank(dataCleanNode.getCompareField())) {
				 throw new RuntimeException("保留最新数据策略下必须指定比较字段");
			 }
		 }

		 // 验证空值处理配置
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableNullHandling()) && "FILL_DEFAULT".equals(dataCleanNode.getNullHandlingStrategy())) {
			 if (dataCleanNode.getDefaultValues() == null || dataCleanNode.getDefaultValues().isEmpty()) {
				 throw new RuntimeException("填充默认值模式下至少需要配置一个字段的默认值");
			 }
		 }

		 // 验证格式化规则
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableFormatting()) && dataCleanNode.getFormatRules() != null) {
			 for (DataCleanNode.FormatRule rule : dataCleanNode.getFormatRules()) {
				 if (StringUtil.isBlank(rule.getFieldName())) {
					 throw new RuntimeException("格式化规则的字段名不能为空");
				 }
			 }
		 }

		 // 验证异常值规则
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableOutlierHandling()) && dataCleanNode.getOutlierRules() != null) {
			 for (DataCleanNode.OutlierRule rule : dataCleanNode.getOutlierRules()) {
				 if (StringUtil.isBlank(rule.getFieldName())) {
					 throw new RuntimeException("异常值规则的字段名不能为空");
				 }
			 }
		 }
	 }

	 /**
	  * 验证自定义清洗配置
	  */
	 private void validateCustomCleaningConfig() {
		 if (StringUtil.isBlank(dataCleanNode.getScriptType())) {
			 throw new RuntimeException("自定义清洗模式下必须选择脚本类型");
		 }

		 if ("JAVASCRIPT".equals(dataCleanNode.getScriptType()) && StringUtil.isBlank(dataCleanNode.getJavascriptCode())) {
			 throw new RuntimeException("JavaScript清洗脚本不能为空");
		 }

		 if ("SQL".equals(dataCleanNode.getScriptType()) && StringUtil.isBlank(dataCleanNode.getSqlCode())) {
			 throw new RuntimeException("SQL清洗语句不能为空");
		 }

		 // Python脚本暂不实现
		 if ("PYTHON".equals(dataCleanNode.getScriptType())) {
			 throw new RuntimeException("Python脚本清洗功能暂未实现");
		 }
	 }

	 /**
	  * 初始化数据源
	  */
	 private void initializeDataSource() {
		 String dependNodeNo = dataCleanNode.getDependNodeNo();

		 // 等待依赖节点
		 waitForDependNode(dependNodeNo);

		 // 构建数据源key
		 sourceDataKey = getSourceDataKey(dependNodeNo);
		 sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dependNodeNo;
	 }

	 /**
	  * 初始化清洗引擎
	  */
	 private void initializeCleaningEngine() {
		 // 初始化并行处理
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableParallelProcessing())) {
			 int threadCount = Boolean.TRUE.equals(dataCleanNode.getEnableMemoryOptimization()) ?
					 Runtime.getRuntime().availableProcessors() / 2 :
					 Runtime.getRuntime().availableProcessors();
			 executorService = Executors.newFixedThreadPool(threadCount);
		 }

		 // 初始化JavaScript引擎
		 if ("CUSTOM".equals(dataCleanNode.getCleanMode()) && "JAVASCRIPT".equals(dataCleanNode.getScriptType())) {
			 ScriptEngineManager manager = new ScriptEngineManager();
			 jsEngine = manager.getEngineByName("javascript");
			 if (jsEngine == null) {
				 throw new RuntimeException("JavaScript引擎初始化失败");
			 }
		 }

		 // 初始化SQL引擎
		 if ("CUSTOM".equals(dataCleanNode.getCleanMode()) && "SQL".equals(dataCleanNode.getScriptType())) {
			 initializeSqlEngine();
		 }

		 // 初始化统计信息
		 cleaningStatistics.put("fieldRemoval", 0L);
		 cleaningStatistics.put("deduplication", 0L);
		 cleaningStatistics.put("nullHandling", 0L);
		 cleaningStatistics.put("formatting", 0L);
		 cleaningStatistics.put("outlierHandling", 0L);
	 }

	 /**
	  * 初始化SQL引擎
	  */
	 private void initializeSqlEngine() {
		 try {
			 // 加载H2驱动
			 Class.forName("org.h2.Driver");

			 // 创建内存数据库连接
			 String dbUrl = "jdbc:h2:mem:data_clean_" + System.currentTimeMillis() + ";DB_CLOSE_DELAY=-1;MODE=MySQL";
			 h2Connection = DriverManager.getConnection(dbUrl, "sa", "");

			 // 生成临时表名
			 tempTableName = "temp_data_" + System.currentTimeMillis();

			 appendLog(String.format("节点【%s】SQL清洗引擎初始化成功，数据库连接已建立", pipelineNode.getProperties().getName()));

		 } catch (ClassNotFoundException e) {
			 throw new RuntimeException("H2数据库驱动未找到，请检查依赖配置", e);
		 } catch (SQLException e) {
			 throw new RuntimeException("初始化H2数据库连接失败: " + e.getMessage(), e);
		 }
	 }

	 /**
	  * 处理数据清洗
	  */
	 private void processDataCleaning(long startTime, Integer overTimes) {
		 long lastStatusCheckTime = System.currentTimeMillis();
		 int cycleCount = 0;

		 while (true) {
			 cycleCount++;

			 // 检查是否需要停止
			 if (isStop()) {
				 throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			 }

			 // 检查超时
			 long currentTime = System.currentTimeMillis();
			 if (checkOverTime(startTime, currentTime, overTimes)) {
				 appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				 break;
			 }

			 // 检查依赖节点失败
			 dependFailedCheck(dataCleanNode.getDependNodeNo());

			 // 收集数据进行批处理
			 boolean hasData = collectDataForBatch();

			 if (hasData) {
				 // 处理批量数据清洗
				 processBatchCleaning();
			 } else {
				 // 检查依赖节点状态
				 Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				 if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					 // 处理剩余数据
					 processRemainingData();
					 break;
				 }
			 }

			 // 定期检查状态和输出日志
			 if (currentTime - lastStatusCheckTime > 5000) {
				 appendLog(String.format("节点 【%s】 已处理 %d 条数据，当前批次循环：%d",
						 pipelineNode.getProperties().getName(), totalCount, cycleCount));
				 lastStatusCheckTime = currentTime;
			 }
		 }
	 }

	 /**
	  * 收集数据进行批处理
	  */
	 private boolean collectDataForBatch() {
		 boolean hasData = false;
		 while (dataBuffer.size() < dataCleanNode.getBatchSize()) {
			 Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			 if (data != null) {
				 dataBuffer.add(data);
				 hasData = true;
			 } else {
				 // 检查依赖节点状态
				 Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				 if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					 // 依赖节点已完成，继续获取队列中剩余数据
					 while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						 dataBuffer.add(data);
						 hasData = true;
						 // 如果缓冲区满了，先返回处理
						 if (dataBuffer.size() >= dataCleanNode.getBatchSize()) {
							 break;
						 }
					 }
					 // 无论是否还有更多数据，都需要退出收集循环
					 break;
				 } else {
					 // 依赖节点未完成，退出收集，等待下次调用
					 ThreadUtil.sleep(20);
					 break;
				 }
			 }
		 }
		 return hasData;
	 }

	 /**
	  * 处理批量数据清洗
	  */
	 private void processBatchCleaning() {
		 if (dataBuffer.isEmpty()) {
			 return;
		 }

		 List<Map<String, Object>> currentBatch = new ArrayList<>(dataBuffer);
		 dataBuffer.clear();

		 // 如果是SQL清洗模式，进行批量SQL处理
		 if ("CUSTOM".equals(dataCleanNode.getCleanMode()) && "SQL".equals(dataCleanNode.getScriptType())) {
			 processSqlBatchCleaning(currentBatch);
		 } else {
			 // 标准清洗或JavaScript清洗
			 if (Boolean.TRUE.equals(dataCleanNode.getEnableParallelProcessing()) && executorService != null) {
				 processDataInParallel(currentBatch);
			 } else {
				 processDataSequentially(currentBatch);
			 }
		 }
	 }

	 /**
	  * 处理SQL批量清洗
	  */
	 private void processSqlBatchCleaning(List<Map<String, Object>> dataList) {
		 try {
			 totalCount += dataList.size();

			 // 执行SQL批量处理
			 List<Map<String, Object>> cleanedResults = processSqlBatch(dataList);

			 boolean keepOriginalData = Boolean.TRUE.equals(dataCleanNode.getKeepOriginalData());

			 // 处理清洗结果
			 for (Map<String, Object> cleanedResult : cleanedResults) {
				 Map<String, Object> finalResult = new HashMap<>();

				 // 添加清洗后的数据（排除原始数据字段）
				 for (Map.Entry<String, Object> entry : cleanedResult.entrySet()) {
					 if (!"_ORIGINAL_DATA".equals(entry.getKey())) {
						 finalResult.put(entry.getKey(), entry.getValue());
					 }
				 }

				 // 如果需要保留原始数据，直接输出JSON字符串
				 if (keepOriginalData && cleanedResult.containsKey("_ORIGINAL_DATA")) {
					 String originalDataJson = (String) cleanedResult.get("_ORIGINAL_DATA");
					 if (originalDataJson != null && !originalDataJson.trim().isEmpty()) {
						 finalResult.put("_original_data", originalDataJson);
					 }
				 }

				 // 推送清洗后的数据到下游节点
				 pushDataToDownstream(finalResult);
				 cleanedCount++;
			 }

			 // 计算被移除的记录数
			 removedCount += dataList.size() - cleanedResults.size();

		 } catch (Exception e) {
			 // SQL清洗失败时的错误处理
			 errorCount += dataList.size();
			 String errorMsg = String.format("SQL批量清洗错误：%s", e.getMessage());

			 if ("STOP".equals(dataCleanNode.getErrorHandling())) {
				 throw new RuntimeException(errorMsg);
			 } else if ("LOG".equals(dataCleanNode.getErrorHandling())) {
				 appendLog(errorMsg);
			 }
			 // SKIP模式：忽略错误，继续处理
		 }
	 }

	 /**
	  * 并行处理数据
	  */
	 private void processDataInParallel(List<Map<String, Object>> dataList) {
		 int batchSize = dataList.size() / Runtime.getRuntime().availableProcessors() + 1;
		 List<Future<List<Map<String, Object>>>> futures = new ArrayList<>();

		 for (int i = 0; i < dataList.size(); i += batchSize) {
			 int endIndex = Math.min(i + batchSize, dataList.size());
			 List<Map<String, Object>> subList = dataList.subList(i, endIndex);

			 Future<List<Map<String, Object>>> future = executorService.submit(() -> {
				 List<Map<String, Object>> cleanedData = new ArrayList<>();
				 for (Map<String, Object> data : subList) {
					 try {
						 Map<String, Object> result = cleanData(data);
						 if (result != null) {
							 cleanedData.add(result);
						 }
					 } catch (Exception e) {
						 handleProcessingError(data, e);
					 }
				 }
				 return cleanedData;
			 });

			 futures.add(future);
		 }

		 // 收集结果
		 for (Future<List<Map<String, Object>>> future : futures) {
			 try {
				 List<Map<String, Object>> cleanedData = future.get();
				 for (Map<String, Object> data : cleanedData) {
					 pushDataToDownstream(data);
					 cleanedCount++;
				 }
			 } catch (Exception e) {
				 appendLog(String.format("并行处理异常：%s", e.getMessage()));
				 errorCount++;
			 }
		 }
	 }

	 /**
	  * 顺序处理数据
	  */
	 private void processDataSequentially(List<Map<String, Object>> dataList) {
		 for (Map<String, Object> data : dataList) {
			 try {
				 totalCount++;
				 Map<String, Object> result = cleanData(data);
				 if (result != null) {
					 pushDataToDownstream(result);
					 cleanedCount++;
				 } else {
					 removedCount++;
				 }
			 } catch (Exception e) {
				 handleProcessingError(data, e);
			 }
		 }
	 }

	 /**
	  * 清洗单条数据
	  */
	 private Map<String, Object> cleanData(Map<String, Object> data) throws Exception {
		 if (data == null) {
			 return null;
		 }

		 Map<String, Object> originalData = Boolean.TRUE.equals(dataCleanNode.getKeepOriginalData()) ?
				 new HashMap<>(data) : null;

		 Map<String, Object> cleanedData = new HashMap<>(data);

		 if ("STANDARD".equals(dataCleanNode.getCleanMode())) {
			 cleanedData = applyStandardCleaning(cleanedData);
		 } else if ("CUSTOM".equals(dataCleanNode.getCleanMode())) {
			 cleanedData = applyCustomCleaning(cleanedData);
		 }

		 // 如果需要保留原始数据
		 if (originalData != null && cleanedData != null) {
			 cleanedData.put("_original_data", StringUtil.toJson(originalData));
		 }

		 return cleanedData;
	 }

	 /**
	  * 应用标准清洗
	  */
	 private Map<String, Object> applyStandardCleaning(Map<String, Object> data) {
		 // 1. 去除字段
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableFieldRemoval())) {
			 data = removeFields(data);
			 if (data == null) return null;
		 }

		 // 2. 数据去重
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableDeduplication())) {
			 if (!checkDeduplication(data)) {
				 removedCount++;
				 cleaningStatistics.put("deduplication", cleaningStatistics.get("deduplication") + 1);
				 return null; // 重复数据，丢弃
			 }
		 }

		 // 3. 空值处理
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableNullHandling())) {
			 data = handleNullValues(data);
			 if (data == null) return null;
		 }

		 // 4. 数据格式化
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableFormatting())) {
			 formatData(data);
		 }

		 // 5. 异常值处理
		 if (Boolean.TRUE.equals(dataCleanNode.getEnableOutlierHandling())) {
			 data = handleOutliers(data);
		 }

		 return data;
	 }

	 /**
	  * 去除字段
	  */
	 private Map<String, Object> removeFields(Map<String, Object> data) {
		 if (StringUtil.isBlank(dataCleanNode.getFieldsToRemove())) {
			 return data;
		 }

		 String[] fieldsToRemove = dataCleanNode.getFieldsToRemove().split(",");
		 Map<String, Object> result = new HashMap<>(data);

		 for (String field : fieldsToRemove) {
			 String trimmedField = field.trim();
			 if (result.containsKey(trimmedField)) {
				 result.remove(trimmedField);
				 cleaningStatistics.put("fieldRemoval", cleaningStatistics.get("fieldRemoval") + 1);
			 }
		 }

		 return result;
	 }

	 /**
	  * 检查去重
	  */
	 private boolean checkDeduplication(Map<String, Object> data) {
		 String deduplicationKey;

		 if ("ALL_FIELDS".equals(dataCleanNode.getDeduplicationStrategy())) {
			 // 全字段去重
			 deduplicationKey = data.toString();
		 } else {
			 // 关键字段去重
			 StringBuilder keyBuilder = new StringBuilder();
			 String[] keyFields = dataCleanNode.getKeyFields().split(",");
			 for (String field : keyFields) {
				 String trimmedField = field.trim();
				 Object value = data.get(trimmedField);
				 keyBuilder.append(trimmedField).append("=").append(value).append(";");
			 }
			 deduplicationKey = keyBuilder.toString();
		 }

		 String keepStrategy = dataCleanNode.getKeepStrategy();

		 if (deduplicationKeys.contains(deduplicationKey)) {
			 // 重复数据，根据保留策略处理
			 if ("FIRST".equals(keepStrategy)) {
				 // 保留第一个，丢弃后续重复数据
				 return false;
			 } else if ("LAST".equals(keepStrategy)) {
				 // 保留最后一个，更新记录（用于最后统一输出）
				 latestRecords.put(deduplicationKey, data);
				 return false; // 暂时不输出，等待最后处理
			 } else if ("LATEST".equals(keepStrategy)) {
				 // 保留最新的，需要比较时间戳
				 Map<String, Object> existingRecord = latestRecords.get(deduplicationKey);
				 if (existingRecord == null || isNewerRecord(data, existingRecord)) {
					 // 当前数据更新或首次出现，替换已存储的记录
					 latestRecords.put(deduplicationKey, data);
				 }
				 return false; // 暂时不输出，等待最后处理
			 }
			 return false; // 默认丢弃重复数据
		 } else {
			 // 首次出现的数据
			 deduplicationKeys.add(deduplicationKey);

			 if ("FIRST".equals(keepStrategy)) {
				 // FIRST策略：直接通过，不需要存储
				 return true;
			 } else if ("LAST".equals(keepStrategy) || "LATEST".equals(keepStrategy)) {
				 // LAST/LATEST策略：存储数据，等待最后处理
				 latestRecords.put(deduplicationKey, data);
				 return false; // 暂时不输出，等待最后处理
			 }

			 return true; // 默认通过
		 }
	 }

	 /**
	  * 判断当前记录是否比已存在的记录更新
	  */
	 private boolean isNewerRecord(Map<String, Object> currentRecord, Map<String, Object> existingRecord) {
		 String timestampField = dataCleanNode.getCompareField();
		 if (StringUtil.isBlank(timestampField)) {
			 // 如果没有配置比较字段，默认认为当前记录更新
			 return true;
		 }

		 Object currentValue = currentRecord.get(timestampField);
		 Object existingValue = existingRecord.get(timestampField);

		 // 如果任一值为空，默认当前记录更新
		 if (currentValue == null || existingValue == null) {
			 return currentValue != null;
		 }

		 try {
			 // 直接比较字段值的大小
			 return compareValues(currentValue, existingValue) > 0;
		 } catch (Exception e) {
			 // 值比较失败时，记录警告并默认当前记录更新
			 logger.warn("节点【{}】比较字段值失败，字段：{}，当前值：{}，已存在值：{}，错误：{}",
					 pipelineNode.getProperties().getName(), timestampField,
					 currentValue, existingValue, e.getMessage());
			 return true;
		 }
	 }

	 /**
	  * 比较两个值的大小
	  *
	  * @return 正数表示value1更大，负数表示value2更大，0表示相等
	  */
	 private int compareValues(Object value1, Object value2) {
		 // 如果两个值类型相同且实现了Comparable接口，直接比较
		 if (value1.getClass().equals(value2.getClass()) && value1 instanceof Comparable) {
			 return ((Comparable<Object>) value1).compareTo(value2);
		 }

		 // 如果都是数字类型，转换为Double进行比较
		 if (value1 instanceof Number && value2 instanceof Number) {
			 Double num1 = ((Number) value1).doubleValue();
			 Double num2 = ((Number) value2).doubleValue();
			 return num1.compareTo(num2);
		 }

		 // 尝试转换为字符串进行比较
		 String str1 = value1.toString();
		 String str2 = value2.toString();
		 return str1.compareTo(str2);
	 }

	 /**
	  * 处理空值
	  */
	 private Map<String, Object> handleNullValues(Map<String, Object> data) {
		 String strategy = dataCleanNode.getNullHandlingStrategy();

		 if ("REMOVE".equals(strategy)) {
			 // 检查是否有空值，有则移除整条记录
			 for (Object value : data.values()) {
				 if (value == null || "".equals(value)) {
					 cleaningStatistics.put("nullHandling", cleaningStatistics.get("nullHandling") + 1);
					 return null;
				 }
			 }
		 } else if ("FILL_DEFAULT".equals(strategy)) {
			 // 用默认值填充
			 if (dataCleanNode.getDefaultValues() != null) {
				 for (DataCleanNode.DefaultValue defaultValue : dataCleanNode.getDefaultValues()) {
					 String fieldName = defaultValue.getFieldName();
					 Object currentValue = data.get(fieldName);

					 if (currentValue == null || "".equals(currentValue)) {
						 Object newValue = convertDefaultValue(defaultValue.getDefaultValue(), defaultValue.getDataType());
						 data.put(fieldName, newValue);
						 cleaningStatistics.put("nullHandling", cleaningStatistics.get("nullHandling") + 1);
					 }
				 }
			 }
		 }
		 // TODO: 实现FILL_PREVIOUS和FILL_AVERAGE策略

		 return data;
	 }

	 /**
	  * 格式化数据
	  */
	 private Map<String, Object> formatData(Map<String, Object> data) {
		 if (dataCleanNode.getFormatRules() == null) {
			 return data;
		 }

		 for (DataCleanNode.FormatRule rule : dataCleanNode.getFormatRules()) {
			 String fieldName = rule.getFieldName();
			 Object value = data.get(fieldName);

			 if (value != null) {
				 Object formattedValue = applyFormatRule(value, rule);
				 if (formattedValue != null) {
					 data.put(fieldName, formattedValue);
					 cleaningStatistics.put("formatting", cleaningStatistics.get("formatting") + 1);
				 }
			 }
		 }

		 return data;
	 }

	 /**
	  * 应用格式化规则
	  */
	 private Object applyFormatRule(Object value, DataCleanNode.FormatRule rule) {
		 String stringValue = value.toString();

		 switch (rule.getFormatType()) {
			 case "TRIM":
				 return stringValue.trim();
			 case "UPPER_CASE":
				 return stringValue.toUpperCase();
			 case "LOWER_CASE":
				 return stringValue.toLowerCase();
			 case "CAPITALIZE":
				 return StringUtil.isNotBlank(stringValue) ?
						 stringValue.substring(0, 1).toUpperCase() + stringValue.substring(1).toLowerCase() : stringValue;
			 case "DATE_FORMAT":
				 return formatDate(stringValue, rule.getFormatPattern());
			 case "NUMBER_FORMAT":
				 return formatNumber(stringValue, rule.getFormatPattern());
			 case "REGEX_REPLACE":
				 if (StringUtil.isNotBlank(rule.getRegexPattern())) {
					 return stringValue.replaceAll(rule.getRegexPattern(), rule.getReplacement() != null ? rule.getReplacement() : "");
				 }
				 break;
		 }

		 return value;
	 }

	 /**
	  * 处理异常值
	  */
	 private Map<String, Object> handleOutliers(Map<String, Object> data) {
		 if (dataCleanNode.getOutlierRules() == null) {
			 return data;
		 }

		 for (DataCleanNode.OutlierRule rule : dataCleanNode.getOutlierRules()) {
			 String fieldName = rule.getFieldName();
			 Object value = data.get(fieldName);

			 if (value != null && isOutlier(value, rule)) {
				 if ("REMOVE".equals(rule.getHandleMethod())) {
					 cleaningStatistics.put("outlierHandling", cleaningStatistics.get("outlierHandling") + 1);
					 return null; // 移除整条记录
				 } else if ("REPLACE".equals(rule.getHandleMethod())) {
					 Object replaceValue = convertDefaultValue(rule.getReplaceValue(), "STRING");
					 data.put(fieldName, replaceValue);
					 cleaningStatistics.put("outlierHandling", cleaningStatistics.get("outlierHandling") + 1);
				 } else if ("FLAG".equals(rule.getHandleMethod())) {
					 data.put(fieldName + "_outlier_flag", true);
					 cleaningStatistics.put("outlierHandling", cleaningStatistics.get("outlierHandling") + 1);
				 }
			 }
		 }

		 return data;
	 }

	 /**
	  * 检查是否为异常值
	  */
	 private boolean isOutlier(Object value, DataCleanNode.OutlierRule rule) {
		 switch (rule.getDetectionMethod()) {
			 case "RANGE":
				 if (StringUtil.isNotBlank(rule.getMinValue()) && StringUtil.isNotBlank(rule.getMaxValue())) {
					 try {
						 double numValue = Double.parseDouble(value.toString());
						 double minValue = Double.parseDouble(rule.getMinValue());
						 double maxValue = Double.parseDouble(rule.getMaxValue());
						 return numValue < minValue || numValue > maxValue;
					 } catch (NumberFormatException e) {
						 return false;
					 }
				 }
				 break;
			 case "REGEX":
				 if (StringUtil.isNotBlank(rule.getRegexPattern())) {
					 Pattern pattern = Pattern.compile(rule.getRegexPattern());
					 return !pattern.matcher(value.toString()).matches();
				 }
				 break;
			 // TODO: 实现STANDARD_DEVIATION和IQR检测方法
		 }

		 return false;
	 }

	 /**
	  * 应用自定义清洗
	  */
	 private Map<String, Object> applyCustomCleaning(Map<String, Object> data) throws Exception {
		 if ("JAVASCRIPT".equals(dataCleanNode.getScriptType())) {
			 return executeJavaScriptCleaning(data);
		 } else if ("SQL".equals(dataCleanNode.getScriptType())) {
			 return executeSqlCleaning(data);
		 }

		 return data;
	 }

	 /**
	  * 执行JavaScript清洗
	  */
	 @SuppressWarnings("unchecked")
	 private Map<String, Object> executeJavaScriptCleaning(Map<String, Object> data) throws ScriptException {
		 // 创建数据的副本，避免原数据被意外修改
		 Map<String, Object> dataCopy = new HashMap<>(data);

		 try {
			 // 将数据转换为JSON字符串传递给JavaScript引擎（传对象js无法正确修改值）
			 String jsonData = StringUtil.toJson(dataCopy);
			 jsEngine.put("recordJson", jsonData);

			 // 构建包装后的JavaScript代码，自动处理JSON转换
			 String wrappedJsCode = buildWrappedJavaScriptCode(dataCleanNode.getJavascriptCode());

			 // 执行包装后的JavaScript代码
			 jsEngine.eval(wrappedJsCode);

			 // 调用包装后的函数并获取结果
			 Object result = jsEngine.eval("wrappedCleanData(recordJson)");

			 if (result == null) {
				 return null;
			 }

			 // 如果返回的是JSON字符串，解析为Map
			 if (result instanceof String) {
				 String resultJson = (String) result;
				 if ("null".equals(resultJson) || StringUtil.isBlank(resultJson)) {
					 return null;
				 }
				 try {
					 return StringUtil.fromJson(resultJson, Map.class);
					 // 恢复原始数据类型 没必要恢复，入库会根据字段类型再做转换
					 //return restoreDataTypes(resultMap, dataCopy);
				 } catch (Exception e) {
					 appendLog(String.format("节点【%s】解析JavaScript返回的JSON失败: %s",
							 pipelineNode.getProperties().getName(), e.getMessage()));
					 return dataCopy;
				 }
			 }

			 // 如果返回的是Map类型，直接返回
			 if (result instanceof Map) {
				 return (Map<String, Object>) result;
			 }

			 // 如果都获取不到，返回原数据副本
			 return dataCopy;

		 } catch (ScriptException e) {
			 // 记录JavaScript执行错误的详细信息
			 appendLog(String.format("节点【%s】JavaScript清洗执行错误: %s, 数据: %s",
					 pipelineNode.getProperties().getName(), e.getMessage(), data.toString()));
			 throw e;
		 }
	 }

	 /**
	  * 构建包装后的JavaScript代码，自动处理JSON转换
	  */
	 private String buildWrappedJavaScriptCode(String userJsCode) {
		 StringBuilder wrappedCode = new StringBuilder();

		 // 添加包装函数
		 wrappedCode.append("function wrappedCleanData(recordJson) {\n");
		 wrappedCode.append("  // 自动解析JSON为JavaScript对象\n");
		 wrappedCode.append("  var record = JSON.parse(recordJson);\n");
		 wrappedCode.append("  \n");

		 // 检查用户代码是否已经包含cleanData函数定义
		 if (userJsCode.contains("function cleanData")) {
			 // 用户代码包含完整的cleanData函数定义
			 wrappedCode.append("  // 用户定义的cleanData函数\n");
			 wrappedCode.append("  ").append(userJsCode).append("\n");
			 wrappedCode.append("  \n");
			 wrappedCode.append("  // 调用用户的cleanData函数\n");
			 wrappedCode.append("  var result = cleanData(record);\n");
		 } else {
			 // 用户代码是直接的处理逻辑，包装成cleanData函数
			 wrappedCode.append("  // 包装用户的处理逻辑\n");
			 wrappedCode.append("  function cleanData(record) {\n");
			 wrappedCode.append("    ").append(userJsCode.replace("\n", "\n    ")).append("\n");
			 wrappedCode.append("    return record;\n");
			 wrappedCode.append("  }\n");
			 wrappedCode.append("  \n");
			 wrappedCode.append("  // 调用包装后的cleanData函数\n");
			 wrappedCode.append("  var result = cleanData(record);\n");
		 }

		 wrappedCode.append("  \n");
		 wrappedCode.append("  // 自动转换结果为JSON字符串返回\n");
		 wrappedCode.append("  if (result === null || result === undefined) {\n");
		 wrappedCode.append("    return null;\n");
		 wrappedCode.append("  }\n");
		 wrappedCode.append("  return JSON.stringify(result);\n");
		 wrappedCode.append("}\n");

		 return wrappedCode.toString();
	 }

	 /**
	  * 执行SQL清洗
	  */
	 private Map<String, Object> executeSqlCleaning(Map<String, Object> data) {
		 // SQL清洗在批量处理中进行，这里直接返回原数据
		 // 实际的SQL处理在processSqlBatchCleaning中执行
		 return data;
	 }

	 /**
	  * 处理SQL批量数据
	  */
	 private List<Map<String, Object>> processSqlBatch(List<Map<String, Object>> batchData) throws SQLException {
		 if (batchData.isEmpty()) {
			 return new ArrayList<>();
		 }

		 List<Map<String, Object>> results;

		 try {
			 // 1. 创建临时表
			 Map<String, String> fieldNameMapping = createTempTable(batchData.get(0));

			 // 2. 插入批量数据（如果需要保留原始数据，会在此步骤中添加原始数据字段）
			 insertBatchData(batchData);

			 // 3. 执行用户SQL
			 results = executeUserSql(fieldNameMapping);

		 } finally {
			 // 4. 清理临时表
			 dropTempTable();
		 }

		 return results;
	 }

	 /**
	  * 创建临时表
	  */
	 private Map<String, String> createTempTable(Map<String, Object> sampleData) throws SQLException {
		 StringBuilder createSql = new StringBuilder();
		 createSql.append("CREATE TEMPORARY TABLE ").append(tempTableName).append(" (");

		 // 用于存储大写字段名到原始字段名的映射
		 Map<String, String> fieldNameMapping = new HashMap<>();

		 boolean first = true;
		 for (Map.Entry<String, Object> entry : sampleData.entrySet()) {
			 if (!first) {
				 createSql.append(", ");
			 }

			 String originalFieldName = entry.getKey();
			 String columnType = inferColumnType(entry.getValue());

			 createSql.append("`").append(originalFieldName).append("` ").append(columnType);

			 // 记录字段名映射（H2会将字段名转为大写）
			 fieldNameMapping.put(originalFieldName.toUpperCase(), originalFieldName);

			 first = false;
		 }

		 // 如果需要保留原始数据，添加原始数据字段
		 if (Boolean.TRUE.equals(dataCleanNode.getKeepOriginalData())) {
			 createSql.append(", `_original_data` TEXT");
			 fieldNameMapping.put("_ORIGINAL_DATA", "_original_data");
		 }

		 createSql.append(")");

		 try (Statement stmt = h2Connection.createStatement()) {
			 stmt.execute(createSql.toString());
		 }

		 return fieldNameMapping;
	 }

	 /**
	  * 推断列类型
	  */
	 private String inferColumnType(Object value) {
		 if (value == null) {
			 return "VARCHAR(1000)";
		 }

		 if (value instanceof Integer || value instanceof Long) {
			 return "BIGINT";
		 } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
			 return "DECIMAL(20,6)";
		 } else if (value instanceof Boolean) {
			 return "BOOLEAN";
		 } else if (value instanceof Date) {
			 return "TIMESTAMP";
		 } else {
			 return "VARCHAR(1000)";
		 }
	 }

	 /**
	  * 插入批量数据
	  */
	 private void insertBatchData(List<Map<String, Object>> batchData) throws SQLException {
		 if (batchData.isEmpty()) {
			 return;
		 }

		 Map<String, Object> sampleData = batchData.get(0);
		 List<String> columnNames = new ArrayList<>(sampleData.keySet());

		 // 如果需要保留原始数据，添加原始数据字段
		 boolean keepOriginalData = Boolean.TRUE.equals(dataCleanNode.getKeepOriginalData());
		 if (keepOriginalData) {
			 columnNames.add("_original_data");
		 }

		 // 构建INSERT语句
		 StringBuilder insertSql = new StringBuilder();
		 insertSql.append("INSERT INTO ").append(tempTableName).append(" (");

		 for (int i = 0; i < columnNames.size(); i++) {
			 if (i > 0) insertSql.append(", ");
			 insertSql.append("`").append(columnNames.get(i)).append("`");
		 }

		 insertSql.append(") VALUES (");
		 for (int i = 0; i < columnNames.size(); i++) {
			 if (i > 0) insertSql.append(", ");
			 insertSql.append("?");
		 }
		 insertSql.append(")");

		 try (PreparedStatement pstmt = h2Connection.prepareStatement(insertSql.toString())) {
			 for (Map<String, Object> data : batchData) {
				 // 插入原始字段数据
				 for (int i = 0; i < sampleData.size(); i++) {
					 String columnName = (String) sampleData.keySet().toArray()[i];
					 Object value = data.get(columnName);
					 pstmt.setObject(i + 1, value);
				 }

				 // 如果需要保留原始数据，插入原始数据JSON
				 if (keepOriginalData) {
					 String originalDataJson = StringUtil.toJson(data);
					 pstmt.setString(sampleData.size() + 1, originalDataJson);
				 }

				 pstmt.addBatch();
			 }
			 pstmt.executeBatch();
		 }
	 }

	 /**
	  * 执行用户SQL
	  */
	 private List<Map<String, Object>> executeUserSql(Map<String, String> fieldNameMapping) throws SQLException {
		 List<Map<String, Object>> results = new ArrayList<>();

		 // 验证和预处理SQL
		 String userSql = dataCleanNode.getSqlCode().trim();
		 if (!isValidSelectSql(userSql)) {
			 throw new SQLException("只支持SELECT语句进行数据清洗");
		 }

		 // 替换表名占位符
		 userSql = userSql.replaceAll("\\betl_input_data\\b", tempTableName);

		 try (Statement stmt = h2Connection.createStatement();
			  ResultSet rs = stmt.executeQuery(userSql)) {

			 ResultSetMetaData metaData = rs.getMetaData();
			 int columnCount = metaData.getColumnCount();

			 while (rs.next()) {
				 Map<String, Object> row = new HashMap<>();
				 for (int i = 1; i <= columnCount; i++) {
					 String h2ColumnName = metaData.getColumnLabel(i);
					 Object value;
					 if ("_original_data".equalsIgnoreCase(h2ColumnName)) {
						 value = rs.getString(i);
					 } else {
						 value = rs.getObject(i);
					 }

					 // 使用映射恢复原始字段名，如果映射中没有则使用H2返回的字段名
					 String originalFieldName = fieldNameMapping.getOrDefault(h2ColumnName, h2ColumnName);
					 row.put(originalFieldName, value);
				 }
				 results.add(row);
			 }
		 }

		 return results;
	 }

	 /**
	  * 验证SQL是否为有效的SELECT语句
	  */
	 private boolean isValidSelectSql(String sql) {
		 if (StringUtil.isBlank(sql)) {
			 return false;
		 }

		 String upperSql = sql.trim().toUpperCase();

		 // 必须以SELECT开始
		 if (!upperSql.startsWith("SELECT")) {
			 return false;
		 }

		 // 不能包含危险的关键字
		 String[] dangerousKeywords = {
				 "DROP", "DELETE", "INSERT", "UPDATE", "CREATE", "ALTER",
				 "TRUNCATE", "EXEC", "EXECUTE", "CALL", "MERGE"
		 };

		 for (String keyword : dangerousKeywords) {
			 if (upperSql.contains(keyword)) {
				 return false;
			 }
		 }

		 return true;
	 }

	 /**
	  * 删除临时表
	  */
	 private void dropTempTable() {
		 try (Statement stmt = h2Connection.createStatement()) {
			 stmt.execute("DROP TABLE IF EXISTS " + tempTableName);
		 } catch (SQLException e) {
			 appendLog(String.format("节点【%s】删除临时表失败: %s", pipelineNode.getProperties().getName(), e.getMessage()));
		 }
	 }

	 /**
	  * 清理SQL资源
	  */
	 private void cleanupSqlResources() {
		 try {
			 // 关闭H2连接
			 if (h2Connection != null && !h2Connection.isClosed()) {
				 dropTempTable();
				 h2Connection.close();
				 appendLog(String.format("节点【%s】SQL清洗资源清理完成", pipelineNode.getProperties().getName()));
			 }
		 } catch (Exception e) {
			 appendLog(String.format("节点【%s】清理SQL资源时出错: %s", pipelineNode.getProperties().getName(), e.getMessage()));
		 }
	 }

	 /**
	  * 处理处理错误
	  */
	 private void handleProcessingError(Map<String, Object> data, Exception e) {
		 errorCount++;
		 String errorMsg = String.format("数据清洗错误：%s，数据：%s", e.getMessage(), data.toString());

		 if ("STOP".equals(dataCleanNode.getErrorHandling())) {
			 throw new RuntimeException(errorMsg);
		 } else if ("LOG".equals(dataCleanNode.getErrorHandling())) {
			 appendLog(errorMsg);
		 }
		 // SKIP模式：忽略错误，继续处理
	 }

	 /**
	  * 处理剩余数据
	  */
	 private void processRemainingData() {
		 if (!dataBuffer.isEmpty()) {
			 appendLog(String.format("处理剩余数据：%d 条", dataBuffer.size()));
			 processBatchCleaning();
		 }

		 // 处理去重策略中需要延迟输出的记录
		 String keepStrategy = dataCleanNode.getKeepStrategy();
		 if (("LAST".equals(keepStrategy) || "LATEST".equals(keepStrategy)) && !latestRecords.isEmpty()) {
			 appendLog(String.format("处理去重%s记录：%d 条", keepStrategy, latestRecords.size()));
			 for (Map<String, Object> record : latestRecords.values()) {
				 pushDataToDownstream(record);
				 cleanedCount++;
			 }
		 }

		 // 清理去重相关的内存
		 clearDeduplicationMemory();
	 }

	 /**
	  * 清理去重相关的内存
	  */
	 private void clearDeduplicationMemory() {
		 if (!deduplicationKeys.isEmpty() || !latestRecords.isEmpty()) {
			 int keyCount = deduplicationKeys.size();
			 int recordCount = latestRecords.size();

			 deduplicationKeys.clear();
			 latestRecords.clear();

			 appendLog(String.format("清理去重内存：去重键 %d 个，延迟记录 %d 条", keyCount, recordCount));
		 }
	 }

	 /**
	  * 输出清洗统计信息
	  */
	 private void outputCleaningStatistics() {
		 StringBuilder stats = new StringBuilder();
		 stats.append("=== 数据清洗统计信息 ===\n");
		 stats.append(String.format("总处理记录数: %d\n", totalCount));
		 stats.append(String.format("清洗通过记录数: %d\n", cleanedCount));
		 stats.append(String.format("移除记录数: %d\n", removedCount));
		 stats.append(String.format("错误记录数: %d\n", errorCount));
		 stats.append("=== 清洗操作统计 ===\n");
		 for (Map.Entry<String, Long> entry : cleaningStatistics.entrySet()) {
			 stats.append(String.format("%s: %d\n", entry.getKey(), entry.getValue()));
		 }
		 stats.append("========================");

		 appendLog(stats.toString());
	 }

	 /**
	  * 转换默认值
	  */
	 private Object convertDefaultValue(String value, String dataType) {
		 if (value == null) {
			 return null;
		 }

		 switch (dataType) {
			 case "NUMBER":
				 try {
					 return new BigDecimal(value);
				 } catch (NumberFormatException e) {
					 return 0;
				 }
			 case "BOOLEAN":
				 return Boolean.parseBoolean(value);
			 case "DATE":
				 try {
					 return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(value);
				 } catch (ParseException e) {
					 return new Date();
				 }
			 default:
				 return value;
		 }
	 }

	 /**
	  * 格式化日期
	  */
	 private String formatDate(String dateStr, String pattern) {
		 try {
			 Date date = parseDate(dateStr);
			 if (date != null && StringUtil.isNotBlank(pattern)) {
				 SimpleDateFormat formatter = new SimpleDateFormat(pattern);
				 return formatter.format(date);
			 }
		 } catch (Exception e) {
			 // 格式化失败，返回原值
		 }
		 return dateStr;
	 }

	 /**
	  * 格式化数字
	  */
	 private String formatNumber(String numberStr, String pattern) {
		 try {
			 double number = Double.parseDouble(numberStr);
			 if (StringUtil.isNotBlank(pattern)) {
				 DecimalFormat formatter = new DecimalFormat(pattern);
				 return formatter.format(number);
			 }
		 } catch (Exception e) {
			 // 格式化失败，返回原值
		 }
		 return numberStr;
	 }

	 /**
	  * 解析日期
	  */
	 private Date parseDate(String dateStr) throws ParseException {
		 for (SimpleDateFormat formatter : dateFormatters) {
			 try {
				 return formatter.parse(dateStr);
			 } catch (ParseException e) {
				 // 继续尝试下一个格式
			 }
		 }
		 throw new ParseException("无法解析日期: " + dateStr, 0);
	 }

	 /**
	  * 恢复原始数据类型
	  */
	 private Map<String, Object> restoreDataTypes(Map<String, Object> resultMap, Map<String, Object> originalMap) {
		 Map<String, Object> restoredMap = new HashMap<>();

		 for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
			 String key = entry.getKey();
			 Object newValue = entry.getValue();

			 // 如果新值为null，直接设置
			 if (newValue == null) {
				 restoredMap.put(key, null);
				 continue;
			 }

			 // 如果原始数据中不存在该字段，说明是新增字段，直接使用新值
			 if (!originalMap.containsKey(key)) {
				 restoredMap.put(key, newValue);
				 continue;
			 }

			 Object originalValue = originalMap.get(key);

			 // 如果原始值为null，使用新值的类型
			 if (originalValue == null) {
				 restoredMap.put(key, newValue);
				 continue;
			 }

			 // 根据原始值的类型进行转换
			 try {
				 Object restoredValue = convertToOriginalType(newValue, originalValue);
				 restoredMap.put(key, restoredValue);
			 } catch (Exception e) {
				 // 类型转换失败时，记录警告并使用新值
				 appendLog(String.format("节点【%s】字段 %s 类型转换失败，原始类型: %s，新值: %s，错误: %s",
						 pipelineNode.getProperties().getName(), key,
						 originalValue.getClass().getSimpleName(), newValue.toString(), e.getMessage()));
				 restoredMap.put(key, newValue);
			 }
		 }

		 return restoredMap;
	 }

	 /**
	  * 将值转换为原始类型
	  */
	 private Object convertToOriginalType(Object newValue, Object originalValue) throws Exception {
		 String newValueStr = newValue.toString();

		 // 如果新值是字符串且为空，根据原始类型处理
		 if (newValue instanceof String && StringUtil.isBlank(newValueStr)) {
			 if (originalValue instanceof Number) {
				 return null; // 数字类型的空字符串转为null
			 }
			 return newValueStr; // 字符串类型保持空字符串
		 }

		 // 根据原始值类型进行转换
		 if (originalValue instanceof String) {
			 return newValueStr;
		 } else if (originalValue instanceof Integer) {
			 return Integer.valueOf(newValueStr);
		 } else if (originalValue instanceof Long) {
			 return Long.valueOf(newValueStr);
		 } else if (originalValue instanceof Double) {
			 return Double.valueOf(newValueStr);
		 } else if (originalValue instanceof Float) {
			 return Float.valueOf(newValueStr);
		 } else if (originalValue instanceof BigDecimal) {
			 return new BigDecimal(newValueStr);
		 } else if (originalValue instanceof Boolean) {
			 return Boolean.valueOf(newValueStr);
		 } else if (originalValue instanceof Date) {
			 // 尝试解析日期
			 return parseDate(newValueStr);
		 } else {
			 // 其他类型直接返回新值
			 return newValue;
		 }
	 }
 }
