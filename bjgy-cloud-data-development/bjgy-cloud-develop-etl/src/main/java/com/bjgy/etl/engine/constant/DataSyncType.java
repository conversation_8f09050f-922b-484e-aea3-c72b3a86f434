package com.bjgy.etl.engine.constant;

public enum DataSyncType {
	/**
	 * 读取数据库表
	 */
	ONLY_INSERT(1, "仅插入"),
	/**
	 * 插入及更新
	 */
	INSERT_UPDATE(2, "更新及插入（需设置主键）"),
	/**
	 * 仅更新
	 */
	ONLY_UPDATE(3, "仅更新（需设置主键）"),
	;


	private final Integer code;
	private final String name;

	DataSyncType(Integer code, String name) {
		this.code = code;
		this.name = name;
	}

	public Integer getCode() {
		return code;
	}

	public static Boolean ifInsert(Integer code) {
		return ONLY_INSERT.getCode().equals(code) || INSERT_UPDATE.getCode().equals(code);
	}

	public static Boolean ifSync(Integer code) {
		return INSERT_UPDATE.getCode().equals(code) || ONLY_UPDATE.getCode().equals(code);
	}

	public String getName() {
		return name;
	}

}
