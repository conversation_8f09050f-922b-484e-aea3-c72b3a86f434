/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.engine.impl.transform;

import lombok.extern.slf4j.Slf4j;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.transform.DataDecryptNode;
import com.bjgy.flink.common.utils.JSONUtil;
import com.bjgy.flink.common.utils.ThreadUtil;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据解密引擎
 */
@Slf4j
public class DataDecrypt extends EtlEngine {

    private DataDecryptNode dataDecryptNode;
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong decryptedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final long startTime = System.currentTimeMillis();

    public DataDecrypt(PipelineNode pipelineNode) {
        super(pipelineNode);
        this.dataDecryptNode = JSONUtil.parseObject(nodeJson, DataDecryptNode.class);
    }

    @Override
    public void run() {
        try {
            initCache();

            // 等待依赖节点
            waitForDependNode(dataDecryptNode.getDependNodeNo());

            appendLog(String.format("节点【%s】开始运行", pipelineNode.getProperties().getName()));

            // 根据解密模式执行不同的解密逻辑
            switch (dataDecryptNode.getDecryptMode()) {
                case "FIELD_LEVEL":
                    executeFieldLevelDecryption();
                    break;
                case "ROW_LEVEL":
                    executeRowLevelDecryption();
                    break;
                case "CUSTOM":
                    executeCustomDecryption();
                    break;
                default:
                    throw new RuntimeException(String.format("节点【%s】不支持的解密模式: %s",
                        pipelineNode.getProperties().getName(), dataDecryptNode.getDecryptMode()));
            }

            // 输出统计信息
            if (dataDecryptNode.getOutputStatistics()) {
                appendLog(String.format("节点【%s】解密完成 - 处理记录数: %d, 解密记录数: %d, 错误记录数: %d",
                    pipelineNode.getProperties().getName(), processedCount.get(), decryptedCount.get(), errorCount.get()));
            }

            successEnd();

        } catch (Exception e) {
            log.error("数据解密节点运行失败", e);
			appendLog(String.format("节点【%s】数据解密节点运行失败: %s", pipelineNode.getProperties().getName(), e.getMessage()));
            failEnd();
        }
    }

	/**
	 * 执行字段级解密
	 */
	private void executeFieldLevelDecryption() {
		appendLog(String.format("节点【%s】开始执行字段级解密", pipelineNode.getProperties().getName()));
		String sourceDataKey = getSourceDataKey(dataDecryptNode.getDependNodeNo());

		List<Map<String, Object>> batchData = new ArrayList<>();
		int batchSize = dataDecryptNode.getBatchSize() != null ? dataDecryptNode.getBatchSize() : 1000;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点【%s】已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点【%s】运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataDecryptNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDecryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batchData.add(data);
						if (batchData.size() >= batchSize) {
							processFieldLevelBatch(batchData);
							batchData.clear();
						}
					}
					// 处理最后剩余的数据
					if (!batchData.isEmpty()) {
						processFieldLevelBatch(batchData);
						batchData.clear();
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			batchData.add(data);
			if (batchData.size() >= batchSize) {
				processFieldLevelBatch(batchData);
				batchData.clear();
			}
		}
	}

	/**
	 * 执行行级解密
	 */
	private void executeRowLevelDecryption() {
		appendLog(String.format("节点【%s】开始执行行级解密", pipelineNode.getProperties().getName()));
		String sourceDataKey = getSourceDataKey(dataDecryptNode.getDependNodeNo());

		List<Map<String, Object>> batchData = new ArrayList<>();
		int batchSize = dataDecryptNode.getBatchSize() != null ? dataDecryptNode.getBatchSize() : 1000;

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点【%s】已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点【%s】运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataDecryptNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDecryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						batchData.add(data);
						if (batchData.size() >= batchSize) {
							processRowLevelBatch(batchData);
							batchData.clear();
						}
					}
					// 处理最后剩余的数据
					if (!batchData.isEmpty()) {
						processRowLevelBatch(batchData);
						batchData.clear();
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			batchData.add(data);
			if (batchData.size() >= batchSize) {
				processRowLevelBatch(batchData);
				batchData.clear();
			}
		}
	}

	/**
	 * 执行自定义解密
	 */
	private void executeCustomDecryption() {
		appendLog(String.format("节点【%s】开始执行自定义解密", pipelineNode.getProperties().getName()));
		String sourceDataKey = getSourceDataKey(dataDecryptNode.getDependNodeNo());

		ScriptEngine scriptEngine = null;
		if ("JAVASCRIPT".equals(dataDecryptNode.getScriptType())) {
			ScriptEngineManager manager = new ScriptEngineManager();
			scriptEngine = manager.getEngineByName("javascript");
		}

		while (true) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点【%s】已被停止", pipelineNode.getProperties().getName()));
			}

			// 检查超时
			long currentTime = System.currentTimeMillis();
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			if (checkOverTime(startTime, currentTime, overTimes)) {
				appendLog(String.format("节点【%s】运行超时，停止执行", pipelineNode.getProperties().getName()));
				break;
			}

			// 检查依赖节点失败
			dependFailedCheck(dataDecryptNode.getDependNodeNo());

			Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			if (data == null) {
				// 检查依赖节点状态
				String sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dataDecryptNode.getDependNodeNo();
				Integer dependNodeStatus = memoryManager.getNodeStatus(sourceStatusKey);
				if (CommonRunStatus.isSuccess(dependNodeStatus)) {
					// 依赖节点已完成，继续获取队列中剩余数据
					while ((data = memoryManager.rightPop(sourceDataKey)) != null) {
						try {
							Map<String, Object> decryptedData = executeJavaScriptDecryption(data, scriptEngine);
							pushDataToDownstream(decryptedData);
							processedCount.incrementAndGet();
							decryptedCount.incrementAndGet();
						} catch (Exception e) {
							handleError("自定义解密脚本执行失败", e, data);
						}
					}
					break;
				}
				// 依赖节点未完成，继续等待
				ThreadUtil.sleep(20);
				continue;
			}

			try {
				Map<String, Object> decryptedData = executeJavaScriptDecryption(data, scriptEngine);
				pushDataToDownstream(decryptedData);
				processedCount.incrementAndGet();
				decryptedCount.incrementAndGet();
			} catch (Exception e) {
				handleError("自定义解密脚本执行失败", e, data);
			}
		}
	}

	/**
	 * 处理字段级解密批量数据
	 */
	private void processFieldLevelBatch(List<Map<String, Object>> batchData) {
		for (Map<String, Object> data : batchData) {
			try {
				Map<String, Object> decryptedData = new HashMap<>(data);
				boolean hasDecryption = false;

				// 对每个配置的解密字段进行解密
				for (DataDecryptNode.DecryptionField field : dataDecryptNode.getDecryptFields()) {
					if (decryptedData.containsKey(field.getEncryptedFieldName())) {
						Object encryptedValue = decryptedData.get(field.getEncryptedFieldName());
						if (encryptedValue != null) {
							String decryptedValue = decryptValue(encryptedValue.toString(), field);

							// 设置输出字段名
							String outputFieldName = StringUtil.isNotBlank(field.getOutputFieldName())
									? field.getOutputFieldName()
									: field.getEncryptedFieldName().replace("encrypted_", "").replace("_encrypted", "");

							decryptedData.put(outputFieldName, decryptedValue);

							// 是否保留加密字段
							if (!field.getKeepEncrypted() && !outputFieldName.equals(field.getEncryptedFieldName())) {
								decryptedData.remove(field.getEncryptedFieldName());
							}

							hasDecryption = true;
						}
					}
				}

				pushDataToDownstream(decryptedData);
				processedCount.incrementAndGet();
				if (hasDecryption) {
					decryptedCount.incrementAndGet();
				}

			} catch (Exception e) {
				handleError("字段级解密处理失败", e, data);
			}
		}
	}

	/**
	 * 处理行级解密批量数据
	 */
	private void processRowLevelBatch(List<Map<String, Object>> batchData) {
		for (Map<String, Object> data : batchData) {
			try {
				// 获取加密字段的数据
				String encryptedFieldName = dataDecryptNode.getEncryptedFieldName();
				if (!data.containsKey(encryptedFieldName)) {
					appendLog(String.format("节点【%s】数据中不包含加密字段: %s", pipelineNode.getProperties().getName(), encryptedFieldName));
					pushDataToDownstream(data);
					processedCount.incrementAndGet();
					continue;
				}

				Object encryptedValue = data.get(encryptedFieldName);
				if (encryptedValue == null) {
					pushDataToDownstream(data);
					processedCount.incrementAndGet();
					continue;
				}

				// 解密数据
				String decryptedData = decryptRowData(encryptedValue.toString());

				// 反序列化数据
				Map<String, Object> deserializedData = deserializeData(decryptedData);

				// 构建输出数据
				Map<String, Object> outputData = new HashMap<>();
				if (!dataDecryptNode.getKeepEncryptedField()) {
					// 不保留加密字段，使用原始数据作为基础
					outputData.putAll(data);
					outputData.remove(encryptedFieldName);
				}

				// 添加解密后的数据
				outputData.putAll(deserializedData);

				// 如果保留加密字段，则保留原加密数据
				if (dataDecryptNode.getKeepEncryptedField()) {
					outputData.put(encryptedFieldName, encryptedValue);
				}

				pushDataToDownstream(outputData);
				processedCount.incrementAndGet();
				decryptedCount.incrementAndGet();

			} catch (Exception e) {
				handleError("行级解密处理失败", e, data);
			}
		}
	}

	/**
	 * 解密单个字段值
	 */
	private String decryptValue(String encryptedValue, DataDecryptNode.DecryptionField field) throws Exception {
		String algorithm = field.getAlgorithm();
		String secretKey = field.getSecretKey();
		String iv = field.getIv();

		switch (algorithm) {
			case "AES256":
			case "AES128":
				return decryptAES(encryptedValue, secretKey, iv, algorithm);
			case "DES":
				return decryptDES(encryptedValue, secretKey, iv);
			case "3DES":
				return decrypt3DES(encryptedValue, secretKey, iv);
			case "RSA":
				return decryptRSA(encryptedValue, secretKey);
			case "BASE64":
				return new String(Base64.getDecoder().decode(encryptedValue), StandardCharsets.UTF_8);
			default:
				throw new RuntimeException("不支持的解密算法: " + algorithm);
		}
	}

	/**
	 * AES解密
	 */
	private String decryptAES(String encryptedData, String key, String iv, String algorithm) {
		try {
			// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
			String transformation;
			if (StringUtil.isBlank(iv)) {
				transformation = "AES/ECB/PKCS5Padding";
			} else {
				transformation = "AES/CBC/PKCS5Padding";
			}

			// 确保密钥长度正确
			byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
			if (algorithm.equals("AES256") && keyBytes.length != 32) {
				throw new RuntimeException("AES256密钥长度必须是32位，当前长度: " + keyBytes.length);
			} else if (algorithm.equals("AES128") && keyBytes.length != 16) {
				throw new RuntimeException("AES128密钥长度必须是16位，当前长度: " + keyBytes.length);
			}

			SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
			Cipher cipher = Cipher.getInstance(transformation);

			if (StringUtil.isNotBlank(iv)) {
				byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
				if (ivBytes.length != 16) {
					throw new RuntimeException("AES IV长度必须是16位，当前长度: " + ivBytes.length);
				}
				IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
				cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
			} else {
				// 使用ECB模式，不需要IV
				cipher.init(Cipher.DECRYPT_MODE, secretKey);
			}

			byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
			byte[] decrypted = cipher.doFinal(encryptedBytes);
			return new String(decrypted, StandardCharsets.UTF_8);

		} catch (Exception e) {
			throw new RuntimeException("AES解密失败: " + e.getMessage() + ", 算法: " + algorithm + ", 密钥长度: " + key.length() + ", IV: " + (StringUtil.isNotBlank(iv) ? "已设置" : "未设置"), e);
		}
	}

	private String decryptDES(String encryptedData, String key, String iv) throws Exception {
		// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
		String transformation;
		if (StringUtil.isBlank(iv)) {
			transformation = "DES/ECB/PKCS5Padding";
		} else {
			transformation = "DES/CBC/PKCS5Padding";
		}

		// 确保密钥长度正确（DES密钥必须是8字节）
		byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
		if (keyBytes.length != 8) {
			throw new RuntimeException("DES密钥长度必须是8位，当前长度: " + keyBytes.length);
		}

		SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DES");
		Cipher cipher = Cipher.getInstance(transformation);

		if (StringUtil.isNotBlank(iv)) {
			byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
			if (ivBytes.length != 8) {
				throw new RuntimeException("DES IV长度必须是8位，当前长度: " + ivBytes.length);
			}
			IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
			cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
		} else {
			cipher.init(Cipher.DECRYPT_MODE, secretKey);
		}

		byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
		byte[] decrypted = cipher.doFinal(encryptedBytes);
		return new String(decrypted, StandardCharsets.UTF_8);
	}

	private String decrypt3DES(String encryptedData, String key, String iv) throws Exception {
		// 如果没有IV，使用ECB模式；如果有IV，使用CBC模式
		String transformation;
		if (StringUtil.isBlank(iv)) {
			transformation = "DESede/ECB/PKCS5Padding";
		} else {
			transformation = "DESede/CBC/PKCS5Padding";
		}

		// 确保密钥长度正确（3DES密钥必须是24字节）
		byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
		if (keyBytes.length != 24) {
			throw new RuntimeException("3DES密钥长度必须是24位，当前长度: " + keyBytes.length);
		}

		SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DESede");
		Cipher cipher = Cipher.getInstance(transformation);

		if (StringUtil.isNotBlank(iv)) {
			byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
			if (ivBytes.length != 8) {
				throw new RuntimeException("3DES IV长度必须是8位，当前长度: " + ivBytes.length);
			}
			IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
			cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
		} else {
			cipher.init(Cipher.DECRYPT_MODE, secretKey);
		}

		byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
		byte[] decrypted = cipher.doFinal(encryptedBytes);
		return new String(decrypted, StandardCharsets.UTF_8);
	}

	/**
	 * RSA解密
	 */
	private String decryptRSA(String encryptedData, String privateKey) throws Exception {
		try {
			// 移除私钥字符串中的头尾标识和换行符
			String cleanPrivateKey = privateKey
					.replace("-----BEGIN PRIVATE KEY-----", "")
					.replace("-----END PRIVATE KEY-----", "")
					.replace("-----BEGIN RSA PRIVATE KEY-----", "")
					.replace("-----END RSA PRIVATE KEY-----", "")
					.replaceAll("\\s", "");

			// Base64解码私钥
			byte[] keyBytes = Base64.getDecoder().decode(cleanPrivateKey);

			// 生成私钥对象
			PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			PrivateKey privKey = keyFactory.generatePrivate(spec);

			// 创建解密器
			Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
			cipher.init(Cipher.DECRYPT_MODE, privKey);

			// 检查是否是分块加密的数据（包含"|"分隔符）
			if (encryptedData.contains("|")) {
				// 分块解密
				String[] encryptedBlocks = encryptedData.split("\\|");
				StringBuilder result = new StringBuilder();

				for (String block : encryptedBlocks) {
					byte[] encryptedBytes = Base64.getDecoder().decode(block);
					byte[] decrypted = cipher.doFinal(encryptedBytes);
					result.append(new String(decrypted, StandardCharsets.UTF_8));
				}

				return result.toString();
			} else {
				// 单块解密
				byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
				byte[] decrypted = cipher.doFinal(encryptedBytes);
				return new String(decrypted, StandardCharsets.UTF_8);
			}

		} catch (Exception e) {
			throw new RuntimeException("RSA解密失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解密行级数据
	 */
	private String decryptRowData(String encryptedData) throws Exception {
		String algorithm = dataDecryptNode.getRowDecryptAlgorithm();
		String secretKey = dataDecryptNode.getRowSecretKey();
		String iv = dataDecryptNode.getRowIV();

		switch (algorithm) {
			case "AES256":
			case "AES128":
				return decryptAES(encryptedData, secretKey, iv, algorithm);
			case "DES":
				return decryptDES(encryptedData, secretKey, iv);
			case "3DES":
				return decrypt3DES(encryptedData, secretKey, iv);
			case "RSA":
				return decryptRSA(encryptedData, secretKey);
			case "BASE64":
				return new String(Base64.getDecoder().decode(encryptedData), StandardCharsets.UTF_8);
			default:
				throw new RuntimeException("不支持的解密算法: " + algorithm);
		}
	}

	/**
	 * 反序列化数据
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> deserializeData(String serializedData) throws Exception {
		switch (dataDecryptNode.getSerializationFormat()) {
			case "JSON":
				return JSONUtil.parseObject(serializedData, Map.class);
			case "XML":
				// XML反序列化实现
				Map<String, Object> xmlData = new HashMap<>();

				// 移除根标签 <data> 和 </data>
				String content = serializedData;
				if (content.startsWith("<data>") && content.endsWith("</data>")) {
					content = content.substring(6, content.length() - 7); // 移除 <data> 和 </data>
				}

				// 简单的XML解析，匹配 <key>value</key> 格式
				String pattern = "<([^>]+)>([^<]*)</\\1>";
				java.util.regex.Pattern xmlPattern = java.util.regex.Pattern.compile(pattern);
				java.util.regex.Matcher matcher = xmlPattern.matcher(content);

				while (matcher.find()) {
					String key = matcher.group(1);
					String value = matcher.group(2);
					xmlData.put(key, value);
				}

				return xmlData;
			case "CSV":
				// CSV反序列化需要知道字段顺序，这里简化处理
				Map<String, Object> csvData = new HashMap<>();
				String[] values = serializedData.split(",");
				for (int i = 0; i < values.length; i++) {
					csvData.put("field_" + i, values[i]);
				}
				return csvData;
			case "DELIMITED":
				String delimiter = StringUtil.isNotBlank(dataDecryptNode.getDelimiter())
						? dataDecryptNode.getDelimiter() : ",";
				Map<String, Object> delimitedData = new HashMap<>();
				String[] delimitedValues = serializedData.split(delimiter);
				for (int i = 0; i < delimitedValues.length; i++) {
					delimitedData.put("field_" + i, delimitedValues[i]);
				}
				return delimitedData;
			default:
				return JSONUtil.parseObject(serializedData, Map.class);
		}
	}

	/**
	 * 执行JavaScript解密
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> executeJavaScriptDecryption(Map<String, Object> data, ScriptEngine scriptEngine) throws Exception {
		// 创建数据的副本，避免原数据被意外修改
		Map<String, Object> dataCopy = new HashMap<>(data);

		try {
			// 将数据转换为JSON字符串传递给JavaScript引擎
			String jsonData = StringUtil.toJson(dataCopy);
			scriptEngine.put("recordJson", jsonData);

			// 构建包装后的JavaScript代码，自动处理JSON转换
			String wrappedJsCode = buildWrappedJavaScriptCode(dataDecryptNode.getJavascriptCode());

			// 执行包装后的JavaScript代码
			scriptEngine.eval(wrappedJsCode);

			// 调用包装后的函数并获取结果
			Object result = scriptEngine.eval("wrappedDecryptData(recordJson)");

			if (result == null) {
				return null;
			}

			// 如果返回的是JSON字符串，解析为Map
			if (result instanceof String) {
				String resultJson = (String) result;
				if ("null".equals(resultJson) || StringUtil.isBlank(resultJson)) {
					return null;
				}
				try {
					return StringUtil.fromJson(resultJson, Map.class);
				} catch (Exception e) {
					appendLog(String.format("节点【%s】解析JavaScript返回的JSON失败: %s",
							pipelineNode.getProperties().getName(), e.getMessage()));
					return dataCopy;
				}
			}

			// 如果返回的是Map类型，直接返回
			if (result instanceof Map) {
				return (Map<String, Object>) result;
			}

			// 如果都获取不到，返回原数据副本
			return dataCopy;

		} catch (Exception e) {
			// 记录JavaScript执行错误的详细信息
			appendLog(String.format("节点【%s】JavaScript解密执行错误: %s, 数据: %s",
					pipelineNode.getProperties().getName(), e.getMessage(), data.toString()));
			throw e;
		}
	}

	/**
	 * 构建包装后的JavaScript代码，自动处理JSON转换
	 */
	private String buildWrappedJavaScriptCode(String userJsCode) {
		StringBuilder wrappedCode = new StringBuilder();

		// 添加包装函数
		wrappedCode.append("function wrappedDecryptData(recordJson) {\n");
		wrappedCode.append("  // 自动解析JSON为JavaScript对象\n");
		wrappedCode.append("  var record = JSON.parse(recordJson);\n");
		wrappedCode.append("  \n");

		// 检查用户代码是否已经包含decrypt_data函数定义
		if (userJsCode.contains("function decrypt_data")) {
			// 用户代码包含完整的decrypt_data函数定义
			wrappedCode.append("  // 用户定义的decrypt_data函数\n");
			wrappedCode.append("  ").append(userJsCode).append("\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用用户的decrypt_data函数\n");
			wrappedCode.append("  var result = decrypt_data(record);\n");
		} else {
			// 用户代码是直接的处理逻辑，包装成decrypt_data函数
			wrappedCode.append("  // 包装用户的处理逻辑\n");
			wrappedCode.append("  function decrypt_data(record) {\n");
			wrappedCode.append("    ").append(userJsCode.replace("\n", "\n    ")).append("\n");
			wrappedCode.append("    return record;\n");
			wrappedCode.append("  }\n");
			wrappedCode.append("  \n");
			wrappedCode.append("  // 调用包装后的decrypt_data函数\n");
			wrappedCode.append("  var result = decrypt_data(record);\n");
		}

		wrappedCode.append("  \n");
		wrappedCode.append("  // 自动转换结果为JSON字符串返回\n");
		wrappedCode.append("  if (result === null || result === undefined) {\n");
		wrappedCode.append("    return null;\n");
		wrappedCode.append("  }\n");
		wrappedCode.append("  return JSON.stringify(result);\n");
		wrappedCode.append("}\n");

		return wrappedCode.toString();
	}

	/**
	 * 处理错误
	 */
	private void handleError(String message, Exception e, Map<String, Object> data) {
		errorCount.incrementAndGet();
		processedCount.incrementAndGet();

		String errorMsg = String.format("%s: %s, 数据: %s", message, e.getMessage(), JSONUtil.toJsonString(data));

		switch (dataDecryptNode.getErrorHandling()) {
			case "SKIP":
				appendLog(String.format("节点【%s】跳过错误数据: %s", pipelineNode.getProperties().getName(), errorMsg));
				break;
			case "STOP":
				throw new RuntimeException(errorMsg, e);
			case "LOG":
				appendLog(String.format("节点【%s】记录错误，继续处理: %s", pipelineNode.getProperties().getName(), errorMsg));
				// 推送原始数据
				pushDataToDownstream(data);
				break;
		}
	}
}
