package com.bjgy.etl.engine.impl.extract;

import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.extract.ReadExcelNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import com.bjgy.framework.common.utils.FileDownloadUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ReadExcel
 */
public class ReadExcel extends EtlEngine {

    private static final Logger logger = LoggerFactory.getLogger(ReadExcel.class);

    public ReadExcel(PipelineNode pipelineNode) {
        super(pipelineNode);
    }

    @SneakyThrows
    @Override
    public void run() {
        initCache();
        Integer overTimes = pipelineNodeEntity.getOverTimes();
        long startTime = System.currentTimeMillis();
        long totalCount = 0;
        String tempFilePath = null;

        try {
            ReadExcelNode readExcelNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, ReadExcelNode.class);
            Integer fetchSize = readExcelNode.getFetchSize();
            String dependNodeNo = readExcelNode.getDependNodeNo();

            // 等待依赖节点
            waitForDependNode(dependNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

            // 执行Excel读取逻辑
            appendLog(String.format("节点 【%s】 开始读取Excel文件：%s", pipelineNode.getProperties().getName(), readExcelNode.getFileName()));

            // 下载文件到临时目录
            tempFilePath = FileDownloadUtil.downloadFileFromUrl(readExcelNode.getFileUrl());
            appendLog(String.format("节点 【%s】 文件下载到临时路径：%s", pipelineNode.getProperties().getName(), tempFilePath));

            try (Workbook workbook = createWorkbook(tempFilePath)) {
                Sheet sheet = getSheet(workbook, readExcelNode.getSheetName());
                if (sheet == null) {
                    throw new RuntimeException(String.format("在Excel文件中未找到工作表 '%s'", readExcelNode.getSheetName()));
                }

                // 获取列名
                List<String> columnNames = getColumnNames(sheet, readExcelNode);
                appendLog(String.format("节点 【%s】 找到 %d 列：%s", pipelineNode.getProperties().getName(), columnNames.size(), String.join(", ", columnNames)));

                // 计算数据范围
                int startRowIndex = readExcelNode.getStartRow() - 1; // Excel行号从1开始，POI从0开始
                int endRowIndex = readExcelNode.getEndRow() == 0 ? sheet.getLastRowNum() : readExcelNode.getEndRow() - 1;
                int startColIndex = columnToIndex(readExcelNode.getStartColumn());
                int endColIndex = StringUtil.isBlank(readExcelNode.getEndColumn()) ?
                    (columnNames.size() - 1 + startColIndex) : columnToIndex(readExcelNode.getEndColumn());

                appendLog(String.format("节点 【%s】 正在读取数据，从第 %d 行到第 %d 行，从 %s 列到 %s 列",
                    pipelineNode.getProperties().getName(),
                    readExcelNode.getStartRow(),
                    readExcelNode.getEndRow() == 0 ? sheet.getLastRowNum() + 1 : readExcelNode.getEndRow(),
                    readExcelNode.getStartColumn(),
                    StringUtil.isBlank(readExcelNode.getEndColumn()) ? indexToColumn(endColIndex) : readExcelNode.getEndColumn()));

                updateLog();

                // 读取数据
                for (int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
                	dependFailedCheck(dependNodeNo);
                    if (isStop()) {
                        throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
                    }

                    long currentTime = System.currentTimeMillis();
                    if (checkOverTime(startTime, currentTime, overTimes)) {
                        appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
                        appendLog(String.format("节点 【%s】 运行超时，已获取数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
                        updateLog();
                        break;
                    }

                    Row row = sheet.getRow(rowIndex);
                    if (row == null) {
                        if (readExcelNode.getSkipEmptyRows()) {
                            continue;
                        } else {
                            // 创建空行数据
                            Map<String, Object> emptyRowData = new HashMap<>();
                            for (String columnName : columnNames) {
                                emptyRowData.put(columnName, null);
                            }
                            // 向内存队列中存储数据
                            pushDataToDownstream(emptyRowData);
                            totalCount++;
                            continue;
                        }
                    }

                    // 检查是否为空行
                    if (readExcelNode.getSkipEmptyRows() && isEmptyRow(row, startColIndex, endColIndex)) {
                        continue;
                    }

                    // 构建行数据
                    Map<String, Object> rowData = new HashMap<>();
                    for (int colIndex = 0; colIndex < columnNames.size(); colIndex++) {
                        int actualColIndex = startColIndex + colIndex;
                        if (actualColIndex <= endColIndex) {
                            Cell cell = row.getCell(actualColIndex);
                            Object cellValue = getCellValue(cell);
                            rowData.put(columnNames.get(colIndex), cellValue);
                        }
                    }

                    // 向内存队列中推送数据（使用数据复制机制）
                    pushDataToDownstream(rowData);
                    totalCount++;

                    // 每处理10000条数据打印一次日志
                    if (totalCount % 10000 == 0) {
                        appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
                        updateLog();
                    }

                    // 判断是否被消费 - 控制队列大小
                    while (true) {
						dependFailedCheck(dependNodeNo);
                        if (isStop()) {
                            throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
                        }
                        currentTime = System.currentTimeMillis();
                        if (checkOverTime(startTime, currentTime, overTimes)) {
                            appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
                            appendLog(String.format("节点 【%s】 运行超时，已获取数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
                            updateLog();
                            break;
                        }
                        // 检查队列大小时需要考虑数据复制机制
                        long currentQueueSize = getCurrentQueueSize();
                        if (currentQueueSize < fetchSize) {
                            break;
                        }
                        ThreadUtil.sleep(10);
                    }
                }
            }

            appendLog(String.format("节点 【%s】 运行成功结束，获取数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
            successEnd();

        } catch (Exception e) {
            appendLog(String.format("节点 【%s】 运行失败结束，获取数据总数：%d，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
            failEnd();
        } finally {
            // 清理临时文件
            if (tempFilePath != null) {
                FileDownloadUtil.cleanupTempFile(tempFilePath);
                logger.info("节点 【{}】 清理临时文件：{}", pipelineNode.getProperties().getName(), tempFilePath);
            }
        }
    }

    /**
     * 创建Workbook对象
     */
    private Workbook createWorkbook(String filePath) throws IOException {
        if (filePath.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(new FileInputStream(filePath));
        } else if (filePath.toLowerCase().endsWith(".xls")) {
            return new HSSFWorkbook(new FileInputStream(filePath));
        } else {
            throw new IllegalArgumentException("Unsupported file format. Only .xls and .xlsx are supported.");
        }
    }

    /**
     * 获取工作表
     */
    private Sheet getSheet(Workbook workbook, String sheetName) {
        if (StringUtil.isBlank(sheetName)) {
            return workbook.getSheetAt(0); // 默认第一个工作表
        }
        return workbook.getSheet(sheetName);
    }

    /**
     * 获取列名
     */
    private List<String> getColumnNames(Sheet sheet, ReadExcelNode readExcelNode) {
        List<String> columnNames = new ArrayList<>();

        if (readExcelNode.getHeaderRow() == 0) {
            // 没有标题行，使用默认列名 A, B, C...
            int startColIndex = columnToIndex(readExcelNode.getStartColumn());
            int endColIndex = StringUtil.isBlank(readExcelNode.getEndColumn()) ?
                getLastColumnIndex(sheet) : columnToIndex(readExcelNode.getEndColumn());

            for (int i = startColIndex; i <= endColIndex; i++) {
                columnNames.add(indexToColumn(i));
            }
        } else {
            // 从标题行读取列名
            Row headerRow = sheet.getRow(readExcelNode.getHeaderRow() - 1);
            if (headerRow != null) {
                int startColIndex = columnToIndex(readExcelNode.getStartColumn());
                int endColIndex = StringUtil.isBlank(readExcelNode.getEndColumn()) ?
                    headerRow.getLastCellNum() - 1 : columnToIndex(readExcelNode.getEndColumn());

                for (int i = startColIndex; i <= endColIndex; i++) {
                    Cell cell = headerRow.getCell(i);
                    String columnName = getCellValue(cell) != null ? getCellValue(cell).toString() : indexToColumn(i);
                    columnNames.add(columnName);
                }
            }
        }

        return columnNames;
    }

    /**
     * 获取工作表最后一列的索引
     */
    private int getLastColumnIndex(Sheet sheet) {
        int maxCol = 0;
        for (Row row : sheet) {
            if (row.getLastCellNum() > maxCol) {
                maxCol = row.getLastCellNum() - 1;
            }
        }
        return maxCol;
    }

    /**
     * 列字母转索引 (A=0, B=1, ...)
     */
    private int columnToIndex(String column) {
        if (StringUtil.isBlank(column)) {
            return 0;
        }
        column = column.toUpperCase();
        int result = 0;
        for (int i = 0; i < column.length(); i++) {
            result = result * 26 + (column.charAt(i) - 'A' + 1);
        }
        return result - 1;
    }

    /**
     * 索引转列字母 (0=A, 1=B, ...)
     */
    private String indexToColumn(int index) {
        StringBuilder result = new StringBuilder();
        while (index >= 0) {
            result.insert(0, (char) ('A' + index % 26));
            index = index / 26 - 1;
        }
        return result.toString();
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row, int startCol, int endCol) {
        for (int i = startCol; i <= endCol; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                Object value = getCellValue(cell);
                if (value != null && !value.toString().trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回Double
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            case BLANK:
            default:
                return null;
        }
    }

    /**
     * 获取当前队列大小
     * 如果启用了数据复制机制，返回所有下游队列的最大大小
     * 否则返回原有单一队列的大小
     */
    private long getCurrentQueueSize() {
        if (isDataReplicationEnabled()) {
            // 数据复制机制：返回所有下游队列中的最大大小
            long maxQueueSize = 0;
            for (String downstreamNodeId : downstreamNodeIds) {
                String dataKey = downstreamDataKeys.get(downstreamNodeId);
                if (dataKey != null) {
                    long queueSize = memoryManager.getQueueSize(dataKey);
                    maxQueueSize = Math.max(maxQueueSize, queueSize);
                }
            }
            return maxQueueSize;
        } else {
            // 原有机制：返回单一队列大小
            return memoryManager.getQueueSize(NODE_DATA_KEY);
        }
    }
}
