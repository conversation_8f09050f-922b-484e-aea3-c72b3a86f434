package com.bjgy.etl.engine.impl.load;

import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.load.WriteExcelNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @ClassName WriteExcel
 */
public class WriteExcel extends EtlEngine {

	private static final Logger logger = LoggerFactory.getLogger(WriteExcel.class);

	public WriteExcel(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	private String sourceDataKey;
	private String sourceDataStatusKey;
	private WriteExcelNode writeExcelNode;
	private long totalCount = 0;
	private List<Map<String, Object>> dataList = new ArrayList<>();
	private SimpleDateFormat dateFormatter;
	private DecimalFormat numberFormatter;
	private List<WriteExcelNode.ColumnMapping> effectiveColumnMappings = new ArrayList<>();
	private boolean hasWriteHeaderRow = false;
	private boolean shouldWriteHeaderRow = false;
	private int emptyDataCycles = 0; // 连续空数据循环计数

	@SneakyThrows
	@Override
	public void run() {
		initCache();
		Integer overTimes = pipelineNodeEntity.getOverTimes();
		long startTime = System.currentTimeMillis();
		String outputFilePath;

		try {
			writeExcelNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, WriteExcelNode.class);
			String dependNodeNo = writeExcelNode.getDependNodeNo();

			// 等待依赖节点
			waitForDependNode(dependNodeNo);

			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

			// 初始化格式化器
			dateFormatter = new SimpleDateFormat(writeExcelNode.getDateFormat());
			numberFormatter = new DecimalFormat(writeExcelNode.getNumberFormat());

			// 获取数据节点
			String sourceNodeNo = writeExcelNode.getDependNodeNo();
			sourceDataKey = getSourceDataKey(sourceNodeNo);
			sourceDataStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + sourceNodeNo;

			// 初始化列映射
			initializeColumnMappings();

			// 构建输出文件路径
			outputFilePath = buildOutputFilePath();
			appendLog(String.format("节点 【%s】 开始写入Excel文件：%s", pipelineNode.getProperties().getName(), outputFilePath));

			// 创建输出目录
			try {
				Path outputPath = Paths.get(outputFilePath);
				appendLog(String.format("节点 【%s】 准备创建输出目录：%s",
						pipelineNode.getProperties().getName(), outputPath.getParent().toString()));

				Files.createDirectories(outputPath.getParent());
				appendLog(String.format("节点 【%s】 输出目录创建成功", pipelineNode.getProperties().getName()));

				// 检查目录权限
				if (!Files.isWritable(outputPath.getParent())) {
					throw new RuntimeException(String.format("节点 【%s】 输出目录没有写入权限：%s",
							pipelineNode.getProperties().getName(), outputPath.getParent().toString()));
				}
				appendLog(String.format("节点 【%s】 输出目录权限检查通过", pipelineNode.getProperties().getName()));

			} catch (Exception e) {
				appendLog(String.format("节点 【%s】 创建输出目录失败：%s",
						pipelineNode.getProperties().getName(), e.getMessage()));
				throw new RuntimeException(String.format("节点 【%s】 创建输出目录失败",
						pipelineNode.getProperties().getName()), e);
			}

			// 诊断信息：记录依赖节点的初始状态
			Integer initialStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
			long initialQueueSize = memoryManager.getQueueSize(sourceDataKey);
			appendLog(String.format("节点 【%s】 开始处理，依赖节点初始状态：%d，初始队列大小：%d",
					pipelineNode.getProperties().getName(), initialStatus, initialQueueSize));

			// 处理数据并写入Excel
			appendLog(String.format("节点 【%s】 准备创建Workbook对象", pipelineNode.getProperties().getName()));
			try (Workbook workbook = createWorkbook(outputFilePath)) {
				appendLog(String.format("节点 【%s】 Workbook对象创建成功", pipelineNode.getProperties().getName()));

				appendLog(String.format("节点 【%s】 准备获取或创建工作表：%s",
						pipelineNode.getProperties().getName(), writeExcelNode.getSheetName()));
				Sheet sheet = getOrCreateSheet(workbook, writeExcelNode.getSheetName());
				appendLog(String.format("节点 【%s】 工作表获取成功", pipelineNode.getProperties().getName()));

				// 确定数据写入的起始行位置
				int currentRowIndex;
				if ("append".equals(writeExcelNode.getWriteMode())) {
					if (sheet.getLastRowNum() >= 0) {
						// 追加模式且文件有数据：从现有数据的下一行开始写入
						currentRowIndex = sheet.getLastRowNum() + 1;
						appendLog(String.format("节点 【%s】 追加模式：从第 %d 行开始写入（现有数据结束于第 %d 行）",
								pipelineNode.getProperties().getName(), currentRowIndex + 1, sheet.getLastRowNum() + 1));
					} else {
						// 追加模式但文件为空或不存在：从第1行开始写入，并写入标题行
						currentRowIndex = 0; // POI从0开始
						appendLog(String.format("节点 【%s】 追加模式但文件为空：从第1行开始写入并包含标题行",
								pipelineNode.getProperties().getName()));
						shouldWriteHeaderRow = true;

					}
				} else {
					// 覆盖模式：从配置的起始行开始写入
					currentRowIndex = writeExcelNode.getStartRow() - 1; // Excel行号从1开始，POI从0开始
					appendLog(String.format("节点 【%s】 覆盖模式：从配置的第 %d 行开始写入",
							pipelineNode.getProperties().getName(), writeExcelNode.getStartRow()));
					shouldWriteHeaderRow = true;
				}

				// 读取并写入数据
				long lastStatusCheckTime = System.currentTimeMillis();
				int cycleCount = 0;

				// 输出详细的配置信息用于诊断
				appendLog(String.format("节点 【%s】 配置信息 - 批次大小：%d，写入模式：%s，包含标题：%s",
						pipelineNode.getProperties().getName(),
						writeExcelNode.getBatchSize(),
						writeExcelNode.getWriteMode(),
						writeExcelNode.getIncludeHeader()));

				appendLog(String.format("节点 【%s】 配置信息 - 起始行：%d，起始列：%s，工作表名：%s",
						pipelineNode.getProperties().getName(),
						writeExcelNode.getStartRow(),
						writeExcelNode.getStartColumn(),
						writeExcelNode.getSheetName()));

				// 检查系统内存和磁盘空间
				Runtime runtime = Runtime.getRuntime();
				long maxMemory = runtime.maxMemory();
				long totalMemory = runtime.totalMemory();
				long freeMemory = runtime.freeMemory();
				long usedMemory = totalMemory - freeMemory;

				appendLog(String.format("节点 【%s】 系统资源 - 最大内存：%d MB，已用内存：%d MB，可用内存：%d MB",
						pipelineNode.getProperties().getName(),
						maxMemory / 1024 / 1024,
						usedMemory / 1024 / 1024,
						freeMemory / 1024 / 1024));

				try {
					Path outputPath = Paths.get(outputFilePath).getParent();
					long freeSpace = Files.getFileStore(outputPath).getUsableSpace();
					appendLog(String.format("节点 【%s】 磁盘空间 - 可用空间：%d MB",
							pipelineNode.getProperties().getName(), freeSpace / 1024 / 1024));
				} catch (Exception e) {
					appendLog(String.format("节点 【%s】 无法获取磁盘空间信息：%s",
							pipelineNode.getProperties().getName(), e.getMessage()));
				}

				appendLog(String.format("节点 【%s】 开始数据处理循环", pipelineNode.getProperties().getName()));
				updateLog();

				while (true) {
					cycleCount++;

					// 第一次循环时输出更多诊断信息
					if (cycleCount == 1) {
						appendLog(String.format("节点 【%s】 进入第一次循环，准备检查依赖节点状态",
								pipelineNode.getProperties().getName()));
					}

					dependFailedCheck(dependNodeNo);
					if (isStop()) {
						throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
					}

					long currentTime = System.currentTimeMillis();
					if (checkOverTime(startTime, currentTime, overTimes)) {
						appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
						appendLog(String.format("节点 【%s】 运行超时，已处理数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
						updateLog();
						break;
					}

					// 第一次循环时记录准备获取数据
					if (cycleCount == 1) {
						appendLog(String.format("节点 【%s】 第一次循环，准备从队列获取数据，数据键：%s",
								pipelineNode.getProperties().getName(), sourceDataKey));
					}

					// 获取数据
					Map<String, Object> dataMap = memoryManager.rightPop(sourceDataKey);
					boolean hasNewData = false;
					if (dataMap != null) {
						totalCount++;
						dataList.add(dataMap);
						hasNewData = true;
						emptyDataCycles = 0; // 重置空循环计数

						// 第一次获取到数据时记录
						if (totalCount == 1) {
							appendLog(String.format("节点 【%s】 成功获取到第一条数据，数据字段数：%d",
									pipelineNode.getProperties().getName(), dataMap.size()));
						}
					} else {
						emptyDataCycles++;

						// 第一次循环时记录未获取到数据
						if (cycleCount == 1) {
							appendLog(String.format("节点 【%s】 第一次循环未获取到数据",
									pipelineNode.getProperties().getName()));
						}

						// 如果连续多次空循环，输出诊断信息
						if (emptyDataCycles % 100 == 0) { // 每100次空循环输出一次
							Integer currentSourceStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
							long currentQueueSize = memoryManager.getQueueSize(sourceDataKey);
							appendLog(String.format("节点 【%s】 连续 %d 次空循环，依赖节点状态：%d，队列大小：%d",
									pipelineNode.getProperties().getName(), emptyDataCycles, currentSourceStatus, currentQueueSize));
							updateLog();
						}
					}

					// 批量写入数据
					if (dataList.size() >= writeExcelNode.getBatchSize()) {
						currentRowIndex = writeDataBatch(sheet, currentRowIndex);
						dataList.clear();
					}

					// 判断节点状态
					Integer sourceDataStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
					if (CommonRunStatus.isSuccess(sourceDataStatus)) {
						// 获取剩余数据
						long remainSize = memoryManager.getQueueSize(sourceDataKey);
						appendLog(String.format("节点 【%s】 依赖节点已完成，剩余队列大小：%d", pipelineNode.getProperties().getName(), remainSize));
						for (int i = 0; i < remainSize; i++) {
							Map<String, Object> remainData = memoryManager.rightPop(sourceDataKey);
							if (remainData != null) {
								totalCount++;
								dataList.add(remainData);
							}
						}
						break;
					}

					// 定期状态检查（每30秒）
					if (currentTime - lastStatusCheckTime > 30000) {
						long queueSize = memoryManager.getQueueSize(sourceDataKey);
						appendLog(String.format("节点 【%s】 状态检查 - 循环次数：%d，已处理：%d 条，队列大小：%d，依赖节点状态：%d",
								pipelineNode.getProperties().getName(), cycleCount, totalCount, queueSize, sourceDataStatus));
						lastStatusCheckTime = currentTime;
						updateLog();
					}

					// 如果没有获取到新数据且依赖节点未完成，短暂休眠避免疯狂循环
					if (!hasNewData) {
						try {
							Thread.sleep(50); // 休眠50ms，避免疯狂循环
						} catch (InterruptedException e) {
							Thread.currentThread().interrupt();
							throw new RuntimeException(String.format("节点 【%s】 被中断", pipelineNode.getProperties().getName()));
						}
					}
				}

				// 写入剩余数据
				if (!dataList.isEmpty()) {
					writeDataBatch(sheet, currentRowIndex);
				}

				// 应用高级配置
				applyAdvancedSettings(sheet);

				// 保存文件
				try (FileOutputStream fileOut = new FileOutputStream(outputFilePath)) {
					workbook.write(fileOut);
				}
			}

			appendLog(String.format("节点 【%s】 运行成功结束，已处理数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
			successEnd();

		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，已处理数据总数：%d，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			failEnd();
		} finally {
			// 清理临时文件（如果需要）
			logger.info("节点 【{}】 完成Excel写入操作", pipelineNode.getProperties().getName());
		}
	}

	/**
	 * 初始化列映射
	 */
	private void initializeColumnMappings() {
		if (writeExcelNode.getColumnMappings() != null && !writeExcelNode.getColumnMappings().isEmpty()) {
			effectiveColumnMappings = new ArrayList<>(writeExcelNode.getColumnMappings());
			appendLog(String.format("节点 【%s】 使用已配置的列映射，数量：%d",
					pipelineNode.getProperties().getName(), effectiveColumnMappings.size()));
		} else {
			effectiveColumnMappings = new ArrayList<>();
			appendLog(String.format("节点 【%s】 列映射未配置，将从数据自动生成",
					pipelineNode.getProperties().getName()));
		}
	}

	/**
	 * 根据数据动态生成列映射
	 */
	private void generateColumnMappingsFromData(Map<String, Object> dataMap) {
		if (dataMap == null || dataMap.isEmpty()) {
			return;
		}

		appendLog(String.format("节点 【%s】 从数据生成列映射，字段数量：%d",
				pipelineNode.getProperties().getName(), dataMap.size()));

		for (String key : dataMap.keySet()) {
			WriteExcelNode.ColumnMapping mapping = new WriteExcelNode.ColumnMapping();
			mapping.setSourceColumn(key);
			mapping.setTargetColumn(key); // 默认使用源字段名作为目标列名

			// 根据数据类型推断列类型
			Object value = dataMap.get(key);
			if (value != null) {
				if (value instanceof Number) {
					mapping.setColumnType("NUMERIC");
				} else if (value instanceof Date) {
					mapping.setColumnType("DATE");
				} else if (value instanceof Boolean) {
					mapping.setColumnType("BOOLEAN");
				} else {
					mapping.setColumnType("STRING");
				}
			} else {
				mapping.setColumnType("STRING");
			}

			effectiveColumnMappings.add(mapping);
		}

		appendLog(String.format("节点 【%s】 生成了 %d 个列映射",
				pipelineNode.getProperties().getName(), effectiveColumnMappings.size()));
	}

	/**
	 * 补充缺失的列映射
	 */
	private void supplementColumnMappings(Map<String, Object> dataMap) {
		if (dataMap == null || dataMap.isEmpty()) {
			return;
		}

		// 获取已配置的源字段列表
		List<String> configuredSourceColumns = new ArrayList<>();
		for (WriteExcelNode.ColumnMapping mapping : effectiveColumnMappings) {
			configuredSourceColumns.add(mapping.getSourceColumn());
		}

		// 查找数据中未映射的字段
		List<String> missingColumns = new ArrayList<>();
		for (String key : dataMap.keySet()) {
			if (!configuredSourceColumns.contains(key)) {
				missingColumns.add(key);
			}
		}

		if (!missingColumns.isEmpty()) {
			appendLog(String.format("节点 【%s】 发现 %d 个未映射字段，添加到列映射中：%s",
					pipelineNode.getProperties().getName(), missingColumns.size(), String.join(", ", missingColumns)));

			// 为缺失的字段添加默认映射
			for (String missingColumn : missingColumns) {
				WriteExcelNode.ColumnMapping mapping = new WriteExcelNode.ColumnMapping();
				mapping.setSourceColumn(missingColumn);
				mapping.setTargetColumn(missingColumn); // 默认使用源字段名作为目标列名

				// 根据数据类型推断列类型
				Object value = dataMap.get(missingColumn);
				if (value != null) {
					if (value instanceof Number) {
						mapping.setColumnType("NUMERIC");
					} else if (value instanceof Date) {
						mapping.setColumnType("DATE");
					} else if (value instanceof Boolean) {
						mapping.setColumnType("BOOLEAN");
					} else {
						mapping.setColumnType("STRING");
					}
				} else {
					mapping.setColumnType("STRING");
				}

				effectiveColumnMappings.add(mapping);
			}

			appendLog(String.format("节点 【%s】 补充后的列映射总数：%d",
					pipelineNode.getProperties().getName(), effectiveColumnMappings.size()));
		}
	}

	/**
	 * 构建输出文件路径
	 */
	private String buildOutputFilePath() {
		appendLog(String.format("节点 【%s】 开始构建输出文件路径", pipelineNode.getProperties().getName()));

		String filePath = writeExcelNode.getFilePath();
		String fileName = writeExcelNode.getFileName();

		// 验证路径和文件名
		if (filePath == null || filePath.trim().isEmpty()) {
			throw new RuntimeException(String.format("节点 【%s】 文件路径不能为空",
					pipelineNode.getProperties().getName()));
		}

		if (fileName == null || fileName.trim().isEmpty()) {
			throw new RuntimeException(String.format("节点 【%s】 文件名不能为空",
					pipelineNode.getProperties().getName()));
		}

		appendLog(String.format("节点 【%s】 配置的文件路径：%s", pipelineNode.getProperties().getName(), filePath));
		appendLog(String.format("节点 【%s】 配置的文件名：%s", pipelineNode.getProperties().getName(), fileName));

		// 确保文件路径以/结尾
		if (!filePath.endsWith("/") && !filePath.endsWith("\\")) {
			filePath += "/";
		}

		String fullPath = filePath + fileName;
		appendLog(String.format("节点 【%s】 构建的完整文件路径：%s", pipelineNode.getProperties().getName(), fullPath));

		return fullPath;
	}

	/**
	 * 创建Workbook对象
	 */
	private Workbook createWorkbook(String outputFilePath) {
		appendLog(String.format("节点 【%s】 开始创建Workbook，文件路径：%s",
				pipelineNode.getProperties().getName(), outputFilePath));

		// 检查内存情况
		Runtime runtime = Runtime.getRuntime();
		long freeMemory = runtime.freeMemory();
		long maxMemory = runtime.maxMemory();
		long totalMemory = runtime.totalMemory();
		long usedMemory = totalMemory - freeMemory;

		appendLog(String.format("节点 【%s】 创建Workbook前内存检查 - 已用：%d MB，可用：%d MB，最大：%d MB",
				pipelineNode.getProperties().getName(),
				usedMemory / 1024 / 1024,
				freeMemory / 1024 / 1024,
				maxMemory / 1024 / 1024));

		// 如果可用内存不足100MB，先尝试垃圾回收
		if (freeMemory < 100 * 1024 * 1024) {
			appendLog(String.format("节点 【%s】 内存不足，尝试垃圾回收", pipelineNode.getProperties().getName()));
			System.gc();
			ThreadUtil.sleep(1000); // 等待1秒让GC完成

			// 重新检查内存
			freeMemory = runtime.freeMemory();
			appendLog(String.format("节点 【%s】 垃圾回收后可用内存：%d MB",
					pipelineNode.getProperties().getName(), freeMemory / 1024 / 1024));
		}

		try {
			// 使用超时机制创建Workbook
			return createWorkbookWithTimeout(outputFilePath, 30000); // 30秒超时
		} catch (Exception e) {
			throw new RuntimeException(String.format("节点 【%s】 创建Workbook失败：%s",
					pipelineNode.getProperties().getName(), e.getMessage()));
		}
	}

	/**
	 * 使用超时机制创建Workbook对象
	 */
	private Workbook createWorkbookWithTimeout(String outputFilePath, long timeoutMs) throws Exception {
		appendLog(String.format("节点 【%s】 使用超时机制创建Workbook，超时时间：%d 秒",
				pipelineNode.getProperties().getName(), timeoutMs / 1000));

		// 使用Future来实现超时控制
		ExecutorService executor = Executors.newSingleThreadExecutor();
		Future<Workbook> future = executor.submit(() -> {
			try {
				return createWorkbookInternal(outputFilePath);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});

		try {
			// 等待结果，设置超时
			Workbook workbook = future.get(timeoutMs, TimeUnit.MILLISECONDS);
			appendLog(String.format("节点 【%s】 Workbook创建成功", pipelineNode.getProperties().getName()));
			return workbook;
		} catch (TimeoutException e) {
			future.cancel(true);
			throw new RuntimeException(String.format("节点 【%s】 创建Workbook超时（%d秒）",
					pipelineNode.getProperties().getName(), timeoutMs / 1000));
		} catch (ExecutionException e) {
			Throwable cause = e.getCause();
			if (cause instanceof RuntimeException) {
				throw (RuntimeException) cause;
			} else {
				throw new RuntimeException(cause);
			}
		} finally {
			executor.shutdown();
		}
	}

	/**
	 * 内部创建Workbook的实际逻辑
	 */
	private Workbook createWorkbookInternal(String outputFilePath) throws IOException {
		appendLog(String.format("节点 【%s】 开始内部创建Workbook逻辑", pipelineNode.getProperties().getName()));

		// 如果是追加模式且文件存在，则打开现有文件
		if ("append".equals(writeExcelNode.getWriteMode()) && Files.exists(Paths.get(outputFilePath))) {
			appendLog(String.format("节点 【%s】 追加模式，文件已存在，准备打开现有文件",
					pipelineNode.getProperties().getName()));

			// 检查文件是否可读
			if (!Files.isReadable(Paths.get(outputFilePath))) {
				throw new IOException(String.format("节点 【%s】 现有文件不可读：%s",
						pipelineNode.getProperties().getName(), outputFilePath));
			}

			// 检查文件大小，如果文件过大可能导致内存问题
			long fileSize = Files.size(Paths.get(outputFilePath));
			appendLog(String.format("节点 【%s】 现有文件大小：%d MB",
					pipelineNode.getProperties().getName(), fileSize / 1024 / 1024));

			if (fileSize > 50 * 1024 * 1024) { // 文件大于50MB时警告
				appendLog(String.format("节点 【%s】 警告：现有文件较大（%d MB），可能影响性能",
						pipelineNode.getProperties().getName(), fileSize / 1024 / 1024));
			}

			try (FileInputStream fis = new FileInputStream(outputFilePath)) {
				if (outputFilePath.toLowerCase().endsWith(".xlsx")) {
					appendLog(String.format("节点 【%s】 创建XLSX格式的Workbook（追加模式）",
							pipelineNode.getProperties().getName()));
					return new XSSFWorkbook(fis);
				} else if (outputFilePath.toLowerCase().endsWith(".xls")) {
					appendLog(String.format("节点 【%s】 创建XLS格式的Workbook（追加模式）",
							pipelineNode.getProperties().getName()));
					return new HSSFWorkbook(fis);
				}
			}
		}

		// 创建新的Workbook
		appendLog(String.format("节点 【%s】 创建新的Workbook文件", pipelineNode.getProperties().getName()));

		try {
			if (outputFilePath.toLowerCase().endsWith(".xlsx")) {
				appendLog(String.format("节点 【%s】 开始创建XLSX格式的新Workbook",
						pipelineNode.getProperties().getName()));

				// 设置POI的临时文件目录（避免默认临时目录权限问题）
				try {
					String tempDir = System.getProperty("user.dir") + "/temp";
					Path tempPath = Paths.get(tempDir);
					if (!Files.exists(tempPath)) {
						Files.createDirectories(tempPath);
						appendLog(String.format("节点 【%s】 创建临时目录：%s",
								pipelineNode.getProperties().getName(), tempDir));
					}
					System.setProperty("java.io.tmpdir", tempDir);
				} catch (Exception e) {
					appendLog(String.format("节点 【%s】 设置临时目录失败，使用默认临时目录：%s",
							pipelineNode.getProperties().getName(), e.getMessage()));
				}

				XSSFWorkbook workbook = new XSSFWorkbook();
				appendLog(String.format("节点 【%s】 XLSX格式Workbook创建成功",
						pipelineNode.getProperties().getName()));
				return workbook;
			} else if (outputFilePath.toLowerCase().endsWith(".xls")) {
				appendLog(String.format("节点 【%s】 开始创建XLS格式的新Workbook",
						pipelineNode.getProperties().getName()));

				HSSFWorkbook workbook = new HSSFWorkbook();
				appendLog(String.format("节点 【%s】 XLS格式Workbook创建成功",
						pipelineNode.getProperties().getName()));
				return workbook;
			} else {
				throw new IllegalArgumentException(String.format("节点 【%s】 不支持的文件格式：%s，仅支持.xls和.xlsx格式",
						pipelineNode.getProperties().getName(), outputFilePath));
			}
		} catch (OutOfMemoryError e) {
			appendLog(String.format("节点 【%s】 创建Workbook时内存不足", pipelineNode.getProperties().getName()));
			throw new IOException(String.format("节点 【%s】 创建Workbook时内存不足，请增加JVM堆内存",
					pipelineNode.getProperties().getName()), e);
		}
	}

	/**
	 * 获取或创建工作表
	 */
	private Sheet getOrCreateSheet(Workbook workbook, String sheetName) {
		Sheet sheet = workbook.getSheet(sheetName);
		if (sheet == null) {
			sheet = workbook.createSheet(sheetName);
		}
		return sheet;
	}

	/**
	 * 写入标题行
	 */
	private void writeHeaderRow(Sheet sheet, int rowIndex) {
		if (effectiveColumnMappings.isEmpty()) {
			appendLog(String.format("Node 【%s】 no column mappings available, skipping header row",
					pipelineNode.getProperties().getName()));
			return;
		}

		Row headerRow = sheet.createRow(rowIndex);
		int startColIndex = columnToIndex(writeExcelNode.getStartColumn());

		for (int i = 0; i < effectiveColumnMappings.size(); i++) {
			WriteExcelNode.ColumnMapping mapping = effectiveColumnMappings.get(i);
			Cell cell = headerRow.createCell(startColIndex + i);
			cell.setCellValue(mapping.getTargetColumn());

			// 设置标题行样式
			CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
			Font headerFont = sheet.getWorkbook().createFont();
			headerFont.setBold(true);
			headerStyle.setFont(headerFont);
			headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			headerStyle.setBorderBottom(BorderStyle.THIN);
			headerStyle.setBorderTop(BorderStyle.THIN);
			headerStyle.setBorderRight(BorderStyle.THIN);
			headerStyle.setBorderLeft(BorderStyle.THIN);
			cell.setCellStyle(headerStyle);
		}
	}

	/**
	 * 批量写入数据
	 */
	private int writeDataBatch(Sheet sheet, int startRowIndex) {
		int currentRowIndex = startRowIndex;

		if (CollectionUtils.isEmpty(dataList)) {
			return currentRowIndex;
		}

		Map<String, Object> firstDataMap = dataList.get(0);
		// 如果列映射为空或者映射个数与dataList不等，需要先获取一条数据来初始化列映射
		List<String> columnMappings = effectiveColumnMappings.stream().map(WriteExcelNode.ColumnMapping::getSourceColumn).collect(Collectors.toList());
		if (effectiveColumnMappings.isEmpty() || !columnMappings.containsAll(firstDataMap.keySet())) {
			initializeColumnMappingsFromFirstData();
		}

		//映射完仍然为空
		if (effectiveColumnMappings.isEmpty()) {
			appendLog(String.format("Node 【%s】 no column mappings available, cannot write data",
					pipelineNode.getProperties().getName()));
			return currentRowIndex;
		}

		// 写入标题行（仅在覆盖模式下）
		if (writeExcelNode.getIncludeHeader() && !hasWriteHeaderRow && shouldWriteHeaderRow) {
			writeHeaderRow(sheet, startRowIndex);
			hasWriteHeaderRow = true;
			currentRowIndex++;
		}

		int startColIndex = columnToIndex(writeExcelNode.getStartColumn());

		for (Map<String, Object> dataMap : dataList) {
			Row row = sheet.createRow(currentRowIndex);

			for (int i = 0; i < effectiveColumnMappings.size(); i++) {
				WriteExcelNode.ColumnMapping mapping = effectiveColumnMappings.get(i);
				Cell cell = row.createCell(startColIndex + i);

				Object value = dataMap.get(mapping.getSourceColumn());
				setCellValue(cell, value, mapping);
			}

			currentRowIndex++;
		}

		if (totalCount % 10000 == 0) {
			appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
			updateLog();
		}

		return currentRowIndex;
	}

	/**
	 * 设置单元格值
	 */
	private void setCellValue(Cell cell, Object value, WriteExcelNode.ColumnMapping mapping) {
		if (value == null) {
			handleNullValue(cell);
			return;
		}

		try {
			switch (mapping.getColumnType()) {
				case "STRING":
					cell.setCellValue(value.toString());
					break;
				case "NUMERIC":
					if (value instanceof Number) {
						cell.setCellValue(((Number) value).doubleValue());
					} else {
						try {
							// 优先使用numberFormatter解析数字字符串
							Number numValue = numberFormatter.parse(value.toString());
							cell.setCellValue(numValue.doubleValue());
						} catch (ParseException e) {
							// 如果numberFormatter解析失败，尝试使用Double.parseDouble
							try {
								double numValue = Double.parseDouble(value.toString());
								cell.setCellValue(numValue);
							} catch (NumberFormatException ex) {
								// 如果都失败了，设置为字符串
								cell.setCellValue(value.toString());
							}
						}
					}
					// 应用数字格式
					CellStyle numStyle = cell.getSheet().getWorkbook().createCellStyle();
					DataFormat format = cell.getSheet().getWorkbook().createDataFormat();
					String numberFormat;
					if (StringUtil.isNotBlank(mapping.getFormat())) {
						// 使用映射中指定的格式
						numberFormat = mapping.getFormat();
					} else {
						// 使用全局数字格式
						numberFormat = writeExcelNode.getNumberFormat();
					}
					numStyle.setDataFormat(format.getFormat(numberFormat));
					cell.setCellStyle(numStyle);
					break;
				case "DATE":
					Date dateValue;
					if (value instanceof Date) {
						dateValue = (Date) value;
					} else {
						try {
							dateValue = dateFormatter.parse(value.toString());
						} catch (ParseException e) {
							cell.setCellValue(value.toString());
							return;
						}
					}
					cell.setCellValue(dateValue);
					// 应用日期格式
					CellStyle dateStyle = cell.getSheet().getWorkbook().createCellStyle();
					CreationHelper createHelper = cell.getSheet().getWorkbook().getCreationHelper();
					String dateFormat = StringUtil.isNotBlank(mapping.getFormat()) ? mapping.getFormat() : writeExcelNode.getDateFormat();
					dateStyle.setDataFormat(createHelper.createDataFormat().getFormat(dateFormat));
					cell.setCellStyle(dateStyle);
					break;
				case "BOOLEAN":
					if (value instanceof Boolean) {
						cell.setCellValue((Boolean) value);
					} else {
						cell.setCellValue(Boolean.parseBoolean(value.toString()));
					}
					break;
				default:
					cell.setCellValue(value.toString());
					break;
			}
		} catch (Exception e) {
			// 如果转换失败，设置为字符串值
			cell.setCellValue(value.toString());
		}
	}

	/**
	 * 处理空值
	 */
	private void handleNullValue(Cell cell) {
		switch (writeExcelNode.getNullValueHandling()) {
			case "keep":
				// 保持空值，不设置任何内容
				break;
			case "empty_string":
				cell.setCellValue("");
				break;
			case "default_value":
				cell.setCellValue(writeExcelNode.getDefaultValue());
				break;
			default:
				break;
		}
	}

	/**
	 * 应用高级设置
	 */
	private void applyAdvancedSettings(Sheet sheet) {
		if (effectiveColumnMappings.isEmpty()) {
			return;
		}

		// 自动调整列宽
		if (writeExcelNode.getAutoSizeColumns()) {
			int startColIndex = columnToIndex(writeExcelNode.getStartColumn());
			for (int i = 0; i < effectiveColumnMappings.size(); i++) {
				int colIndex = startColIndex + i;
				sheet.autoSizeColumn(colIndex);

				// 获取自动调整后的列宽
				int autoWidth = sheet.getColumnWidth(colIndex);

				// 设置最小列宽（约20个字符的宽度）
				int minWidth = 20 * 256;

				// 设置最大列宽（约50个字符的宽度，避免过宽）
				int maxWidth = 50 * 256;

				// 对于中文内容，适当增加列宽
				int adjustedWidth = Math.max(autoWidth, minWidth);
				adjustedWidth = Math.min(adjustedWidth, maxWidth);

				// 如果自动调整的宽度太小，则使用调整后的宽度
				if (autoWidth < minWidth) {
					sheet.setColumnWidth(colIndex, adjustedWidth);
				}
			}
		}

		// 冻结窗格
		if (writeExcelNode.getFreezePane()) {
			int freezeRows = writeExcelNode.getFreezeRows();
			int freezeCols = writeExcelNode.getFreezeCols();
			sheet.createFreezePane(freezeCols, freezeRows);
		}
	}

	/**
	 * 将列字母转换为索引（A=0, B=1, ...）
	 */
	private int columnToIndex(String column) {
		if (StringUtil.isBlank(column)) {
			return 0;
		}

		int result = 0;
		for (int i = 0; i < column.length(); i++) {
			result = result * 26 + (column.charAt(i) - 'A' + 1);
		}
		return result - 1;
	}

	/**
	 * 将索引转换为列字母（0=A, 1=B, ...）
	 */
	private String indexToColumn(int index) {
		StringBuilder result = new StringBuilder();
		while (index >= 0) {
			result.insert(0, (char) ('A' + index % 26));
			index = index / 26 - 1;
		}
		return result.toString();
	}

	/**
	 * 从第一条数据初始化列映射
	 */
	private void initializeColumnMappingsFromFirstData() {
		appendLog(String.format("节点 【%s】 尝试从首条数据获取字段映射",
				pipelineNode.getProperties().getName()));

		// 尝试获取第一条数据来初始化列映射
		int maxAttempts = 100; // 最多尝试100次，避免无限循环
		int attempts = 0;

		while (effectiveColumnMappings.isEmpty() && attempts < maxAttempts) {
			if (isStop()) {
				throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			}

			Map<String, Object> dataMap = dataList.get(0);
			if (dataMap != null) {
				// 生成列映射
				if (writeExcelNode.getColumnMappings() == null || writeExcelNode.getColumnMappings().isEmpty()) {
					generateColumnMappingsFromData(dataMap);
				} else {
					// 如果有部分配置，则补充缺失的映射
					effectiveColumnMappings = new ArrayList<>(writeExcelNode.getColumnMappings());
					supplementColumnMappings(dataMap);
				}

				break;
			}

			attempts++;
			ThreadUtil.sleep(100); // 等待100ms再尝试
		}

		if (effectiveColumnMappings.isEmpty()) {
			appendLog(String.format("节点 【%s】 从首条来源数据获取字段映射失败",
					pipelineNode.getProperties().getName()));
		} else {
			appendLog(String.format("节点 【%s】 从首条来源数据获取字段映射成功，字段数量：%s",
					pipelineNode.getProperties().getName(), effectiveColumnMappings.size()));
		}
	}
}
