package com.bjgy.etl.engine.impl.extract;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.node.extract.ReadKafkaNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;

/**
 * @ClassName ReadKafka
 */
public class ReadKafka extends EtlEngine {

	private KafkaConsumer<String, String> consumer;
	private final ObjectMapper objectMapper = new ObjectMapper();

	public ReadKafka(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	@SneakyThrows
	@Override
	public void run() {
		initCache();
		Integer overTimes = pipelineNodeEntity.getOverTimes();
		long startTime = System.currentTimeMillis();
		long totalCount = 0;

		try {
			ReadKafkaNode readKafkaNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, ReadKafkaNode.class);
			String dependNodeNo = readKafkaNode.getDependNodeNo();

			// 等待依赖节点
			waitForDependNode(dependNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));

			// 创建Kafka消费者
			consumer = createKafkaConsumer(readKafkaNode);

			// 验证连接
			try {
				appendLog(String.format("节点 【%s】 正在验证Kafka连接：%s",
						pipelineNode.getProperties().getName(), readKafkaNode.getBootstrapServers()));

				// 尝试获取主题分区信息来验证连接
				consumer.partitionsFor(readKafkaNode.getTopic());

				appendLog(String.format("节点 【%s】 Kafka连接验证成功", pipelineNode.getProperties().getName()));
			} catch (Exception e) {
				if (consumer != null) {
					try {
						consumer.close();
					} catch (Exception closeException) {
						// 忽略关闭异常
					}
				}
				throw new RuntimeException(String.format("节点 【%s】 Kafka连接验证失败，无法连接到：%s，错误信息：%s",
						pipelineNode.getProperties().getName(), readKafkaNode.getBootstrapServers(), e.getMessage()), e);
			}

			consumer.subscribe(Collections.singletonList(readKafkaNode.getTopic()));

			// 判断运行模式
			String runMode = readKafkaNode.getRunMode();
			Long maxMessageCount = readKafkaNode.getMaxMessageCount() == null ? 1000L : readKafkaNode.getMaxMessageCount();
			boolean isUnlimitedMode = "unlimited".equals(runMode);

			if (isUnlimitedMode) {
				appendLog(String.format("节点 【%s】 开始消费Kafka主题：%s（永不停止模式）", pipelineNode.getProperties().getName(), readKafkaNode.getTopic()));
			} else {
				appendLog(String.format("节点 【%s】 开始消费Kafka主题：%s（限制模式，最大消息数：%d）",
						pipelineNode.getProperties().getName(), readKafkaNode.getTopic(), maxMessageCount));
			}
			
			// 消费开始前再次验证连接状态
			try {
				appendLog(String.format("节点 【%s】 验证消费前Kafka连接状态", pipelineNode.getProperties().getName()));
				consumer.partitionsFor(readKafkaNode.getTopic());
				appendLog(String.format("节点 【%s】 消费前连接状态正常", pipelineNode.getProperties().getName()));
			} catch (Exception e) {
				throw new RuntimeException(String.format("节点 【%s】 消费前Kafka连接状态异常，无法开始消费：%s", 
						pipelineNode.getProperties().getName(), e.getMessage()), e);
			}
			
			updateLog();

			// 消费消息
			long lastConnectionCheckTime = System.currentTimeMillis();
			int emptyPollCount = 0; // 连续空轮询计数
			
			while (true) {
				dependFailedCheck(dependNodeNo);
				if (isStop()) {
					throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
				}
				// 检查是否达到消息数量限制
				if (!isUnlimitedMode && totalCount >= maxMessageCount) {
					appendLog(String.format("节点 【%s】 已达到最大消息数限制：%d，停止消费", pipelineNode.getProperties().getName(), maxMessageCount));
					break;
				}

				// 只有在非永不停止模式下才检查超时
				if (!isUnlimitedMode) {
					long currentTime = System.currentTimeMillis();
					if (checkOverTime(startTime, currentTime, overTimes)) {
						appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
						appendLog(String.format("节点 【%s】 运行超时，已消费数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
						updateLog();
						break;
					}
				}

				// 定期检查连接状态（每5分钟检查一次）
				long currentTime = System.currentTimeMillis();
				if (currentTime - lastConnectionCheckTime > 300000) { // 5分钟
					try {
						appendLog(String.format("节点 【%s】 定期检查Kafka连接状态", pipelineNode.getProperties().getName()));
						consumer.partitionsFor(readKafkaNode.getTopic());
						lastConnectionCheckTime = currentTime;
						emptyPollCount = 0; // 重置空轮询计数
					} catch (Exception e) {
						throw new RuntimeException(String.format("节点 【%s】 Kafka连接状态异常：%s", 
								pipelineNode.getProperties().getName(), e.getMessage()), e);
					}
				}

				ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(readKafkaNode.getPollTimeoutMs()));

				if (records.isEmpty()) {
					emptyPollCount++;
					
					// 如果连续空轮询次数过多，检查连接状态
					if (emptyPollCount >= 10) { // 连续10次空轮询
						try {
							appendLog(String.format("节点 【%s】 连续空轮询，检查Kafka连接状态", pipelineNode.getProperties().getName()));
							consumer.partitionsFor(readKafkaNode.getTopic());
							emptyPollCount = 0; // 重置计数
						} catch (Exception e) {
							throw new RuntimeException(String.format("节点 【%s】 连续空轮询时发现Kafka连接异常：%s", 
									pipelineNode.getProperties().getName(), e.getMessage()), e);
						}
					}
				} else {
					emptyPollCount = 0; // 有数据时重置空轮询计数
				}

				for (ConsumerRecord<String, String> record : records) {
					dependFailedCheck(dependNodeNo);
					if (isStop()) {
						throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
					}

					// 构建消息数据
					Map<String, Object> messageData = buildMessageData(record, readKafkaNode);

					// 向内存队列中推送数据
					pushDataToDownstream(messageData);
					totalCount++;

					// 每处理1000条数据打印一次日志
					if (totalCount % 1000 == 0) {
						if (isUnlimitedMode) {
							appendLog(String.format("节点 【%s】 已消费 %d 条消息（永不停止模式）", pipelineNode.getProperties().getName(), totalCount));
						} else {
							appendLog(String.format("节点 【%s】 已消费 %d 条消息（剩余：%d）",
									pipelineNode.getProperties().getName(), totalCount, maxMessageCount - totalCount));
						}
						updateLog();
					}

					// 控制队列大小
					while (getCurrentQueueSize() >= 1000) {
						dependFailedCheck(dependNodeNo);
						if (isStop()) {
							throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
						}
						ThreadUtil.sleep(100);
					}

					// 检查是否达到消息数量限制（在处理每条消息后检查）
					if (!isUnlimitedMode && totalCount >= maxMessageCount) {
						break;
					}
				}

				// 如果没有消息，短暂休眠
				if (records.isEmpty()) {
					// 根据连续空轮询次数调整休眠时间，避免过度轮询
					int sleepTime = Math.min(100 + emptyPollCount * 10, 1000); // 最少100ms，最多1000ms
					ThreadUtil.sleep(sleepTime);
				}
			}

			appendLog(String.format("节点 【%s】 运行成功结束，消费数据总数：%s", pipelineNode.getProperties().getName(), totalCount));
			successEnd();

		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，消费数据总数：%s，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			failEnd();
		} finally {
			if (consumer != null) {
				try {
					consumer.close();
				} catch (Exception e) {
					appendLog(String.format("节点 【%s】 关闭Kafka消费者失败：%s", pipelineNode.getProperties().getName(), e.getMessage()));
				}
			}
		}
	}

	/**
	 * 创建Kafka消费者
	 */
	private KafkaConsumer<String, String> createKafkaConsumer(ReadKafkaNode readKafkaNode) {
		Properties props = new Properties();

		// 基础配置
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, readKafkaNode.getBootstrapServers());
		props.put(ConsumerConfig.GROUP_ID_CONFIG, readKafkaNode.getGroupId());
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

		// 消费配置
		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, readKafkaNode.getAutoOffsetReset());
		props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, readKafkaNode.getMaxPollRecords());
		props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, readKafkaNode.getMaxPollIntervalMs());

		// 连接超时配置
		props.put(ConsumerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 30000); // 30秒空闲连接超时
		props.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000); // 30秒请求超时

		// 自动提交配置
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, readKafkaNode.getEnableAutoCommit());
		if (readKafkaNode.getEnableAutoCommit()) {
			props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, readKafkaNode.getAutoCommitIntervalMs());
		}

		// 会话配置
		props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, readKafkaNode.getSessionTimeoutMs());
		props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, readKafkaNode.getHeartbeatIntervalMs());

		// 安全配置
		if (!"PLAINTEXT".equals(readKafkaNode.getSecurityProtocol())) {
			props.put("security.protocol", readKafkaNode.getSecurityProtocol());

			if (readKafkaNode.getSecurityProtocol().contains("SASL")) {
				props.put("sasl.mechanism", readKafkaNode.getSaslMechanism());
				if (StringUtils.isNotBlank(readKafkaNode.getSaslUsername()) &&
						StringUtils.isNotBlank(readKafkaNode.getSaslPassword())) {
					String jaasConfig = String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
							readKafkaNode.getSaslUsername(), readKafkaNode.getSaslPassword());
					props.put("sasl.jaas.config", jaasConfig);
				}
			}
		}

		return new KafkaConsumer<>(props);
	}

	/**
	 * 构建消息数据
	 */
	private Map<String, Object> buildMessageData(ConsumerRecord<String, String> record, ReadKafkaNode readKafkaNode) {
		Map<String, Object> messageData = new HashMap<>();

		// 基础字段
		messageData.put("key", record.key());
		messageData.put("topic", record.topic());
		messageData.put("partition", record.partition());
		messageData.put("offset", record.offset());
		messageData.put("timestamp", record.timestamp());

		// 处理消息值
		String value = record.value();
		if ("JSON".equals(readKafkaNode.getMessageFormat())) {
			try {
				// 尝试解析JSON
				JsonNode jsonNode = objectMapper.readTree(value);
				if (jsonNode.isObject()) {
					// 如果是JSON对象，展开字段
					Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();
					while (fields.hasNext()) {
						Map.Entry<String, JsonNode> field = fields.next();
						messageData.put(field.getKey(), getJsonValue(field.getValue()));
					}
				} else {
					messageData.put("value", value);
				}
			} catch (Exception e) {
				// JSON解析失败，作为字符串处理
				messageData.put("value", value);
			}
		} else {
			messageData.put("value", value);
		}

		return messageData;
	}

	/**
	 * 获取JSON值
	 */
	private Object getJsonValue(JsonNode jsonNode) {
		if (jsonNode.isNull()) {
			return null;
		} else if (jsonNode.isBoolean()) {
			return jsonNode.booleanValue();
		} else if (jsonNode.isInt()) {
			return jsonNode.intValue();
		} else if (jsonNode.isLong()) {
			return jsonNode.longValue();
		} else if (jsonNode.isDouble()) {
			return jsonNode.doubleValue();
		} else if (jsonNode.isTextual()) {
			return jsonNode.textValue();
		} else {
			return jsonNode.toString();
		}
	}

	/**
	 * 获取当前队列大小
	 * 如果启用了数据复制机制，返回所有下游队列的最大大小
	 * 否则返回原有单一队列的大小
	 */
	private long getCurrentQueueSize() {
		if (isDataReplicationEnabled()) {
			// 数据复制机制：返回所有下游队列中的最大大小
			long maxQueueSize = 0;
			for (String downstreamNodeId : downstreamNodeIds) {
				String dataKey = downstreamDataKeys.get(downstreamNodeId);
				if (dataKey != null) {
					long queueSize = memoryManager.getQueueSize(dataKey);
					maxQueueSize = Math.max(maxQueueSize, queueSize);
				}
			}
			return maxQueueSize;
		} else {
			// 原有机制：返回单一队列大小
			return memoryManager.getQueueSize(NODE_DATA_KEY);
		}
	}
}
