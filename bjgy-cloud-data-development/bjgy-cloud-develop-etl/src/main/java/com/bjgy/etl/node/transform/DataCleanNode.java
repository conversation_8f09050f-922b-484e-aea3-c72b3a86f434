/*
 * Copyright (c) 2024 天津数睿通科技有限公司
 * All rights reserved.
 */
package com.bjgy.etl.node.transform;

import lombok.Data;

import java.util.List;

/**
 * 数据清洗节点配置类
 *
 * @ClassName DataCleanNode
 */
@Data
public class DataCleanNode {

    // 基本配置
    private String dependNodeNo;                    // 数据源节点编号
    private String cleanMode;                       // 清洗模式：STANDARD/CUSTOM

    // 去除字段配置
    private Boolean enableFieldRemoval;             // 启用去除字段
    private String fieldsToRemove;                  // 要去除的字段，逗号分隔

    // 数据去重配置
    private Boolean enableDeduplication;            // 启用数据去重
    private String deduplicationStrategy;           // 去重策略：ALL_FIELDS/KEY_FIELDS
    private String keyFields;                       // 关键字段，逗号分隔
    private String keepStrategy;                    // 保留策略：FIRST/LAST/LATEST
    private String compareField;                  // 比较字段，用于LATEST策略判断数据新旧（值越大越新）

    // 空值处理配置
    private Boolean enableNullHandling;             // 启用空值处理
    private String nullHandlingStrategy;            // 空值策略：REMOVE/FILL_DEFAULT/FILL_PREVIOUS/FILL_AVERAGE
    private List<DefaultValue> defaultValues;       // 默认值配置

    // 数据格式化配置
    private Boolean enableFormatting;               // 启用数据格式化
    private List<FormatRule> formatRules;           // 格式化规则

    // 异常值处理配置
    private Boolean enableOutlierHandling;          // 启用异常值处理
    private List<OutlierRule> outlierRules;         // 异常值规则

    // 自定义清洗配置
    private String scriptType;                      // 脚本类型：JAVASCRIPT/PYTHON/SQL
    private String javascriptCode;                  // JavaScript代码
    private String pythonCode;                      // Python代码（暂不实现）
    private String sqlCode;                         // SQL代码

    // 性能优化配置
    private Integer batchSize;                      // 批处理大小
    private Boolean enableParallelProcessing;       // 启用并行处理
    private Boolean enableMemoryOptimization;       // 启用内存优化

    // 错误处理
    private String errorHandling;                   // 错误处理策略：SKIP/STOP/LOG

    // 输出配置
    private Boolean outputStatistics;               // 输出统计信息
    private Boolean keepOriginalData;               // 保留原始数据

    /**
     * 默认值配置
     */
    @Data
    public static class DefaultValue {
        private String fieldName;                   // 字段名
        private String defaultValue;                // 默认值
        private String dataType;                    // 数据类型：STRING/NUMBER/DATE/BOOLEAN
    }

    /**
     * 格式化规则
     */
    @Data
    public static class FormatRule {
        private String fieldName;                   // 字段名
        private String formatType;                  // 格式化类型：TRIM/UPPER_CASE/LOWER_CASE/CAPITALIZE/DATE_FORMAT/NUMBER_FORMAT/REGEX_REPLACE
        private String formatPattern;               // 格式参数
        private String regexPattern;                // 正则表达式
        private String replacement;                 // 替换值
    }

    /**
     * 异常值规则
     */
    @Data
    public static class OutlierRule {
        private String fieldName;                   // 字段名
        private String detectionMethod;             // 检测方法：RANGE/STANDARD_DEVIATION/IQR/REGEX
        private String minValue;                    // 最小值
        private String maxValue;                    // 最大值
        private Double stdDevThreshold;             // 标准差阈值
        private String regexPattern;                // 正则表达式
        private String handleMethod;                // 处理方式：REMOVE/REPLACE/FLAG
        private String replaceValue;                // 替换值
    }
}
