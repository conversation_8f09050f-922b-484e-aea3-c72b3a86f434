package com.bjgy.etl.engine.impl.load;

import com.zaxxer.hikari.HikariDataSource;
import lombok.SneakyThrows;
import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
import com.bjgy.api.module.data.integrate.dto.DataDatabaseDto;
import com.bjgy.dto.PipelineNode;
import com.bjgy.etl.engine.EtlEngine;
import com.bjgy.etl.engine.constant.DataSyncType;
import com.bjgy.etl.node.load.InsertDbNode;
import com.bjgy.flink.common.utils.ThreadUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import bjgy.cloud.framework.dbswitch.common.type.ProductTypeEnum;
import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
import bjgy.cloud.framework.dbswitch.core.model.ColumnDescription;
import bjgy.cloud.framework.dbswitch.core.service.IMetaDataByJdbcService;
import bjgy.cloud.framework.dbswitch.core.service.impl.MetaDataByJdbcServiceImpl;
import bjgy.cloud.framework.dbswitch.dbcommon.database.DatabaseOperatorFactory;
import bjgy.cloud.framework.dbswitch.dbcommon.database.IDatabaseOperator;
import bjgy.cloud.framework.dbswitch.dbsynch.DatabaseSynchronizeFactory;
import bjgy.cloud.framework.dbswitch.dbsynch.IDatabaseSynchronize;
import bjgy.cloud.framework.dbswitch.dbwriter.DatabaseWriterFactory;
import bjgy.cloud.framework.dbswitch.dbwriter.IDatabaseWriter;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ReadDb
 */
public class InsertDb extends EtlEngine {


	private static final Logger logger = LoggerFactory.getLogger(InsertDb.class);

	public InsertDb(PipelineNode pipelineNode) {
		super(pipelineNode);
	}

	private String sourceDataKey;
	private String sourceDataStatusKey;
	private List<String> sourcePrimaryKeys;
	private List<String> columns;
	private List<String> updateColumns;
	private IDatabaseWriter databaseWriter;
	private IDatabaseSynchronize databaseSynchronize;
	private IDatabaseOperator databaseOperator;
	private InsertDbNode insertDbNode;
	private DataDatabaseDto database;
	private long totalCount = 0;
	private int batchSize = 1000;
	private Integer dataSyncType;
	private Integer ignoreEx;
	private List<Map<String, Object>> dataList = new ArrayList<>();
	private List<ColumnDescription> columnDescriptions = new ArrayList<>();
	private List<ColumnDescription> updateColumnDescriptions = new ArrayList<>();


	@Override
	public void run() {
		//初始化缓存
		initCache();
		long startTime = System.currentTimeMillis();
		try {
			Integer overTimes = pipelineNodeEntity.getOverTimes();
			insertDbNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, InsertDbNode.class);
			database = buildDatabase(insertDbNode.getDbType(), insertDbNode.getDatabaseId());
			ignoreEx = insertDbNode.getIgnoreEx();
			sourcePrimaryKeys = insertDbNode.getSourcePrimaryKeys();
			dataSyncType = insertDbNode.getDataSyncType();
			//获取数据节点
			String sourceNodeNo = insertDbNode.getDependNodeNo();
			// 等待依赖节点
			waitForDependNode(sourceNodeNo);
			appendLog(String.format("节点 【%s】 开始运行", pipelineNode.getProperties().getName()));
			sourceDataKey = getSourceDataKey(sourceNodeNo);
			sourceDataStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + sourceNodeNo;
			//初始化数据库
			DataDatabaseDto database = buildDatabase(insertDbNode.getDbType(), insertDbNode.getDatabaseId());
			try (HikariDataSource dataSource = createDataSource(database)) {
				ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(database.getDatabaseType());
				IMetaDataByJdbcService service = new MetaDataByJdbcServiceImpl(productTypeEnum);
				columnDescriptions = service.queryTableColumnMeta(database.getJdbcUrl(), database.getUserName(), database.getPassword(), database.getDatabaseSchema(), insertDbNode.getTableName());
				columns = columnDescriptions.stream().map(ColumnDescription::getFieldName).collect(Collectors.toList());
				databaseOperator = DatabaseOperatorFactory.createDatabaseOperator(dataSource, productTypeEnum);
				databaseWriter = DatabaseWriterFactory.createDatabaseWriter(dataSource, productTypeEnum, true);
				databaseWriter.prepareWrite(database.getDatabaseSchema(), insertDbNode.getTableName(), columns);
				if (DataSyncType.ifSync(dataSyncType)) {
					updateColumns = insertDbNode.getUpdateColumns();
					if (!CollectionUtils.isEmpty(updateColumns)) {
						for (String sourcePrimaryKey : sourcePrimaryKeys) {
							if (!updateColumns.contains(sourcePrimaryKey)) {
								updateColumns.add(sourcePrimaryKey);
							}
						}
						// 创建 fieldName 到 ColumnDescription 的映射
						Map<String, ColumnDescription> columnMap = columnDescriptions.stream()
								.collect(Collectors.toMap(ColumnDescription::getFieldName, Function.identity()));
						// 按照 updateColumns 的顺序获取 ColumnDescription
						updateColumnDescriptions = updateColumns.stream()
								.map(columnMap::get)
								.filter(Objects::nonNull)
								.collect(Collectors.toList());
					} else {
						//为空，更新全部
						updateColumns = columns;
						updateColumnDescriptions = columnDescriptions;
					}
					databaseSynchronize = DatabaseSynchronizeFactory.createDatabaseWriter(dataSource, productTypeEnum);
					databaseSynchronize.prepare(database.getDatabaseSchema(), insertDbNode.getTableName(), updateColumns, sourcePrimaryKeys);
				}
				if (Boolean.TRUE.equals(insertDbNode.getTargetTruncate())) {
					databaseOperator.truncateTableData(database.getDatabaseSchema(), insertDbNode.getTableName());
					dataSyncType = DataSyncType.ifInsert(dataSyncType) ? DataSyncType.ONLY_INSERT.getCode() : dataSyncType;
				}
				while (true) {
					dependFailedCheck(sourceNodeNo);
					if (isStop()) {
						throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
					}
					try {
						long currentTime = System.currentTimeMillis();
						if (checkOverTime(startTime, currentTime, overTimes)) {
							appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
							appendLog(String.format("节点 【%s】 运行超时，已处理数据总数：%d", pipelineNode.getProperties().getName(), totalCount));
							//更新节点日志
							updateLog();
							break;
						}
						dealData();
						//判断节点状态
						Integer sourceDataStatus = memoryManager.getNodeStatus(sourceDataStatusKey);
						//如果源节点执行完毕，退出
						if (CommonRunStatus.isSuccess(sourceDataStatus)) {
							//获取剩余个数
							long dealSize = memoryManager.getQueueSize(sourceDataKey);
							for (int i = 0; i < dealSize; i++) {
								dealData();
							}
							break;
						}
						ThreadUtil.sleep(20);
					} catch (Exception e) {
						if (ignoreEx == 1) {
							appendLog(String.format("节点 【%s】 运行错误：%s", pipelineNode.getProperties().getName(), e.getMessage()));
							appendLog(String.format("节点 【%s】 忽略异常设置为true，继续执行", pipelineNode.getProperties().getName()));
							continue;
						}
						throw new RuntimeException(e);
					}
				}
				dealRemainData();
			}
			appendLog(String.format("节点 【%s】 运行成功结束，处理数据总数：%s", pipelineNode.getProperties().getName(), totalCount));
			//成功结束
			successEnd();
		} catch (Exception e) {
			appendLog(String.format("节点 【%s】 运行失败结束，处理数据总数：%s，错误信息：%s", pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			//失败结束
			failEnd();
		}
	}

	private void dealData() {
		Map<String, Object> dataMap = memoryManager.rightPop(sourceDataKey);
		if (dataMap != null) {
			totalCount += 1;
			dataList.add(dataMap);

			// 每处理10000条数据打印一次日志
			if (totalCount % 10000 == 0) {
				appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
				updateLog();
			}
		}
		if (dataList.size() % batchSize == 0) {
			//处理数据
			operatorData();
			dataList.clear();
		}
	}

	private void dealRemainData() {
		if (!dataList.isEmpty()) {
			//处理数据
			operatorData();
		}
	}

	@SneakyThrows
	private void operatorData() {
		if (DataSyncType.ONLY_INSERT.getCode().equals(dataSyncType)) {
			databaseWriter.write(columns, buildRecords(dataList, DataSyncType.ONLY_INSERT));
		} else if (DataSyncType.INSERT_UPDATE.getCode().equals(dataSyncType)) {
			List<Map<String, Object>> insertDatas = new ArrayList<>();
			List<Map<String, Object>> updateDatas = new ArrayList<>();
			for (Map<String, Object> map : dataList) {
				Map<String, Object> pkVal = new HashMap<>();
				for (String sourcePrimaryKey : sourcePrimaryKeys) {
					pkVal.put(sourcePrimaryKey, map.get(sourcePrimaryKey));
				}
				Boolean pkExist = databaseOperator.getPkExist(database.getDatabaseSchema(), insertDbNode.getTableName(), pkVal);
				if (pkExist) {
					updateDatas.add(map);
				} else {
					insertDatas.add(map);
				}
			}
			if (!CollectionUtils.isEmpty(insertDatas)) {
				databaseWriter.write(columns, buildRecords(insertDatas, DataSyncType.ONLY_INSERT));
			}
			if (!CollectionUtils.isEmpty(updateDatas)) {
				databaseSynchronize.executeUpdate(buildRecords(updateDatas, DataSyncType.ONLY_UPDATE));
			}
		} else if (DataSyncType.ONLY_UPDATE.getCode().equals(dataSyncType)) {
			databaseSynchronize.executeUpdate(buildRecords(dataList, DataSyncType.ONLY_UPDATE));
		}
	}

	private Map<String, Object> buildTargetData(Map<String, String> columnMap, Map<String, Object> dataMap) {
		Map<String, Object> targetData = new HashMap<>();
		for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
			String key = entry.getKey();
			Object value = entry.getValue();
			targetData.put(columnMap.getOrDefault(key, key), value);
		}
		return targetData;
	}

	private List<Object[]> buildRecords(List<Map<String, Object>> dataList, DataSyncType dataSyncType) {
		List<Object[]> list = new ArrayList<>();
		for (Map<String, Object> map : dataList) {
			List<Object> objects = new ArrayList<>();
			for (ColumnDescription columnDescription : DataSyncType.ONLY_INSERT.equals(dataSyncType) ? columnDescriptions : updateColumnDescriptions) {
				//对应的是java.sql.Types
				int fieldType = columnDescription.getFieldType();
				Object value = map.get(columnDescription.getFieldName());
				// 数据类型转换逻辑
				try {
					if (value == null) {
						objects.add(null);
					} else {
						Object convertedValue = convertValueByFieldType(value, fieldType);
						objects.add(convertedValue);
					}
				} catch (Exception e) {
					// 转换失败时记录日志并使用原始值
					logger.warn("字段 {} 数据类型转换失败，使用原始值。原始值：{}，目标类型：{}，错误：{}",
							columnDescription.getFieldName(), value, fieldType, e.getMessage());
					objects.add(value);
				}
			}
			list.add(objects.toArray());
		}
		return list;
	}

	/**
	 * 根据数据库字段类型转换数据值
	 *
	 * @param value     原始值
	 * @param fieldType 数据库字段类型（java.sql.Types）
	 * @return 转换后的值
	 */
	private Object convertValueByFieldType(Object value, int fieldType) {
		if (value == null) {
			return null;
		}

		String valueStr = value.toString().trim();
		if (valueStr.isEmpty()) {
			return null;
		}

		try {
			switch (fieldType) {
				// 字符串类型
				case java.sql.Types.VARCHAR:
				case java.sql.Types.CHAR:
				case java.sql.Types.LONGVARCHAR:
				case java.sql.Types.NVARCHAR:
				case java.sql.Types.NCHAR:
				case java.sql.Types.LONGNVARCHAR:
				case java.sql.Types.CLOB:
				case java.sql.Types.NCLOB:
					return valueStr;

				// 整数类型
				case java.sql.Types.TINYINT:
				case java.sql.Types.SMALLINT:
				case java.sql.Types.INTEGER:
					if (value instanceof Number) {
						return ((Number) value).intValue();
					}
					return Integer.parseInt(valueStr);

				case java.sql.Types.BIGINT:
					if (value instanceof Number) {
						return ((Number) value).longValue();
					}
					return Long.parseLong(valueStr);

				// 浮点数类型
				case java.sql.Types.FLOAT:
				case java.sql.Types.REAL:
					if (value instanceof Number) {
						return ((Number) value).floatValue();
					}
					return Float.parseFloat(valueStr);

				case java.sql.Types.DOUBLE:
					if (value instanceof Number) {
						return ((Number) value).doubleValue();
					}
					return Double.parseDouble(valueStr);

				// 精确数值类型
				case java.sql.Types.DECIMAL:
				case java.sql.Types.NUMERIC:
					if (value instanceof BigDecimal) {
						return value;
					}
					if (value instanceof Number) {
						return BigDecimal.valueOf(((Number) value).doubleValue());
					}
					return new BigDecimal(valueStr);

				// 布尔类型
				case java.sql.Types.BOOLEAN:
				case java.sql.Types.BIT:
					if (value instanceof Boolean) {
						return value;
					}
					// 支持多种布尔值表示
					String lowerValue = valueStr.toLowerCase();
					return "true".equals(lowerValue) || "1".equals(lowerValue) ||
							"yes".equals(lowerValue) || "y".equals(lowerValue);

				// 日期时间类型
				case java.sql.Types.DATE:
					if (value instanceof java.sql.Date) {
						return value;
					}
					if (value instanceof java.util.Date) {
						return new java.sql.Date(((java.util.Date) value).getTime());
					}
					if (value instanceof Long) {
						return new java.sql.Date((Long) value);
					}
					// 尝试解析日期字符串
					return parseDate(valueStr);

				case java.sql.Types.TIME:
					if (value instanceof java.sql.Time) {
						return value;
					}
					if (value instanceof java.util.Date) {
						return new java.sql.Time(((java.util.Date) value).getTime());
					}
					if (value instanceof Long) {
						return new java.sql.Time((Long) value);
					}
					return parseTime(valueStr);

				case java.sql.Types.TIMESTAMP:
					if (value instanceof java.sql.Timestamp) {
						return value;
					}
					if (value instanceof java.util.Date) {
						return new java.sql.Timestamp(((java.util.Date) value).getTime());
					}
					if (value instanceof Long) {
						return new java.sql.Timestamp((Long) value);
					}
					return parseTimestamp(valueStr);

				// 二进制类型
				case java.sql.Types.BINARY:
				case java.sql.Types.VARBINARY:
				case java.sql.Types.LONGVARBINARY:
				case java.sql.Types.BLOB:
					if (value instanceof byte[]) {
						return value;
					}
					return valueStr.getBytes();

				// 其他类型默认返回字符串
				default:
					return valueStr;
			}
		} catch (Exception e) {
			throw new RuntimeException("数据类型转换失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解析日期字符串
	 */
	private java.sql.Date parseDate(String dateStr) {
		try {
			// 尝试多种日期格式
			String[] patterns = {
					"yyyy-MM-dd",
					"yyyy/MM/dd",
					"dd/MM/yyyy",
					"dd-MM-yyyy",
					"yyyyMMdd"
			};

			for (String pattern : patterns) {
				try {
					SimpleDateFormat sdf = new SimpleDateFormat(pattern);
					java.util.Date date = sdf.parse(dateStr);
					return new java.sql.Date(date.getTime());
				} catch (Exception ignored) {
					// 继续尝试下一个格式
				}
			}

			// 如果都失败了，抛出异常
			throw new RuntimeException("无法解析日期字符串: " + dateStr);
		} catch (Exception e) {
			throw new RuntimeException("日期解析失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解析时间字符串
	 */
	private java.sql.Time parseTime(String timeStr) {
		try {
			String[] patterns = {
					"HH:mm:ss",
					"HH:mm",
					"HHmmss",
					"HHmm"
			};

			for (String pattern : patterns) {
				try {
					SimpleDateFormat sdf = new SimpleDateFormat(pattern);
					java.util.Date date = sdf.parse(timeStr);
					return new java.sql.Time(date.getTime());
				} catch (Exception ignored) {
					// 继续尝试下一个格式
				}
			}

			throw new RuntimeException("无法解析时间字符串: " + timeStr);
		} catch (Exception e) {
			throw new RuntimeException("时间解析失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解析时间戳字符串
	 */
	private java.sql.Timestamp parseTimestamp(String timestampStr) {
		try {
			String[] patterns = {
					"yyyy-MM-dd HH:mm:ss",
					"yyyy-MM-dd HH:mm:ss.SSS",
					"yyyy/MM/dd HH:mm:ss",
					"dd/MM/yyyy HH:mm:ss",
					"yyyyMMdd HHmmss",
					"yyyy-MM-dd'T'HH:mm:ss",
					"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
			};

			for (String pattern : patterns) {
				try {
					SimpleDateFormat sdf = new SimpleDateFormat(pattern);
					java.util.Date date = sdf.parse(timestampStr);
					return new java.sql.Timestamp(date.getTime());
				} catch (Exception ignored) {
					// 继续尝试下一个格式
				}
			}

			throw new RuntimeException("无法解析时间戳字符串: " + timestampStr);
		} catch (Exception e) {
			throw new RuntimeException("时间戳解析失败: " + e.getMessage(), e);
		}
	}
}
