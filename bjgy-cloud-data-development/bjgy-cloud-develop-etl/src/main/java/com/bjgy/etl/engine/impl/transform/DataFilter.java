 /*
  * Copyright (c) 2024 天津数睿通科技有限公司
  * All rights reserved.
  */
 package com.bjgy.etl.engine.impl.transform;

 import com.bjgy.api.module.data.integrate.constant.CommonRunStatus;
 import com.bjgy.dto.PipelineNode;
 import com.bjgy.etl.engine.EtlEngine;
 import com.bjgy.etl.node.transform.DataFilterNode;
 import bjgy.cloud.framework.dbswitch.common.util.SingletonObject;
 import bjgy.cloud.framework.dbswitch.common.util.StringUtil;

 import java.math.BigDecimal;
 import java.text.ParseException;
 import java.text.SimpleDateFormat;
 import java.util.*;
 import java.util.Arrays;
 import java.util.concurrent.ConcurrentLinkedQueue;
 import java.util.concurrent.ExecutorService;
 import java.util.concurrent.Executors;
 import java.util.concurrent.Future;
 import java.util.regex.Pattern;

 /**
  * 数据过滤器组件处理逻辑
  * 支持简单过滤和高级过滤两种模式
  *
  * @ClassName DataFilter
  */
 public class DataFilter extends EtlEngine {

	 public DataFilter(PipelineNode pipelineNode) {
		 super(pipelineNode);
	 }

	 private DataFilterNode dataFilterNode;
	 private String sourceDataKey;
	 private String sourceStatusKey;
	 private long totalCount = 0;
	 private long filteredCount = 0;
	 private long errorCount = 0;
	 private List<Map<String, Object>> dataBuffer = new ArrayList<>();
	 private ExecutorService executorService;
	 private Queue<Map<String, Object>> filteredDataLog = new ConcurrentLinkedQueue<>();

	 // 日期格式化器
	 private final List<SimpleDateFormat> dateFormatters = Arrays.asList(
			 new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
			 new SimpleDateFormat("yyyy-MM-dd"),
			 new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"),
			 new SimpleDateFormat("yyyy/MM/dd"),
			 new SimpleDateFormat("MM/dd/yyyy"),
			 new SimpleDateFormat("dd/MM/yyyy")
	 );

	 @Override
	 public void run() {
		 // 初始化缓存
		 initCache();
		 long startTime = System.currentTimeMillis();

		 try {
			 Integer overTimes = pipelineNodeEntity.getOverTimes();
			 dataFilterNode = SingletonObject.OBJECT_MAPPER.readValue(nodeJson, DataFilterNode.class);

			 // 验证配置
			 validateConfiguration();

			 // 初始化数据源
			 initializeDataSource();

			 // 初始化并行处理
			 if (Boolean.TRUE.equals(dataFilterNode.getEnableParallelProcessing())) {
				 executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
			 }

			 appendLog(String.format("节点 【%s】 开始运行，过滤模式：%s，批处理大小：%d",
					 pipelineNode.getProperties().getName(),
					 dataFilterNode.getFilterMode(),
					 dataFilterNode.getBatchSize()));

			 // 开始数据过滤处理
			 processDataFiltering(startTime, overTimes);

			 appendLog(String.format("节点 【%s】 运行成功结束，总处理数据：%d 条，过滤通过：%d 条，过滤掉：%d 条，错误：%d 条",
					 pipelineNode.getProperties().getName(), totalCount,
					 totalCount - filteredCount - errorCount, filteredCount, errorCount));
			 successEnd();

		 } catch (Exception e) {
			 appendLog(String.format("节点 【%s】 运行失败结束，总处理数据：%d 条，错误信息：%s",
					 pipelineNode.getProperties().getName(), totalCount, e.getMessage()));
			 failEnd();
		 } finally {
			 // 关闭线程池
			 if (executorService != null && !executorService.isShutdown()) {
				 executorService.shutdown();
			 }
		 }
	 }

	 /**
	  * 验证配置
	  */
	 private void validateConfiguration() {
		 if (StringUtil.isBlank(dataFilterNode.getDependNodeNo())) {
			 throw new RuntimeException("数据过滤器必须配置数据源节点");
		 }

		 if (StringUtil.isBlank(dataFilterNode.getFilterMode())) {
			 throw new RuntimeException("必须选择过滤模式");
		 }

		 if ("SIMPLE".equals(dataFilterNode.getFilterMode())) {
			 if (dataFilterNode.getFilterConditions() == null || dataFilterNode.getFilterConditions().isEmpty()) {
				 throw new RuntimeException("简单过滤模式下至少需要添加一个过滤条件");
			 }

			 // 验证每个条件的完整性
			 for (int i = 0; i < dataFilterNode.getFilterConditions().size(); i++) {
				 DataFilterNode.FilterCondition condition = dataFilterNode.getFilterConditions().get(i);
				 if (StringUtil.isBlank(condition.getFieldName())) {
					 throw new RuntimeException(String.format("第%d个过滤条件的字段名不能为空", i + 1));
				 }

				 if (!isNullOperator(condition.getOperator())) {
					 if (isBetweenOperator(condition.getOperator())) {
						 if (StringUtil.isBlank(condition.getMinValue()) || StringUtil.isBlank(condition.getMaxValue())) {
							 throw new RuntimeException(String.format("第%d个过滤条件的范围值不能为空", i + 1));
						 }
					 } else if (StringUtil.isBlank(condition.getValue())) {
						 throw new RuntimeException(String.format("第%d个过滤条件的比较值不能为空", i + 1));
					 }
				 }
			 }
		 } else if ("ADVANCED".equals(dataFilterNode.getFilterMode())) {
			 if ("CUSTOM".equals(dataFilterNode.getExpressionType()) && StringUtil.isBlank(dataFilterNode.getCustomExpression())) {
				 throw new RuntimeException("自定义表达式不能为空");
			 }

			 if ("SQL".equals(dataFilterNode.getExpressionType()) && StringUtil.isBlank(dataFilterNode.getSqlCondition())) {
				 throw new RuntimeException("SQL条件不能为空");
			 }
		 }

		 // 设置默认值
		 if (dataFilterNode.getBatchSize() == null || dataFilterNode.getBatchSize() <= 0) {
			 dataFilterNode.setBatchSize(1000);
		 }

		 if (StringUtil.isBlank(dataFilterNode.getErrorHandling())) {
			 dataFilterNode.setErrorHandling("SKIP");
		 }
	 }

	 /**
	  * 初始化数据源
	  */
	 private void initializeDataSource() {
		 String dependNodeNo = dataFilterNode.getDependNodeNo();

		 // 等待依赖节点
		 waitForDependNode(dependNodeNo);

		 // 构建数据源key
		 sourceDataKey = getSourceDataKey(dependNodeNo);
		 sourceStatusKey = NODE_STATUS_KEY_PREFIX + pipelineNode.getProperties().getRecordId() + ":" + dependNodeNo;
	 }

	 /**
	  * 处理数据过滤
	  */
	 private void processDataFiltering(long startTime, Integer overTimes) {
		 long lastStatusCheckTime = System.currentTimeMillis();
		 int cycleCount = 0;

		 while (true) {
			 cycleCount++;

			 // 检查是否需要停止
			 if (isStop()) {
				 throw new RuntimeException(String.format("节点 【%s】 已被停止", pipelineNode.getProperties().getName()));
			 }

			 // 检查超时
			 long currentTime = System.currentTimeMillis();
			 if (checkOverTime(startTime, currentTime, overTimes)) {
				 appendLog(String.format("节点 【%s】 运行超时，停止执行", pipelineNode.getProperties().getName()));
				 break;
			 }

			 // 检查依赖节点失败
			 dependFailedCheck(dataFilterNode.getDependNodeNo());

			 // 收集数据进行批处理
			 boolean hasData = collectDataForBatch();

			 if (hasData) {
				 // 执行数据过滤
				 processBatchFiltering();

				 if (totalCount % 10000 == 0 && totalCount > 0) {
					 appendLog(String.format("节点 【%s】 已处理 %d 条数据", pipelineNode.getProperties().getName(), totalCount));
					 updateLog();
				 }
			 }

			 // 定期检查和记录状态（每30秒）
			 if (currentTime - lastStatusCheckTime > 30000) {
				 appendLog(String.format("节点 【%s】 状态检查 - 循环次数：%d，已处理：%d 条，通过：%d 条，过滤：%d 条，错误：%d 条",
						 pipelineNode.getProperties().getName(), cycleCount, totalCount,
						 totalCount - filteredCount - errorCount, filteredCount, errorCount));
				 lastStatusCheckTime = currentTime;
				 updateLog();
			 }

			 // 检查数据源是否完成
			 Integer sourceStatus = memoryManager.getNodeStatus(sourceStatusKey);
			 if (CommonRunStatus.isSuccess(sourceStatus)) {
				 appendLog(String.format("节点 【%s】 数据源已完成，开始处理剩余数据", pipelineNode.getProperties().getName()));
				 // 处理剩余数据
				 processRemainingData();
				 break;
			 }

			 // 短暂休眠
			 try {
				 Thread.sleep(50);
			 } catch (InterruptedException e) {
				 Thread.currentThread().interrupt();
				 break;
			 }
		 }
	 }

	 /**
	  * 收集数据进行批处理
	  */
	 private boolean collectDataForBatch() {
		 boolean hasNewData = false;

		 // 从队列中获取数据
		 for (int i = 0; i < dataFilterNode.getBatchSize() && dataBuffer.size() < dataFilterNode.getBatchSize(); i++) {
			 Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
			 if (data != null) {
				 dataBuffer.add(data);
				 hasNewData = true;
			 } else {
				 break;
			 }
		 }

		 return hasNewData && dataBuffer.size() >= dataFilterNode.getBatchSize();
	 }

	 /**
	  * 执行批量过滤处理
	  */
	 private void processBatchFiltering() {
		 if (dataBuffer.isEmpty()) {
			 return;
		 }

		 List<Map<String, Object>> currentBatch = new ArrayList<>(dataBuffer);
		 dataBuffer.clear();

		 if (Boolean.TRUE.equals(dataFilterNode.getEnableParallelProcessing()) && executorService != null) {
			 // 并行处理
			 processDataInParallel(currentBatch);
		 } else {
			 // 串行处理
			 processDataSequentially(currentBatch);
		 }
	 }

	 /**
	  * 并行处理数据
	  */
	 private void processDataInParallel(List<Map<String, Object>> dataList) {
		 int threadCount = Runtime.getRuntime().availableProcessors();
		 int batchSize = Math.max(1, dataList.size() / threadCount);
		 List<Future<Void>> futures = new ArrayList<>();

		 for (int i = 0; i < dataList.size(); i += batchSize) {
			 int endIndex = Math.min(i + batchSize, dataList.size());
			 List<Map<String, Object>> subList = dataList.subList(i, endIndex);

			 Future<Void> future = executorService.submit(() -> {
				 processDataSequentially(subList);
				 return null;
			 });
			 futures.add(future);
		 }

		 // 等待所有任务完成
		 for (Future<Void> future : futures) {
			 try {
				 future.get();
			 } catch (Exception e) {
				 appendLog(String.format("节点 【%s】 并行处理出现错误：%s", pipelineNode.getProperties().getName(), e.getMessage()));
			 }
		 }
	 }

	 /**
	  * 串行处理数据
	  */
	 private void processDataSequentially(List<Map<String, Object>> dataList) {
		 for (Map<String, Object> data : dataList) {
			 try {
				 boolean passFilter = applyFilter(data);
				 totalCount++;

				 if (passFilter) {
					 // 数据通过过滤，推送到下游
					 pushDataToDownstream(data);
				 } else {
					 // 数据被过滤掉
					 filteredCount++;
					 if (Boolean.TRUE.equals(dataFilterNode.getLogFilteredData())) {
						 logFilteredData(data);
					 }
				 }

			 } catch (Exception e) {
				 errorCount++;
				 handleProcessingError(data, e);
			 }
		 }
	 }

	 /**
	  * 应用过滤器
	  */
	 private boolean applyFilter(Map<String, Object> data) {
		 if ("SIMPLE".equals(dataFilterNode.getFilterMode())) {
			 return applySimpleFilter(data);
		 } else if ("ADVANCED".equals(dataFilterNode.getFilterMode())) {
			 return applyAdvancedFilter(data);
		 }
		 return true;
	 }

	 /**
	  * 应用简单过滤
	  */
	 private boolean applySimpleFilter(Map<String, Object> data) {
		 if (dataFilterNode.getFilterConditions() == null || dataFilterNode.getFilterConditions().isEmpty()) {
			 return true;
		 }

		 boolean isAnd = "AND".equals(dataFilterNode.getLogicalOperator());

		 for (DataFilterNode.FilterCondition condition : dataFilterNode.getFilterConditions()) {
			 boolean conditionResult = evaluateCondition(data, condition);

			 if (isAnd) {
				 // AND逻辑：有任何一个条件为false，直接返回false
				 if (!conditionResult) {
					 return false;
				 }
			 } else {
				 // OR逻辑：有任何一个条件为true，直接返回true
				 if (conditionResult) {
					 return true;
				 }
			 }
		 }

		 // AND逻辑：所有条件都为true，返回true
		 // OR逻辑：所有条件都为false，返回false
		 return isAnd;
	 }

	 /**
	  * 评估单个条件
	  */
	 private boolean evaluateCondition(Map<String, Object> data, DataFilterNode.FilterCondition condition) {
		 // 支持大小写不敏感的字段名匹配
		 Object fieldValue = getFieldValue(data, condition.getFieldName());
		 String operator = condition.getOperator();

		 // 处理空值操作符
		 if (isNullOperator(operator)) {
			 if ("IS_NULL".equals(operator)) {
				 return fieldValue == null;
			 } else if ("IS_NOT_NULL".equals(operator)) {
				 return fieldValue != null;
			 }
		 }

		 // 如果字段值为空，其他操作都返回false
		 if (fieldValue == null) {
			 return false;
		 }

		 // 数据类型验证
		 if (Boolean.TRUE.equals(dataFilterNode.getEnableDataValidation())) {
			 if (!validateDataType(fieldValue, condition.getDataType())) {
				 return false;
			 }
		 }

		 // 根据操作符进行比较
		 switch (operator) {
			 case "EQUALS":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) == 0;
			 case "NOT_EQUALS":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) != 0;
			 case "GREATER_THAN":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) > 0;
			 case "GREATER_EQUAL":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) >= 0;
			 case "LESS_THAN":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) < 0;
			 case "LESS_EQUAL":
				 return compareValues(fieldValue, condition.getValue(), condition.getDataType()) <= 0;
			 case "CONTAINS":
				 return evaluateStringOperation(fieldValue, condition.getValue(), "CONTAINS");
			 case "NOT_CONTAINS":
				 return !evaluateStringOperation(fieldValue, condition.getValue(), "CONTAINS");
			 case "STARTS_WITH":
				 return evaluateStringOperation(fieldValue, condition.getValue(), "STARTS_WITH");
			 case "ENDS_WITH":
				 return evaluateStringOperation(fieldValue, condition.getValue(), "ENDS_WITH");
			 case "REGEX":
				 return evaluateRegexOperation(fieldValue, condition.getValue());
			 case "IN":
				 return isValueInList(fieldValue, condition.getValue(), condition.getDataType());
			 case "NOT_IN":
				 return !isValueInList(fieldValue, condition.getValue(), condition.getDataType());
			 case "BETWEEN":
				 return evaluateBetweenOperation(fieldValue, condition.getMinValue(), condition.getMaxValue(), condition.getDataType());
			 default:
				 return true;
		 }
	 }

	 /**
	  * 获取字段值（支持大小写不敏感）
	  */
	 private Object getFieldValue(Map<String, Object> data, String fieldName) {
		 if (fieldName == null) {
			 return null;
		 }

		 // 首先尝试精确匹配
		 Object value = data.get(fieldName);
		 if (value != null) {
			 return value;
		 }

		 // 如果精确匹配失败，尝试大小写不敏感匹配
		 for (Map.Entry<String, Object> entry : data.entrySet()) {
			 if (entry.getKey() != null && entry.getKey().equalsIgnoreCase(fieldName)) {
				 return entry.getValue();
			 }
		 }

		 return null;
	 }

	 /**
	  * 评估字符串操作
	  */
	 private boolean evaluateStringOperation(Object fieldValue, String compareValue, String operation) {
		 if (fieldValue == null || compareValue == null) {
			 return false;
		 }

		 String fieldStr = fieldValue.toString();

		 switch (operation) {
			 case "CONTAINS":
				 return fieldStr.contains(compareValue);
			 case "STARTS_WITH":
				 return fieldStr.startsWith(compareValue);
			 case "ENDS_WITH":
				 return fieldStr.endsWith(compareValue);
			 default:
				 return false;
		 }
	 }

	 /**
	  * 评估正则表达式操作
	  */
	 private boolean evaluateRegexOperation(Object fieldValue, String regexPattern) {
		 if (fieldValue == null || regexPattern == null) {
			 return false;
		 }

		 try {
			 return Pattern.matches(regexPattern, fieldValue.toString());
		 } catch (Exception e) {
			 // 正则表达式格式错误时记录日志并返回false
			 appendLog(String.format("节点 【%s】 正则表达式格式错误：%s，字段值：%s",
					 pipelineNode.getProperties().getName(), regexPattern, fieldValue.toString()));
			 return false;
		 }
	 }

	 /**
	  * 评估BETWEEN操作
	  */
	 private boolean evaluateBetweenOperation(Object fieldValue, String minValue, String maxValue, String dataType) {
		 if (fieldValue == null || minValue == null || maxValue == null) {
			 return false;
		 }

		 try {
			 return isValueBetween(fieldValue, minValue, maxValue, dataType);
		 } catch (Exception e) {
			 // BETWEEN操作异常时记录日志并返回false
			 appendLog(String.format("节点 【%s】 BETWEEN操作异常：%s，字段值：%s，范围：[%s, %s]",
					 pipelineNode.getProperties().getName(), e.getMessage(), fieldValue.toString(), minValue, maxValue));
			 return false;
		 }
	 }

	 /**
	  * 应用高级过滤
	  */
	 private boolean applyAdvancedFilter(Map<String, Object> data) {
		 if ("CUSTOM".equals(dataFilterNode.getExpressionType())) {
			 return evaluateCustomExpression(data, dataFilterNode.getCustomExpression());
		 } else if ("SQL".equals(dataFilterNode.getExpressionType())) {
			 return evaluateSqlCondition(data, dataFilterNode.getSqlCondition());
		 }
		 return true;
	 }

	 /**
	  * 评估自定义表达式
	  */
	 private boolean evaluateCustomExpression(Map<String, Object> data, String expression) {
		 try {
			 // 简化版本：支持基本的逻辑表达式
			 return evaluateSimpleExpression(data, expression);
		 } catch (Exception e) {
			 appendLog(String.format("节点 【%s】 自定义表达式评估失败：%s，表达式：%s",
					 pipelineNode.getProperties().getName(), e.getMessage(), expression));
			 return false;
		 }
	 }

	 /**
	  * 评估SQL条件
	  */
	 private boolean evaluateSqlCondition(Map<String, Object> data, String sqlCondition) {
		 try {
			 // 简化版本：将SQL条件转换为简单表达式处理
			 return evaluateSimpleExpression(data, sqlCondition);
		 } catch (Exception e) {
			 appendLog(String.format("节点 【%s】 SQL条件评估失败：%s，条件：%s",
					 pipelineNode.getProperties().getName(), e.getMessage(), sqlCondition));
			 return false;
		 }
	 }

	 /**
	  * 评估简单表达式 - 简化版本
	  */
	 private boolean evaluateSimpleExpression(Map<String, Object> data, String expression) {
		 if (expression == null || expression.trim().isEmpty()) {
			 return true; // 空表达式默认返回true
		 }

		 expression = expression.trim();

		 // 处理OR运算符（优先级最低）
		 if (containsIgnoreCase(expression, " OR ")) {
			 String[] orParts = splitIgnoreCase(expression, " OR ");

			 for (String orPart : orParts) {
				 String part = orPart.trim();
				 boolean partResult = evaluateAndExpression(data, part);
				 if (partResult) {
					 return true; // OR：任意一个为true就返回true
				 }
			 }
			 return false;
		 }

		 // 处理AND运算符
		 return evaluateAndExpression(data, expression);
	 }

	 /**
	  * 评估AND表达式 - 简化版本
	  */
	 private boolean evaluateAndExpression(Map<String, Object> data, String expression) {
		 if (expression == null || expression.trim().isEmpty()) {
			 return true; // 空表达式默认返回true
		 }

		 if (containsIgnoreCase(expression, " AND ")) {
			 String[] andParts = splitIgnoreCase(expression, " AND ");

			 for (String andPart : andParts) {
				 String part = andPart.trim();
				 boolean partResult = evaluateSingleCondition(data, part);
				 if (!partResult) {
					 return false; // AND：任意一个为false就返回false
				 }
			 }
			 return true;
		 }

		 // 单个条件
		 return evaluateSingleCondition(data, expression);
	 }

	 /**
	  * 评估单个条件 - 简化版本
	  */
	 private boolean evaluateSingleCondition(Map<String, Object> data, String condition) {
		 if (condition == null || condition.trim().isEmpty()) {
			 return true; // 空条件默认返回true
		 }

		 condition = condition.trim();

		 // 处理NOT操作符
		 if (condition.toUpperCase().startsWith("NOT ")) {
			 String innerCondition = condition.substring(4).trim();
			 return !evaluateSingleCondition(data, innerCondition);
		 }

		 // 移除外层括号
		 if (condition.startsWith("(") && condition.endsWith(")")) {
			 // 检查括号是否匹配
			 if (isBalancedParentheses(condition)) {
				 condition = condition.substring(1, condition.length() - 1).trim();
				 return evaluateSimpleExpression(data, condition);
			 }
		 }

		 // 处理各种比较运算符（按长度排序，避免误匹配）
		 if (condition.contains(">=")) {
			 return evaluateComparison(data, condition, ">=");
		 } else if (condition.contains("<=")) {
			 return evaluateComparison(data, condition, "<=");
		 } else if (condition.contains("!=") || condition.contains("<>")) {
			 String op = condition.contains("!=") ? "!=" : "<>";
			 return evaluateComparison(data, condition, op);
		 } else if (condition.contains(">")) {
			 return evaluateComparison(data, condition, ">");
		 } else if (condition.contains("<")) {
			 return evaluateComparison(data, condition, "<");
		 } else if (condition.contains("=")) {
			 return evaluateComparison(data, condition, "=");
		 } else if (containsIgnoreCase(condition, " NOT LIKE ")) {
			 return !evaluateLikeCondition(data, condition.replaceAll("(?i)\\s+NOT\\s+LIKE\\s+", " LIKE "));
		 } else if (containsIgnoreCase(condition, " LIKE ")) {
			 return evaluateLikeCondition(data, condition);
		 } else if (containsIgnoreCase(condition, " NOT IN ")) {
			 return !evaluateInCondition(data, condition.replaceAll("(?i)\\s+NOT\\s+IN\\s+", " IN "));
		 } else if (containsIgnoreCase(condition, " IN ")) {
			 return evaluateInCondition(data, condition);
		 } else if (containsIgnoreCase(condition, " IS NOT NULL")) {
			 return evaluateNullCondition(data, condition, false);
		 } else if (containsIgnoreCase(condition, " IS NULL")) {
			 return evaluateNullCondition(data, condition, true);
		 }

		 // 处理布尔字段
		 return evaluateBooleanField(data, condition);
	 }

	 /**
	  * 检查括号是否平衡
	  */
	 private boolean isBalancedParentheses(String expression) {
		 int count = 0;
		 for (char c : expression.toCharArray()) {
			 if (c == '(') {
				 count++;
			 } else if (c == ')') {
				 count--;
				 if (count < 0) {
					 return false;
				 }
			 }
		 }
		 return count == 0;
	 }

	 /**
	  * 评估比较条件
	  */
	 private boolean evaluateComparison(Map<String, Object> data, String condition, String operator) {
		 String[] parts = condition.split(Pattern.quote(operator), 2);
		 if (parts.length != 2) {
			 return false;
		 }

		 String leftPart = parts[0].trim();
		 String rightPart = parts[1].trim();

		 Object leftValue = getFieldValueOrLiteral(data, leftPart);
		 Object rightValue = getFieldValueOrLiteral(data, rightPart);

		 return compareValues(leftValue, rightValue, operator);
	 }

	 /**
	  * 评估LIKE条件
	  */
	 private boolean evaluateLikeCondition(Map<String, Object> data, String condition) {
		 String[] parts = splitIgnoreCase(condition, " LIKE ");
		 if (parts.length != 2) {
			 return false;
		 }

		 Object leftValue = getFieldValueOrLiteral(data, parts[0].trim());
		 Object rightValue = getFieldValueOrLiteral(data, parts[1].trim());

		 if (leftValue == null || rightValue == null) {
			 return false;
		 }

		 String text = leftValue.toString();
		 String pattern = rightValue.toString();

		 try {
			 // 安全的LIKE实现
			 // 1. 转义所有正则表达式特殊字符
			 StringBuilder regexPattern = new StringBuilder();
			 for (int i = 0; i < pattern.length(); i++) {
				 char c = pattern.charAt(i);
				 if (c == '%') {
					 regexPattern.append(".*");
				 } else if (c == '_') {
					 regexPattern.append(".");
				 } else {
					 // 转义其他特殊字符
					 if ("[]{}()*+?^$|\\".indexOf(c) != -1) {
						 regexPattern.append("\\");
					 }
					 regexPattern.append(c);
				 }
			 }

			 return text.matches(regexPattern.toString());
		 } catch (Exception e) {
			 // 如果正则表达式有问题，使用简单的字符串匹配
			 if (pattern.contains("%") || pattern.contains("_")) {
				 // 简单的通配符匹配
				 String simplePattern = pattern.replace("%", "").replace("_", "");
				 return text.contains(simplePattern);
			 } else {
				 // 精确匹配
				 return text.equals(pattern);
			 }
		 }
	 }

	 /**
	  * 评估IN条件
	  */
	 private boolean evaluateInCondition(Map<String, Object> data, String condition) {
		 String[] parts = splitIgnoreCase(condition, " IN ");
		 if (parts.length != 2) {
			 return false;
		 }

		 Object leftValue = getFieldValueOrLiteral(data, parts[0].trim());
		 String rightPart = parts[1].trim();

		 // 移除括号
		 if (rightPart.startsWith("(") && rightPart.endsWith(")")) {
			 rightPart = rightPart.substring(1, rightPart.length() - 1);
		 }

		 // 处理空的IN列表
		 if (rightPart.trim().isEmpty()) {
			 return false;
		 }

		 String[] values = rightPart.split(",");
		 for (String value : values) {
			 Object rightValue = getFieldValueOrLiteral(data, value.trim());
			 if (compareValues(leftValue, rightValue, "=")) {
				 return true;
			 }
		 }
		 return false;
	 }

	 /**
	  * 评估NULL条件
	  */
	 private boolean evaluateNullCondition(Map<String, Object> data, String condition, boolean isNull) {
		 String fieldPart = condition.split("(?i)\\s+IS\\s+(NOT\\s+)?NULL")[0].trim();
		 Object value = getFieldValueOrLiteral(data, fieldPart);
		 return isNull == (value == null);
	 }

	 /**
	  * 评估布尔字段
	  */
	 private boolean evaluateBooleanField(Map<String, Object> data, String condition) {
		 Object value = getFieldValueOrLiteral(data, condition);
		 if (value instanceof Boolean) {
			 return (Boolean) value;
		 }
		 if (value != null) {
			 String strValue = value.toString().toLowerCase();
			 return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
		 }
		 return false;
	 }

	 /**
	  * 获取字段值或字面量
	  */
	 private Object getFieldValueOrLiteral(Map<String, Object> data, String expression) {
		 expression = expression.trim();

		 // 字符串字面量
		 if ((expression.startsWith("'") && expression.endsWith("'")) ||
			 (expression.startsWith("\"") && expression.endsWith("\""))) {
			 return expression.substring(1, expression.length() - 1);
		 }

		 // 数字字面量
		 try {
			 if (expression.contains(".")) {
				 return Double.parseDouble(expression);
			 } else {
				 return Long.parseLong(expression);
			 }
		 } catch (NumberFormatException e) {
			 // 不是数字
		 }

		 // 布尔字面量
		 if ("true".equalsIgnoreCase(expression) || "false".equalsIgnoreCase(expression)) {
			 return Boolean.parseBoolean(expression);
		 }

		 // NULL字面量
		 if ("null".equalsIgnoreCase(expression)) {
			 return null;
		 }

		 // 字段名 - 支持大小写不敏感的字段名匹配
		 Object value = data.get(expression);
		 if (value != null) {
			 return value;
		 }

		 // 尝试大小写不敏感匹配
		 for (Map.Entry<String, Object> entry : data.entrySet()) {
			 if (entry.getKey() != null && entry.getKey().equalsIgnoreCase(expression)) {
				 return entry.getValue();
			 }
		 }

		 // 字段名未找到时记录日志
		 appendLog(String.format("节点 【%s】 字段未找到：%s，可用字段：%s",
				 pipelineNode.getProperties().getName(), expression, data.keySet().toString()));
		 return null;
	 }

	 /**
	  * 比较两个值
	  */
	 private boolean compareValues(Object left, Object right, String operator) {
		 if (left == null && right == null) {
			 return "=".equals(operator);
		 }
		 if (left == null || right == null) {
			 return "!=".equals(operator) || "<>".equals(operator);
		 }

		 int comparison = compareObjects(left, right);

		 switch (operator) {
			 case "=":
				 return comparison == 0;
			 case "!=":
			 case "<>":
				 return comparison != 0;
			 case ">":
				 return comparison > 0;
			 case ">=":
				 return comparison >= 0;
			 case "<":
				 return comparison < 0;
			 case "<=":
				 return comparison <= 0;
			 default:
				 return false;
		 }
	 }

	 /**
	  * 不区分大小写包含检查
	  */
	 private boolean containsIgnoreCase(String text, String substring) {
		 return text.toUpperCase().contains(substring.toUpperCase());
	 }

	 /**
	  * 不区分大小写分割（考虑括号层级）
	  */
	 private String[] splitIgnoreCase(String text, String delimiter) {
		 List<String> parts = new ArrayList<>();
		 int parenthesesLevel = 0;
		 int lastSplitIndex = 0;
		 String upperText = text.toUpperCase();
		 String upperDelimiter = delimiter.toUpperCase();

		 for (int i = 0; i <= text.length() - delimiter.length(); i++) {
			 char c = text.charAt(i);
			 if (c == '(') {
				 parenthesesLevel++;
			 } else if (c == ')') {
				 parenthesesLevel--;
			 } else if (parenthesesLevel == 0) {
				 // 只有在括号层级为0时才考虑分割
				 if (upperText.substring(i).startsWith(upperDelimiter)) {
					 // 直接分割，不做边界检查（因为我们的表达式格式是规范的）
					 parts.add(text.substring(lastSplitIndex, i));
					 lastSplitIndex = i + delimiter.length();
					 i += delimiter.length() - 1; // -1 because the loop will increment
				 }
			 }
		 }

		 // 添加最后一部分
		 if (lastSplitIndex < text.length()) {
			 parts.add(text.substring(lastSplitIndex));
		 }

		 // 如果没有找到分隔符，返回原字符串
		 if (parts.isEmpty()) {
			 parts.add(text);
		 }

		 return parts.toArray(new String[0]);
	 }

	 /**
	  * 比较两个对象
	  */
	 private int compareObjects(Object obj1, Object obj2) {
		 if (obj1 == null && obj2 == null) {
			 return 0;
		 }
		 if (obj1 == null) {
			 return -1;
		 }
		 if (obj2 == null) {
			 return 1;
		 }

		 // 如果两个都是数字，按数字比较
		 if (obj1 instanceof Number && obj2 instanceof Number) {
			 BigDecimal bd1 = new BigDecimal(obj1.toString());
			 BigDecimal bd2 = new BigDecimal(obj2.toString());
			 return bd1.compareTo(bd2);
		 }

		 // 如果两个都是布尔值，按布尔值比较
		 if (obj1 instanceof Boolean && obj2 instanceof Boolean) {
			 return ((Boolean) obj1).compareTo((Boolean) obj2);
		 }

		 // 如果两个都是日期，按日期比较
		 if (obj1 instanceof Date && obj2 instanceof Date) {
			 return ((Date) obj1).compareTo((Date) obj2);
		 }

		 // 其他情况按字符串比较
		 return obj1.toString().compareTo(obj2.toString());
	 }

	 /**
	  * 记录被过滤的数据
	  */
	 private void logFilteredData(Map<String, Object> data) {
		 filteredDataLog.offer(data);
		 // 保持日志队列大小不超过1000
		 if (filteredDataLog.size() > 1000) {
			 filteredDataLog.poll();
		 }
	 }

	 /**
	  * 处理处理错误
	  */
	 private void handleProcessingError(Map<String, Object> data, Exception e) {
		 String errorMsg = String.format("数据处理错误：%s，数据：%s", e.getMessage(), data.toString());

		 switch (dataFilterNode.getErrorHandling()) {
			 case "STOP":
				 throw new RuntimeException(errorMsg);
			 case "LOG":
				 appendLog(String.format("节点 【%s】 %s", pipelineNode.getProperties().getName(), errorMsg));
				 break;
			 case "SKIP":
			 default:
				 // 跳过错误数据，继续处理
				 break;
		 }
	 }

	 /**
	  * 处理剩余数据
	  */
	 private void processRemainingData() {
		 // 处理缓冲区中的剩余数据
		 if (!dataBuffer.isEmpty()) {
			 appendLog(String.format("节点 【%s】 处理缓冲区剩余数据：%d 条", pipelineNode.getProperties().getName(), dataBuffer.size()));
			 processBatchFiltering();
		 }

		 // 处理队列中的剩余数据
		 long queueSize = memoryManager.getQueueSize(sourceDataKey);
		 if (queueSize > 0) {
			 appendLog(String.format("节点 【%s】 处理队列剩余数据：%d 条", pipelineNode.getProperties().getName(), queueSize));

			 while (true) {
				 Map<String, Object> data = memoryManager.rightPop(sourceDataKey);
				 if (data == null) {
					 break;
				 }

				 try {
					 boolean passFilter = applyFilter(data);
					 totalCount++;

					 if (passFilter) {
						 pushDataToDownstream(data);
					 } else {
						 filteredCount++;
						 if (Boolean.TRUE.equals(dataFilterNode.getLogFilteredData())) {
							 logFilteredData(data);
						 }
					 }
				 } catch (Exception e) {
					 errorCount++;
					 handleProcessingError(data, e);
				 }
			 }
		 }

		 appendLog(String.format("节点 【%s】 剩余数据处理完成", pipelineNode.getProperties().getName()));
	 }

	 /**
	  * 比较值
	  */
	 private int compareValues(Object fieldValue, String compareValue, String dataType) {
		 // 增加空值检查
		 if (fieldValue == null && compareValue == null) {
			 return 0;
		 }
		 if (fieldValue == null) {
			 return -1;
		 }
		 if (compareValue == null) {
			 return 1;
		 }

		 try {
			 switch (dataType) {
				 case "NUMBER":
					 BigDecimal fieldNum = new BigDecimal(fieldValue.toString());
					 BigDecimal compareNum = new BigDecimal(compareValue);
					 return fieldNum.compareTo(compareNum);
				 case "DATE":
					 Date fieldDate = parseDate(fieldValue.toString());
					 Date compareDate = parseDate(compareValue);
					 return fieldDate.compareTo(compareDate);
				 case "BOOLEAN":
					 Boolean fieldBool = Boolean.parseBoolean(fieldValue.toString());
					 Boolean compareBool = Boolean.parseBoolean(compareValue);
					 return fieldBool.compareTo(compareBool);
				 default: // STRING
					 return fieldValue.toString().compareTo(compareValue);
			 }
		 } catch (Exception e) {
			 // 如果转换失败，按字符串比较
			 return fieldValue.toString().compareTo(compareValue);
		 }
	 }

	 /**
	  * 检查值是否在列表中
	  */
	 private boolean isValueInList(Object fieldValue, String listValue, String dataType) {
		 // 增加空值和空字符串检查
		 if (listValue == null || listValue.trim().isEmpty()) {
			 return false;
		 }

		 // 处理空值情况
		 if (fieldValue == null) {
			 // 检查列表中是否包含null或空值表示
			 String[] values = listValue.split(",");
			 for (String value : values) {
				 String trimmedValue = value.trim();
				 if (trimmedValue.isEmpty() || "null".equalsIgnoreCase(trimmedValue)) {
					 return true;
				 }
			 }
			 return false;
		 }

		 try {
			 String[] values = listValue.split(",");
			 for (String value : values) {
				 String trimmedValue = value.trim();
				 if (trimmedValue.isEmpty()) {
					 continue; // 跳过空值
				 }
				 if (compareValues(fieldValue, trimmedValue, dataType) == 0) {
					 return true;
				 }
			 }
		 } catch (Exception e) {
			 // 记录异常但不中断处理
			 System.err.println("检查值是否在列表中时发生异常: " + e.getMessage());
		 }
		 return false;
	 }

	 /**
	  * 检查值是否在范围内
	  */
	 private boolean isValueBetween(Object fieldValue, String minValue, String maxValue, String dataType) {
		 // 增加空值检查
		 if (fieldValue == null || minValue == null || maxValue == null) {
			 return false;
		 }

		 try {
			 return compareValues(fieldValue, minValue, dataType) >= 0 &&
					 compareValues(fieldValue, maxValue, dataType) <= 0;
		 } catch (Exception e) {
			 // 记录异常并返回false
			 System.err.println("检查值是否在范围内时发生异常: " + e.getMessage());
			 return false;
		 }
	 }

	 /**
	  * 验证数据类型
	  */
	 private boolean validateDataType(Object value, String expectedType) {
		 // 增加空值检查
		 if (value == null) {
			 return true; // null值对所有类型都有效
		 }

		 try {
			 switch (expectedType) {
				 case "NUMBER":
					 new BigDecimal(value.toString());
					 return true;
				 case "DATE":
					 parseDate(value.toString());
					 return true;
				 case "BOOLEAN":
					 String strValue = value.toString().toLowerCase();
					 return "true".equals(strValue) || "false".equals(strValue) ||
							"1".equals(strValue) || "0".equals(strValue) ||
							"yes".equals(strValue) || "no".equals(strValue);
				 default: // STRING
					 return true;
			 }
		 } catch (Exception e) {
			 return false;
		 }
	 }

	 /**
	  * 解析日期
	  */
	 private Date parseDate(String dateStr) throws ParseException {
		 // 增加空值检查
		 if (dateStr == null || dateStr.trim().isEmpty()) {
			 throw new ParseException("日期字符串为空", 0);
		 }

		 String trimmedDateStr = dateStr.trim();

		 for (SimpleDateFormat formatter : dateFormatters) {
			 try {
				 // 确保线程安全
				 synchronized (formatter) {
					 return formatter.parse(trimmedDateStr);
				 }
			 } catch (ParseException e) {
				 // 继续尝试下一个格式
			 }
		 }
		 throw new ParseException("无法解析日期: " + dateStr, 0);
	 }

	 /**
	  * 判断是否为空值操作符
	  */
	 private boolean isNullOperator(String operator) {
		 return "IS_NULL".equals(operator) || "IS_NOT_NULL".equals(operator);
	 }

	 /**
	  * 判断是否为范围操作符
	  */
	 private boolean isBetweenOperator(String operator) {
		 return "BETWEEN".equals(operator);
	 }
 }
