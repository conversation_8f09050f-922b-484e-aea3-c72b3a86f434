package com.bjgy.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据开发-etl流程
 */
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("data_prod_pipeline")
public class DataProdPipelineEntity extends BaseEntity {

	/**
	 * 名称
	 */
	private String name;
	/**
	 * 描述
	 */
	private String description;

	/**
	 * 任务类型 2-一次性 3-周期性
	 */
	private Integer taskType;

	/**
	 * cron表达式
	 */
	private String cron;
	private String edges;

	/**
	 * 0-未发布 1-已发布
	 */
	private Integer status;

	/**
	 * 发布时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date releaseTime;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer releaseUserId;

	/**
	 * 1-等待中 2-运行中 3-正常 4-异常
	 */
	private Integer runStatus;

	/**
	 * 最近开始时间
	 */
	private Date startTime;

	/**
	 * 最近结束时间
	 */
	private Date endTime;

	/**
	 * 项目id
	 */
	private Long projectId;

	/**
	 * 机构id
	 */
	private Long orgId;


}
