package com.bjgy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据生产-etl节点记录
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_prod_pipeline_node_record")
public class DataProdPipelineNodeRecordEntity extends BaseEntity {

	/**
	* 流程id
	*/
	private Long pipelineId;

	/**
	* 节点id
	*/
	private Long pipelineNodeId;

	/**
	* 节点no
	*/
	private String pipelineNodeNo;

	/**
	* 记录id
	*/
	private Long pipelineRecordId;

	/**
	* 工具箱id
	*/
	private Long toolCategoryId;

	/**
	* 机构id
	*/
	private Long orgId;

	/**
	* 项目（租户）id
	*/
	private Long projectId;

	/**
	* 当前状态 字典 run_status
	*/
	private Integer runStatus;

	/**
	* 开始时间
	*/
	private Date startTime;

	/**
	* 结束时间
	*/
	private Date endTime;

	/**
	* 运行日志
	*/
	private String log;

	/**
	* 1-手动 2-调度
	*/
	private Integer executeType;


	private Integer deleted;



}
