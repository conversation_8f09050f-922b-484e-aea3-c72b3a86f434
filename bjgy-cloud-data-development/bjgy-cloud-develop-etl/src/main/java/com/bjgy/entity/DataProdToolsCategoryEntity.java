package com.bjgy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.mybatis.entity.BaseEntity;

/**
 * 数据开发-工具类别
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_prod_tools_category")
public class DataProdToolsCategoryEntity extends BaseEntity {

	/**
	* 名称
	*/
	private String name;
	private Integer type;
	private String path;
	private Integer builtIn;
	private String icon;

	/**
	* 父级ID
	*/
	private Long pid;

	/**
	* 顺序
	*/
	private Integer orderNo;

	private Long projectId;
	private Long orgId;






}
