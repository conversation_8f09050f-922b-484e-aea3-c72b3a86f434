package com.bjgy.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据生产-生产流程节点
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_prod_pipeline_node")
public class DataProdPipelineNodeEntity extends BaseEntity {

	/**
	* 关联的流程id
	*/
	private Long pipelineId;

	/**
	* 机构id
	*/
	private Long orgId;

	/**
	* 项目（租户）id
	*/
	private Long projectId;

	/**
	* 节点编号
	*/
	private String no;

	/**
	 * 节依赖点编号
	 */
	private String dependNo;

	/**
	* 执行顺序
	*/
	private Integer sort;

	/**
	* 节点名称
	*/
	private String name;
	/**
	 * 节点icon
	 */
	private String icon;

	/**
	* 节点类型
	*/
	private String type;
	/**
	 * 1-读取 2-转换 3-写入
	 */
	private Integer toolType;

	/**
	* 节点json数据
	*/
	private String nodeJson;

	/**
	* 横坐标
	*/
	private Integer x;

	/**
	* 纵坐标
	*/
	private Integer y;

	/**
	* 节点描述
	*/
	private String note;

	/**
	* 关联的工具目录id
	*/
	private Long toolCategoryId;

	/**
	* 遇错是否继续（0-否 1-是）
	*/
	private Integer failGoOn;

	/**
	* 节点权重
	*/
	private Integer weight;

	/**
	 * 超时时长
	 */
	private Integer overTimes;





}
