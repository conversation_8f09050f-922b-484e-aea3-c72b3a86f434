package com.bjgy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bjgy.framework.mybatis.entity.BaseEntity;

import java.util.Date;

/**
 * 数据生产-etl流程记录
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("data_prod_pipeline_record")
public class DataProdPipelineRecordEntity extends BaseEntity {

	/**
	* 调度名称
	*/
	private String name;

	/**
	* 流程id
	*/
	private Long pipelineId;

	/**
	* 机构id
	*/
	private Long orgId;

	/**
	* 项目（租户）id
	*/
	private Long projectId;

	/**
	* 当前状态 字典 run_status
	*/
	private Integer runStatus;

	/**
	* 开始时间
	*/
	private Date startTime;

	/**
	* 结束时间
	*/
	private Date endTime;

	/**
	* 运行日志
	*/
	private String log;

	/**
	* 1-手动 2-调度
	*/
	private Integer executeType;

	/**
	* 历史配置json
	*/
	private String configJson;


	private Integer deleted;





}
