package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdToolsCategoryConvert;
import com.bjgy.entity.DataProdToolsCategoryEntity;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataProdToolsCategoryService;
import com.bjgy.vo.DataProdToolsCategoryVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据开发-工具类别
 */
@RestController
@RequestMapping("/toolsCategory")
@Tag(name = "数据开发-工具类别")
@AllArgsConstructor
public class DataProdToolsCategoryController {
	private final DataProdToolsCategoryService dataProdToolsCategoryService;

	@GetMapping("/list")
	@Operation(summary = "树状列表查询")
	public Result<List<DataProdToolsCategoryVO>> listTree(Integer folder) {
		return Result.ok(dataProdToolsCategoryService.listTree(folder));
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataProdToolsCategoryVO> get(@PathVariable("id") Long id) {
		DataProdToolsCategoryEntity entity = dataProdToolsCategoryService.getById(id);

		return Result.ok(DataProdToolsCategoryConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataProdToolsCategoryVO vo) {
		dataProdToolsCategoryService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataProdToolsCategoryVO vo) {
		dataProdToolsCategoryService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataProdToolsCategoryService.delete(idList);

		return Result.ok();
	}
}
