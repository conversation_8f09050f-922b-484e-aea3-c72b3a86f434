package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdPipelineNodeRecordConvert;
import com.bjgy.entity.DataProdPipelineNodeRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataProdPipelineNodeRecordQuery;
import com.bjgy.service.DataProdPipelineNodeRecordService;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据生产-etl节点记录
 */
@RestController
@RequestMapping("/pipelineNodeRecord")
@Tag(name = "数据生产-etl节点记录")
@AllArgsConstructor
public class DataProdPipelineNodeRecordController {
	private final DataProdPipelineNodeRecordService dataProdPipelineNodeRecordService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataProdPipelineNodeRecordVO>> page(@Valid DataProdPipelineNodeRecordQuery query) {
		PageResult<DataProdPipelineNodeRecordVO> page = dataProdPipelineNodeRecordService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataProdPipelineNodeRecordVO> get(@PathVariable("id") Long id) {
		DataProdPipelineNodeRecordEntity entity = dataProdPipelineNodeRecordService.getById(id);

		return Result.ok(DataProdPipelineNodeRecordConvert.INSTANCE.convert(entity));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<String> save(@RequestBody DataProdPipelineNodeRecordVO vo) {
		dataProdPipelineNodeRecordService.save(vo);

		return Result.ok();
	}

	@PutMapping
	@Operation(summary = "修改")
	public Result<String> update(@RequestBody @Valid DataProdPipelineNodeRecordVO vo) {
		dataProdPipelineNodeRecordService.update(vo);

		return Result.ok();
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataProdPipelineNodeRecordService.delete(idList);

		return Result.ok();
	}
}
