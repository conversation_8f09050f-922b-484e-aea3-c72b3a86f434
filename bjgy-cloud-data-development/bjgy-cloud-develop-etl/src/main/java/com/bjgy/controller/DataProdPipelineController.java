package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.dto.ConsoleLog;
import com.bjgy.dto.Pipeline;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataProdPipelineQuery;
import com.bjgy.service.DataProdPipelineService;
import com.bjgy.vo.DataProdPipelineNodeRecordVO;
import com.bjgy.vo.DataProdPipelineVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据开发-etl流程
 */
@RestController
@RequestMapping("/prodPipeline")
@Tag(name = "数据开发-etl流程")
@AllArgsConstructor
public class DataProdPipelineController {
	private final DataProdPipelineService dataProdPipelineService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataProdPipelineVO>> page(@Valid DataProdPipelineQuery query) {
		PageResult<DataProdPipelineVO> page = dataProdPipelineService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<Pipeline> get(@PathVariable("id") Long id) {
		return Result.ok(dataProdPipelineService.get(id));
	}

	@PostMapping
	@Operation(summary = "保存")
	public Result<Long> save(@RequestBody Pipeline pipeline) {
		return Result.ok(dataProdPipelineService.save(pipeline));
	}

	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataProdPipelineService.delete(idList);
		return Result.ok();
	}

	@PostMapping("/run/{id}")
	@Operation(summary = "执行流程")
	public Result<Long> run(@PathVariable Long id) {
		return Result.ok(dataProdPipelineService.run(id));
	}

	@GetMapping("/log/{recordId}")
	@Operation(summary = "获取流程图执行的日志")
	public Result<ConsoleLog> getLog(@PathVariable Integer recordId) {
		return Result.ok(dataProdPipelineService.getLog(recordId));
	}

	@GetMapping("/node-info/{recordId}")
	@Operation(summary = "根据调度记录id获取节点调度记录")
	public Result<List<DataProdPipelineNodeRecordVO>> listNodeRecord(@PathVariable Integer recordId) {
		return Result.ok(dataProdPipelineService.listNodeRecord(recordId));
	}

	@PostMapping("/release/{id}")
	@Operation(summary = "发布")
	public Result<String> release(@PathVariable Long id) {
		dataProdPipelineService.release(id);
		return Result.ok();
	}

	@PostMapping("/cancle/{id}")
	@Operation(summary = "取消发布")
	public Result<String> cancle(@PathVariable Long id) {
		dataProdPipelineService.cancle(id);
		return Result.ok();
	}

	@PostMapping("/stop/{recordId}")
	@Operation(summary = "停止任务")
	public Result<String> stop(@PathVariable Long recordId) {
		dataProdPipelineService.stop(recordId);
		return Result.ok();
	}
}
