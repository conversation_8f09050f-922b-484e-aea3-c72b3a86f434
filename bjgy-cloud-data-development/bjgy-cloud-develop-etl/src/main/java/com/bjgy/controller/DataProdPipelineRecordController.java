package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import com.bjgy.convert.DataProdPipelineRecordConvert;
import com.bjgy.entity.DataProdPipelineRecordEntity;
import com.bjgy.framework.common.page.PageResult;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.query.DataProdPipelineRecordQuery;
import com.bjgy.service.DataProdPipelineRecordService;
import com.bjgy.vo.DataProdPipelineRecordVO;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据生产-etl流程记录
 */
@RestController
@RequestMapping("/pipelineRecord")
@Tag(name = "数据生产-etl流程记录")
@AllArgsConstructor
public class DataProdPipelineRecordController {
	private final DataProdPipelineRecordService dataProdPipelineRecordService;

	@GetMapping("page")
	@Operation(summary = "分页")
	public Result<PageResult<DataProdPipelineRecordVO>> page(@Valid DataProdPipelineRecordQuery query) {
		PageResult<DataProdPipelineRecordVO> page = dataProdPipelineRecordService.page(query);

		return Result.ok(page);
	}

	@GetMapping("{id}")
	@Operation(summary = "信息")
	public Result<DataProdPipelineRecordVO> get(@PathVariable("id") Long id) {
		DataProdPipelineRecordEntity entity = dataProdPipelineRecordService.getById(id);

		return Result.ok(DataProdPipelineRecordConvert.INSTANCE.convert(entity));
	}


	@DeleteMapping
	@Operation(summary = "删除")
	public Result<String> delete(@RequestBody List<Long> idList) {
		dataProdPipelineRecordService.delete(idList);

		return Result.ok();
	}
}
