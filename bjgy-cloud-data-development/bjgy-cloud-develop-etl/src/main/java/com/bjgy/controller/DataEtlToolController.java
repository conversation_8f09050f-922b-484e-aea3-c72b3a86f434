package com.bjgy.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.bjgy.framework.common.utils.Result;
import com.bjgy.service.DataEtlToolService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据开发-ETL工具
 */
@Slf4j
@RestController
@RequestMapping("/etl-tool")
@Tag(name = "数据开发-ETL工具")
@AllArgsConstructor
public class DataEtlToolController {

    private final DataEtlToolService dataEtlToolService;

    @PostMapping("/kafka/test-connection")
    @Operation(summary = "测试Kafka连接")
    public Result<String> testKafkaConnection(@RequestBody Map<String, Object> params) {
        try {
            String result = dataEtlToolService.testKafkaConnection(params);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("测试Kafka连接失败", e);
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/kafka/list-topics")
    @Operation(summary = "获取Kafka主题列表")
    public Result<List<Map<String, Object>>> listKafkaTopics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> topics = dataEtlToolService.listKafkaTopics(params);
            return Result.ok(topics);
        } catch (Exception e) {
            log.error("获取Kafka主题列表失败", e);
            return Result.error(e.getMessage());
        }
    }
}
