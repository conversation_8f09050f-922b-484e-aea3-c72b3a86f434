# ETL内存管理器配置说明

## 概述
EtlMemoryManager是ETL系统的内存管理组件，提供多层内存保护机制，防止内存溢出，确保系统稳定运行。

## 主要功能

### 1. 进程数量控制
- 限制同时运行的ETL进程数量
- 防止过多进程导致系统资源耗尽

### 2. 动态内存使用监控
- **实时内存计算**：使用智能算法计算每个数据对象的实际内存占用
- **精确内存追踪**：支持多种数据类型的准确内存估算，包括：
  - 基本类型包装类（Integer、Long、Double等）
  - 字符串（String）- 根据实际字符长度计算
  - 大数类型（BigDecimal、BigInteger）
  - 日期时间类型（Date、Timestamp等）
  - 数组和集合类型
  - 自定义对象类型
- **JVM堆内存监控**：实时获取JVM堆内存使用情况
- **双重内存保护**：结合ETL估算内存和JVM堆内存进行综合判断

### 3. 多层内存保护机制

#### 启动保护
- 启动新进程前检查活跃进程数和当前内存使用情况
- 超过限制时拒绝启动新进程

#### 推送保护
- 数据推送前检查内存使用情况
- 内存不足时提供两种处理策略：
  1. **等待策略（默认）**：等待内存释放，超时则抛出异常
  2. **异常策略**：立即抛出异常，快速失败

#### 内存不足处理策略

**等待策略（推荐）**：
- 配置参数：`etl.memory.wait.for.memory=true`（默认）
- 超时配置：`etl.memory.wait.timeout.seconds=300`（默认5分钟）
- 优点：避免数据丢失，保证数据完整性
- 缺点：可能导致处理延迟

**异常策略**：
- 配置参数：`etl.memory.wait.for.memory=false`
- 优点：快速失败，便于问题定位
- 缺点：可能导致数据丢失

### 4. 自动监控和清理
- 后台线程每30秒监控一次内存使用情况
- 自动清理已完成的空队列
- 记录详细的内存使用日志

## 配置参数

通过JVM系统属性进行配置：

- `etl.memory.queue.capacity`：每个节点数据队列的最大容量（默认：5000）
- `etl.memory.max.pipelines`：最大并发进程数（默认：50）
- `etl.memory.max.bytes`：最大内存使用量（默认：512MB）
- `etl.memory.warning.threshold`：内存使用警告阈值（默认：80%）
- `etl.memory.critical.threshold`：内存使用临界阈值（默认：90%）
- `etl.memory.wait.for.memory`：内存不足时是否等待内存释放（默认：true）
- `etl.memory.wait.timeout.seconds`：等待内存释放的超时时间，单位秒（默认：300）

注意：移除了`etl.memory.estimated.row.size`参数，因为现在使用动态计算方式。

## 内存计算方式

### 动态内存计算
系统现在使用智能算法实时计算每个数据对象的内存占用：

#### 支持的数据类型及计算方式：
- **基本类型包装类**：根据实际类型大小计算（如Integer=16字节，Long=24字节）
- **字符串**：40字节对象开销 + 字符长度×2字节
- **BigDecimal**：72字节开销 + 字符串表示的大小
- **BigInteger**：48字节开销 + 位数据大小
- **日期类型**：Date=32字节，Timestamp=40字节
- **数组**：24字节对象开销 + 实际数据大小
- **集合**：48字节基本开销 + 所有元素大小之和
- **Map对象**：48字节基本开销 + 所有键值对大小 + Entry开销

#### 计算优势：
- **准确性高**：根据实际数据内容计算，避免固定估算的偏差
- **自适应**：自动适应不同数据结构和大小
- **实时性**：每次数据操作都进行实时计算
- **全面性**：覆盖ETL场景中常见的所有数据类型

## 性能调优建议

### 1. 内存参数调优
```bash
# 设置最大内存使用量（根据可用内存调整）
-Detl.memory.max.bytes=1073741824  # 1GB

# 调整并发进程数（根据CPU核数调整）
-Detl.memory.max.pipelines=100

# 设置队列容量（根据数据量调整）
-Detl.memory.queue.capacity=10000
```

### 2. 内存阈值调优
```bash
# 降低警告阈值，提前预警
-Detl.memory.warning.threshold=0.7  # 70%

# 设置合适的临界阈值
-Detl.memory.critical.threshold=0.85  # 85%
```

### 3. 等待策略调优
```bash
# 启用等待策略（推荐）
-Detl.memory.wait.for.memory=true

# 根据业务需求调整超时时间
-Detl.memory.wait.timeout.seconds=600  # 10分钟
```

## 监控和告警

### 日志监控
系统会输出详细的内存使用日志：
- 内存使用率告警
- 数据推送/弹出的实际大小
- 进程启动/停止的内存变化
- 队列清理情况

### 关键指标
- 堆内存使用率
- ETL估算内存使用量
- 活跃流程数量
- 数据队列数量
- 实际数据大小统计

## 故障排除

### 常见问题

1. **内存使用率持续升高**
   - 检查是否有内存泄漏
   - 调整最大内存限制
   - 减少并发进程数

2. **频繁的内存不足告警**
   - 增加最大内存限制
   - 优化数据结构，减少内存占用
   - 调整队列容量

3. **处理延迟增加**
   - 检查是否频繁触发等待策略
   - 调整内存阈值
   - 增加系统内存

4. **数据丢失**
   - 确保使用等待策略而非异常策略
   - 检查超时时间设置是否合理
   - 监控内存释放情况

### 调试建议
- 启用DEBUG日志级别查看详细的内存计算信息
- 监控JVM堆内存使用情况
- 定期检查内存使用趋势
- 根据实际数据特征调整配置参数 